// package.json
{
  "name": "buddhist-healing-backend",
  "version": "1.0.0",
  "description": "佛教身心療癒網站後端系統",
  "main": "src/app.js",
  "scripts": {
    "start": "node src/app.js",
    "dev": "nodemon src/app.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint src/**/*.js",
    "lint:fix": "eslint src/**/*.js --fix"
  },
  "keywords": [
    "buddhist",
    "healing",
    "meditation",
    "education",
    "api"
  ],
  "author": "Your Name",
  "license": "MIT",
  "dependencies": {
    "express": "^4.18.2",
    "mongoose": "^7.5.0",
    "jsonwebtoken": "^9.0.2",
    "bcryptjs": "^2.4.3",
    "express-rate-limit": "^6.10.0",
    "express-validator": "^7.0.1",
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "morgan": "^1.10.0",
    "dotenv": "^16.3.1",
    "redis": "^4.6.8",
    "socket.io": "^4.7.2",
    "nodemailer": "^6.9.4",
    "stripe": "^12.18.0",
    "multer": "^1.4.5-lts.1",
    "compression": "^1.7.4",
    "joi": "^17.9.2"
  },
  "devDependencies": {
    "nodemon": "^3.0.1",
    "jest": "^29.6.4",
    "supertest": "^6.3.3",
    "eslint": "^8.47.0",
    "eslint-config-standard": "^17.1.0",
    "eslint-plugin-import": "^2.28.1",
    "eslint-plugin-node": "^11.1.0",
    "eslint-plugin-promise": "^6.1.1"
  },
  "engines": {
    "node": ">=16.0.0"
  }
}

// ========== .env 範例 ==========
// .env.example
NODE_ENV=development
PORT=3000

# 資料庫配置
MONGODB_URI=mongodb://localhost:27017/buddhist_healing
REDIS_URL=redis://localhost:6379

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# 郵件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>

# 支付配置
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# 雲端儲存配置
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=ap-northeast-1
AWS_S3_BUCKET=buddhist-healing-media

# 搜尋引擎配置
ELASTICSEARCH_URL=http://localhost:9200

# 分析配置
GOOGLE_ANALYTICS_ID=GA-MEASUREMENT-ID

# ========== Docker 配置 ==========
// Dockerfile
FROM node:18-alpine

WORKDIR /app

# 複製 package 文件
COPY package*.json ./

# 安裝依賴
RUN npm ci --only=production

# 複製應用程式代碼
COPY . .

# 創建非 root 用戶
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# 設定權限
RUN chown -R nodejs:nodejs /app
USER nodejs

# 暴露端口
EXPOSE 3000

# 健康檢查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# 啟動應用
CMD ["npm", "start"]

# ========== Docker Compose ==========
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/buddhist_healing
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
    depends_on:
      - mongo
      - redis
      - elasticsearch
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - app-network

  elasticsearch:
    image: elasticsearch:8.9.0
    ports:
      - "9200:9200"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    restart: unless-stopped
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - app-network

volumes:
  mongo_data:
  redis_data:
  elasticsearch_data:

networks:
  app-network:
    driver: bridge

# ========== Nginx 配置 ==========
# nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream app {
        server app:3000;
    }

    # 速率限制
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/m;

    server {
        listen 80;
        server_name localhost;

        # 重定向到 HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name localhost;

        # SSL 配置
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;

        # 安全頭部
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;

        # API 路由
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 認證路由特殊限制
        location /api/auth/ {
            limit_req zone=auth burst=10 nodelay;
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # WebSocket 支援
        location /socket.io/ {
            proxy_pass http://app;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 靜態文件
        location /static/ {
            alias /app/public/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # 健康檢查
        location /health {
            proxy_pass http://app;
            access_log off;
        }
    }
}

# ========== MongoDB 初始化腳本 ==========
// mongo-init.js
db = db.getSiblingDB('buddhist_healing');

// 創建用戶
db.createUser({
  user: 'app_user',
  pwd: 'secure_password',
  roles: [
    {
      role: 'readWrite',
      db: 'buddhist_healing'
    }
  ]
});

// 創建索引
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ "membership.plan": 1 });
db.users.createIndex({ "progress.lastActiveDate": 1 });

db.courses.createIndex({ category: 1, difficulty: 1 });
db.courses.createIndex({ status: 1 });
db.courses.createIndex({ "statistics.enrollments": -1 });
db.courses.createIndex({ tags: 1 });

db.scriptures.createIndex({ category: 1, difficulty: 1 });
db.scriptures.createIndex({ tags: 1 });
db.scriptures.createIndex({ popularity: -1 });
db.scriptures.createIndex({ 
  title: "text", 
  "content.text": "text", 
  "content.translation": "text" 
});

db.workshops.createIndex({ "schedule.startDate": 1, status: 1 });
db.workshops.createIndex({ type: 1 });

// 插入基礎資料
db.scriptures.insertMany([
  {
    title: "心經",
    originalTitle: "般若波羅蜜多心經",
    category: "般若部",
    subcategory: "心經",
    content: {
      text: "觀自在菩薩，行深般若波羅蜜多時，照見五蘊皆空，度一切苦厄...",
      translation: "觀世音菩薩在深入修行般若波羅蜜多時，照見五蘊皆空，度脫一切苦厄...",
      commentary: "此經為般若經之精要，闡述空性智慧..."
    },
    metadata: {
      translator: "玄奘",
      language: "zh-TW",
      period: "唐代"
    },
    healing: {
      applications: ["焦慮症", "憂鬱症", "失眠"],
      techniques: ["念誦", "抄寫", "禪修"],
      benefits: ["內心平靜", "智慧增長", "煩惱減輕"]
    },
    tags: ["般若", "空性", "智慧", "心經"],
    searchKeywords: ["觀自在", "般若波羅蜜", "五蘊皆空"],
    difficulty: "beginner",
    readingTime: 5,
    popularity: 1000,
    created: new Date(),
    updated: new Date()
  },
  {
    title: "藥師經",
    originalTitle: "藥師琉璃光如來本願功德經",
    category: "大乘經典",
    subcategory: "藥師部",
    content: {
      text: "如是我聞，一時薄伽梵，遊化諸國，至廣嚴城，住樂音樹下...",
      translation: "我是這樣聽聞的，一時，世尊在各國遊化，到了廣嚴城，住在樂音樹下...",
      commentary: "此經詳述藥師如來之十二大願，專治眾生身心諸病..."
    },
    metadata: {
      translator: "玄奘",
      language: "zh-TW",
      period: "唐代"
    },
    healing: {
      applications: ["身體疾病", "心理創傷", "業障病"],
      techniques: ["念佛", "持咒", "供養"],
      benefits: ["身體健康", "心靈療癒", "業障消除"]
    },
    tags: ["藥師佛", "療癒", "十二大願", "琉璃光"],
    searchKeywords: ["藥師琉璃光", "十二大願", "消災延壽"],
    difficulty: "intermediate",
    readingTime: 30,
    popularity: 800,
    created: new Date(),
    updated: new Date()
  }
]);

print("Database initialized successfully!");

# ========== 測試配置 ==========
// jest.config.js
module.exports = {
  testEnvironment: 'node',
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/app.js',
    '!**/node_modules/**',
    '!**/tests/**'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  testMatch: [
    '**/tests/**/*.test.js',
    '**/tests/**/*.spec.js'
  ],
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  testTimeout: 10000
};

// tests/setup.js
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

let mongoServer;

// 測試前設置
beforeAll(async () => {
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();
  
  await mongoose.connect(mongoUri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });
});

// 每個測試後清理
afterEach(async () => {
  const collections = mongoose.connection.collections;
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
});

// 測試後清理
afterAll(async () => {
  await mongoose.connection.dropDatabase();
  await mongoose.connection.close();
  await mongoServer.stop();
});

# ========== ESLint 配置 ==========
// .eslintrc.js
module.exports = {
  env: {
    browser: true,
    commonjs: true,
    es2021: true,
    node: true,
    jest: true
  },
  extends: [
    'standard'
  ],
  parserOptions: {
    ecmaVersion: 12
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
    'semi': ['error', 'always'],
    'quotes': ['error', 'single'],
    'no-trailing-spaces': 'error',
    'eol-last': 'error',
    'no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    'prefer-const': 'error'
  }
};

# ========== GitHub Actions CI/CD ==========
// .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [16.x, 18.x]
        mongodb-version: [5.0, 6.0]

    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Start MongoDB
      uses: supercharge/mongodb-github-action@1.8.0
      with:
        mongodb-version: ${{ matrix.mongodb-version }}
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linter
      run: npm run lint
    
    - name: Run tests
      run: npm run test:coverage
      env:
        NODE_ENV: test
        JWT_SECRET: test-secret
        MONGODB_URI: mongodb://localhost:27017/test
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella

  security:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Run security audit
      run: npm audit --audit-level high
    
    - name: Run Snyk security scan
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}

  deploy:
    if: github.ref == 'refs/heads/main'
    needs: [test, security]
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ap-northeast-1
    
    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1
    
    - name: Build, tag, and push image to Amazon ECR
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: buddhist-healing-api
        IMAGE_TAG: ${{ github.sha }}
      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        docker tag $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:latest
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest
    
    - name: Deploy to ECS
      run: |
        aws ecs update-service --cluster production --service api --force-new-deployment

# ========== 部署腳本 ==========
// scripts/deploy.sh
#!/bin/bash

# 部署腳本
set -e

echo "🚀 Starting deployment..."

# 環境變數檢查
if [ -z "$NODE_ENV" ]; then
    echo "❌ NODE_ENV is not set"
    exit 1
fi

# 安裝依賴
echo "📦 Installing dependencies..."
npm ci --only=production

# 運行資料庫遷移
echo "🗄️ Running database migrations..."
npm run migrate

# 建立生產資料
echo "🌱 Seeding production data..."
npm run seed:prod

# 啟動應用程式
echo "🎉 Starting application..."
pm2 start ecosystem.config.js --env $NODE_ENV

echo "✅ Deployment completed successfully!"

// ecosystem.config.js (PM2 配置)
module.exports = {
  apps: [{
    name: 'buddhist-healing-api',
    script: 'src/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};

# ========== 監控配置 ==========
// monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'buddhist-healing-api'
    static_configs:
      - targets: ['localhost:3000']
    metrics_path: /metrics
    scrape_interval: 5s

# ========== 備份腳本 ==========
// scripts/backup.sh
#!/bin/bash

# 資料庫備份腳本
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"
DB_NAME="buddhist_healing"

echo "📂 Starting backup at $DATE..."

# 創建備份目錄
mkdir -p $BACKUP_DIR

# MongoDB 備份
mongodump --host localhost:27017 --db $DB_NAME --out $BACKUP_DIR/mongodb_$DATE

# 壓縮備份
tar -czf $BACKUP_DIR/backup_$DATE.tar.gz $BACKUP_DIR/mongodb_$DATE

# 清理舊備份 (保留30天)
find $BACKUP_DIR -name "backup_*.tar.gz" -mtime +30 -delete

# 上傳到 S3
aws s3 cp $BACKUP_DIR/backup_$DATE.tar.gz s3://buddhist-healing-backups/

echo "✅ Backup completed: backup_$DATE.tar.gz"network

  mongo:
    image: mongo:6.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123
      - MONGO_INITDB_DATABASE=buddhist_healing
    volumes:
      - mongo_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    restart: unless-stopped
    networks:
      - app-network

  redis:
    image: redis:7.0-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - app-