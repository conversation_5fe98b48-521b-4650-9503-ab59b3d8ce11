//@version=5
strategy("NVDA Advanced Strategy with Alerts", overlay=true, default_qty_type=strategy.percent_of_equity, default_qty_value=10, 
     initial_capital=100000, currency=currency.USD, calc_on_order_fills=true, calc_on_every_tick=true)

// === Input Settings ===
startDate = input.time(timestamp("2024-01-01T00:00:00"), "回測開始日期")
endDate = input.time(timestamp("2025-07-01T00:00:00"), "回測結束日期")

// 限定回測期間條件
inDateRange = (time >= startDate and time <= endDate)

// === Strategy Inputs ===
atrPeriod = input.int(10, "SuperTrend ATR Period")
factor = input.float(3.0, "SuperTrend Multiplier")
smaLength = input.int(20, "SMA Period")
emaLength = input.int(50, "EMA Period")
rsiLength = input.int(14, "RSI Length")
macdFast = input.int(12, "MACD Fast")
macdSlow = input.int(26, "MACD Slow")
macdSignal = input.int(9, "MACD Signal")
entryTolerance = input.float(0.03, "Entry Price ±% SMA", step=0.01)
trailingPerc = input.float(5.0, "Trailing Stop %", step=0.1)
fibRange = input.int(20, "High/Low Lookback for TP")

// === Indicators ===
[supertrend, direction] = ta.supertrend(factor, atrPeriod)
sma = ta.sma(close, smaLength)
ema = ta.ema(close, emaLength)
rsi = ta.rsi(close, rsiLength)
[macdLine, signalLine, _] = ta.macd(close, macdFast, macdSlow, macdSignal)
macdCrossDown = ta.crossunder(macdLine, signalLine)

// === FIB EXTENSION ===
fibHigh = ta.highest(high, fibRange)
fibLow = ta.lowest(low, fibRange)
fibRangeSize = fibHigh - fibLow
fibTarget1 = fibHigh + fibRangeSize * 0.272
fibTarget2 = fibHigh + fibRangeSize * 0.618

// === Entry Conditions ===
inSmaRange = math.abs(close - sma) <= entryTolerance * sma
supertrendUp = direction == 1
entryCond = supertrendUp and rsi > 50 and inSmaRange and close > ema and inDateRange

// === Exit Conditions ===
exitSupertrend = direction == -1
exitRSI = rsi < 45
exitMACD = close < sma and macdCrossDown
exitFib = close >= fibTarget2
exitCond = exitSupertrend or exitRSI or exitMACD or exitFib

// === TRAILING STOP ===
strategy.exit("Exit TS", from_entry="Buy", trail_price=close, trail_offset=close * trailingPerc / 100)

// === EXECUTE ===
if entryCond and strategy.position_size == 0
    strategy.entry("Buy", strategy.long)
    alert("[BUY] NVDA 開個信號 - Price: " + str.tostring(close, format.mintick), alert.freq_once_per_bar)

if exitCond
    strategy.close("Buy")
    alert("[SELL] NVDA 出場信號 - Price: " + str.tostring(close, format.mintick), alert.freq_once_per_bar)

// === Alerts Setup for External Integration ===
// You can connect this to external services (like Telegram/LINE) using webhook integration
// TradingView > Create Alert > Condition: "Any Alert() function call"
// Paste your webhook URL in alert box, e.g. from Telegram Bot or LINE Notify
