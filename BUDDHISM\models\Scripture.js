const mongoose = require('mongoose');

const scriptureSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 300
  },
  originalTitle: {
    type: String,
    trim: true,
    maxlength: 300
  },
  category: {
    type: String,
    required: true,
    enum: [
      '大乘經典', '小乘經典', '金剛乘經典', '禪宗經典', 
      '淨土經典', '華嚴經典', '法華經典', '般若經典',
      '戒律經典', '論典', '祖師語錄', '現代開示'
    ]
  },
  subcategory: {
    type: String,
    enum: [
      '心經系列', '金剛經系列', '法華經系列', '華嚴經系列',
      '阿彌陀經系列', '藥師經系列', '地藏經系列', '觀音經典',
      '維摩經典', '楞嚴經典', '圓覺經典', '涅槃經典',
      '戒律條文', '論述解釋', '禪師語錄', '開示記錄'
    ]
  },
  content: {
    text: {
      type: String,
      required: true
    },
    translation: String, // 白話翻譯
    commentary: String, // 註釋解說
    audioUrl: String, // 音頻朗讀
    videoUrl: String, // 視頻講解
    pronunciation: String // 發音指導
  },
  metadata: {
    author: String, // 原作者
    translator: String, // 翻譯者
    commentator: String, // 註釋者
    source: String, // 出處典籍
    language: {
      type: String,
      default: 'zh-TW',
      enum: ['zh-TW', 'zh-CN', 'en', 'ja', 'ko', 'th', 'my', 'sanskrit', 'pali', 'tibetan']
    },
    period: {
      type: String,
      enum: ['佛陀時代', '初期佛教', '部派佛教', '大乘初期', '大乘中期', '大乘後期', '密教時期', '現代']
    },
    region: String, // 地區來源
    school: {
      type: String,
      enum: ['原始佛教', '上座部', '大眾部', '中觀派', '唯識派', '禪宗', '淨土宗', '天台宗', '華嚴宗', '密宗']
    }
  },
  healing: {
    applications: [{
      type: String,
      enum: [
        '心理療癒', '身體健康', '情緒平衡', '壓力緩解',
        '焦慮治療', '憂鬱改善', '創傷復原', '失眠改善',
        '疼痛管理', '慢性病輔助', '臨終關懷', '家庭和諧',
        '人際關係', '工作壓力', '學習困難', '成癮戒治'
      ]
    }],
    techniques: [{
      type: String,
      enum: [
        '持誦念佛', '觀想禪修', '呼吸調息', '行走禪修',
        '抄經書寫', '聞思修習', '懺悔修持', '慈悲觀修',
        '無常觀修', '空性觀修', '因緣觀修', '四聖諦修'
      ]
    }],
    benefits: [String], // 療癒功效
    instructions: String, // 修持指導
    precautions: String, // 注意事項
    duration: Number, // 建議修持時間（分鐘）
    frequency: String // 建議頻率
  },
  study: {
    difficulty: {
      type: String,
      enum: ['beginner', 'intermediate', 'advanced'],
      default: 'beginner'
    },
    readingTime: {
      type: Number, // 分鐘
      default: 0
    },
    studyGuide: String, // 學習指南
    keyPoints: [String], // 重點摘要
    questions: [String], // 思考問題
    exercises: [String], // 修持練習
    relatedTexts: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Scripture'
    }]
  },
  tags: [String], // 標籤
  searchKeywords: [String], // 搜索關鍵詞
  
  // 統計數據
  statistics: {
    popularity: {
      type: Number,
      default: 0
    },
    views: {
      type: Number,
      default: 0
    },
    bookmarks: {
      type: Number,
      default: 0
    },
    shares: {
      type: Number,
      default: 0
    },
    downloads: {
      type: Number,
      default: 0
    },
    ratings: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      score: {
        type: Number,
        min: 1,
        max: 5
      },
      comment: String,
      date: {
        type: Date,
        default: Date.now
      }
    }],
    averageRating: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    }
  },
  
  // 版本控制
  version: {
    type: String,
    default: '1.0'
  },
  versionHistory: [{
    version: String,
    changes: String,
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    updatedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // 狀態和權限
  status: {
    type: String,
    enum: ['draft', 'review', 'published', 'archived'],
    default: 'draft'
  },
  visibility: {
    type: String,
    enum: ['public', 'members_only', 'premium_only', 'private'],
    default: 'public'
  },
  featured: {
    type: Boolean,
    default: false
  },
  
  // 時間戳記
  created: {
    type: Date,
    default: Date.now
  },
  updated: {
    type: Date,
    default: Date.now
  },
  publishedAt: Date,
  
  // 創建和編輯者
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  lastEditedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 虛擬字段：字數統計
scriptureSchema.virtual('wordCount').get(function() {
  return this.content.text ? this.content.text.length : 0;
});

// 虛擬字段：是否有音頻
scriptureSchema.virtual('hasAudio').get(function() {
  return !!this.content.audioUrl;
});

// 虛擬字段：是否有視頻
scriptureSchema.virtual('hasVideo').get(function() {
  return !!this.content.videoUrl;
});

// 虛擬字段：完整標題（包含原標題）
scriptureSchema.virtual('fullTitle').get(function() {
  return this.originalTitle ? `${this.title} (${this.originalTitle})` : this.title;
});

// 索引設置
scriptureSchema.index({ title: 'text', 'content.text': 'text', tags: 'text', searchKeywords: 'text' });
scriptureSchema.index({ category: 1 });
scriptureSchema.index({ subcategory: 1 });
scriptureSchema.index({ 'metadata.language': 1 });
scriptureSchema.index({ 'metadata.period': 1 });
scriptureSchema.index({ 'metadata.school': 1 });
scriptureSchema.index({ 'healing.applications': 1 });
scriptureSchema.index({ 'study.difficulty': 1 });
scriptureSchema.index({ status: 1 });
scriptureSchema.index({ visibility: 1 });
scriptureSchema.index({ featured: 1 });
scriptureSchema.index({ 'statistics.popularity': -1 });
scriptureSchema.index({ 'statistics.averageRating': -1 });
scriptureSchema.index({ created: -1 });

// 複合索引
scriptureSchema.index({ category: 1, 'study.difficulty': 1 });
scriptureSchema.index({ 'healing.applications': 1, 'study.difficulty': 1 });
scriptureSchema.index({ status: 1, visibility: 1 });

// 更新時間中間件
scriptureSchema.pre('save', function(next) {
  this.updated = new Date();
  
  // 自動計算閱讀時間（平均每分鐘300字）
  if (this.content.text && !this.study.readingTime) {
    this.study.readingTime = Math.ceil(this.content.text.length / 300);
  }
  
  next();
});

// 發布狀態變更時設置發布時間
scriptureSchema.pre('save', function(next) {
  if (this.isModified('status') && this.status === 'published' && !this.publishedAt) {
    this.publishedAt = new Date();
  }
  next();
});

// 靜態方法：按分類搜索
scriptureSchema.statics.findByCategory = function(category, limit = 20) {
  return this.find({ 
    category: category, 
    status: 'published',
    visibility: { $in: ['public', 'members_only'] }
  })
  .sort({ 'statistics.popularity': -1 })
  .limit(limit);
};

// 靜態方法：按療癒應用搜索
scriptureSchema.statics.findByHealing = function(application, limit = 20) {
  return this.find({ 
    'healing.applications': application,
    status: 'published',
    visibility: { $in: ['public', 'members_only'] }
  })
  .sort({ 'statistics.averageRating': -1 })
  .limit(limit);
};

// 靜態方法：高級搜索
scriptureSchema.statics.advancedSearch = function(criteria) {
  const query = { status: 'published' };
  
  if (criteria.text) {
    query.$text = { $search: criteria.text };
  }
  
  if (criteria.category) {
    query.category = criteria.category;
  }
  
  if (criteria.difficulty) {
    query['study.difficulty'] = criteria.difficulty;
  }
  
  if (criteria.healing) {
    query['healing.applications'] = { $in: criteria.healing };
  }
  
  if (criteria.language) {
    query['metadata.language'] = criteria.language;
  }
  
  if (criteria.school) {
    query['metadata.school'] = criteria.school;
  }
  
  return this.find(query);
};

// 實例方法：增加瀏覽次數
scriptureSchema.methods.incrementViews = function() {
  this.statistics.views += 1;
  this.statistics.popularity += 1;
  return this.save();
};

// 實例方法：添加評分
scriptureSchema.methods.addRating = function(userId, score, comment) {
  // 檢查用戶是否已評分
  const existingRating = this.statistics.ratings.find(
    rating => rating.user.toString() === userId.toString()
  );
  
  if (existingRating) {
    existingRating.score = score;
    existingRating.comment = comment;
    existingRating.date = new Date();
  } else {
    this.statistics.ratings.push({
      user: userId,
      score: score,
      comment: comment
    });
  }
  
  // 重新計算平均評分
  const totalScore = this.statistics.ratings.reduce((sum, rating) => sum + rating.score, 0);
  this.statistics.averageRating = totalScore / this.statistics.ratings.length;
  
  return this.save();
};

// 實例方法：檢查用戶權限
scriptureSchema.methods.checkAccess = function(user) {
  if (this.visibility === 'public') {
    return true;
  }
  
  if (!user) {
    return false;
  }
  
  if (this.visibility === 'members_only') {
    return true; // 已登入用戶
  }
  
  if (this.visibility === 'premium_only') {
    return ['premium', 'vip'].includes(user.membership.plan);
  }
  
  if (this.visibility === 'private') {
    return user.role === 'admin' || user._id.toString() === this.createdBy.toString();
  }
  
  return false;
};

// 實例方法：獲取相關經文
scriptureSchema.methods.getRelatedTexts = function(limit = 5) {
  return this.constructor.find({
    _id: { $ne: this._id },
    status: 'published',
    $or: [
      { category: this.category },
      { subcategory: this.subcategory },
      { 'healing.applications': { $in: this.healing.applications } },
      { tags: { $in: this.tags } }
    ]
  })
  .sort({ 'statistics.averageRating': -1, 'statistics.popularity': -1 })
  .limit(limit);
};

module.exports = mongoose.model('Scripture', scriptureSchema);