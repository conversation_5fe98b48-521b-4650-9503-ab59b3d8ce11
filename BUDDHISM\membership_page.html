<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加入會員 - 佛教身心療癒網站</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2C3E50;
            --secondary-color: #F39C12;
            --accent-color: #E8F4F8;
            --text-color: #2C3E50;
            --light-bg: #F8F9FA;
            --success-color: #27AE60;
            --info-color: #3498DB;
            --premium-gold: #FFD700;
            --premium-purple: #8E44AD;
        }

        body {
            font-family: 'Noto Sans CJK TC', 'Microsoft JhengHei', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
        }

        .page-header {
            background: linear-gradient(135deg, var(--premium-purple) 0%, var(--primary-color) 100%);
            color: white;
            padding: 120px 0 80px;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(255,215,0,0.2)"/><circle cx="80" cy="80" r="1" fill="rgba(255,215,0,0.2)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,215,0,0.1)"/></svg>') repeat;
        }

        .navbar-custom {
            background: rgba(44, 62, 80, 0.95);
            backdrop-filter: blur(10px);
        }

        .nav-link {
            color: white !important;
            font-weight: 500;
            margin: 0 10px;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--secondary-color) !important;
        }

        .membership-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border: 3px solid transparent;
        }

        .membership-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .membership-card.featured {
            border-color: var(--premium-gold);
            transform: scale(1.05);
        }

        .membership-card.featured:hover {
            transform: scale(1.05) translateY(-10px);
        }

        .membership-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
        }

        .membership-card.basic::before { background: var(--success-color); }
        .membership-card.premium::before { background: var(--secondary-color); }
        .membership-card.vip::before { background: var(--premium-gold); }

        .featured-badge {
            position: absolute;
            top: -15px;
            right: 30px;
            background: var(--premium-gold);
            color: var(--primary-color);
            padding: 8px 20px;
            border-radius: 15px;
            font-weight: bold;
            font-size: 0.9rem;
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
        }

        .plan-title {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
        }

        .plan-title.basic { color: var(--success-color); }
        .plan-title.premium { color: var(--secondary-color); }
        .plan-title.vip { color: var(--premium-gold); }

        .plan-price {
            text-align: center;
            margin-bottom: 30px;
        }

        .price-amount {
            font-size: 3rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .price-period {
            font-size: 1rem;
            color: #666;
            margin-left: 10px;
        }

        .price-original {
            text-decoration: line-through;
            color: #999;
            font-size: 1.2rem;
            margin-right: 10px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin-bottom: 30px;
        }

        .feature-list li {
            padding: 12px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list i {
            margin-right: 15px;
            font-size: 1.2rem;
        }

        .feature-list .fa-check { color: var(--success-color); }
        .feature-list .fa-star { color: var(--secondary-color); }
        .feature-list .fa-crown { color: var(--premium-gold); }

        .btn-membership {
            width: 100%;
            padding: 15px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.1rem;
            border: none;
            transition: all 0.3s ease;
        }

        .btn-basic {
            background: var(--success-color);
            color: white;
        }

        .btn-basic:hover {
            background: #219A52;
            transform: translateY(-2px);
            color: white;
        }

        .btn-premium {
            background: var(--secondary-color);
            color: white;
        }

        .btn-premium:hover {
            background: #E67E22;
            transform: translateY(-2px);
            color: white;
        }

        .btn-vip {
            background: linear-gradient(135deg, var(--premium-gold), #FFA500);
            color: var(--primary-color);
        }

        .btn-vip:hover {
            background: linear-gradient(135deg, #FFA500, var(--premium-gold));
            transform: translateY(-2px);
            color: var(--primary-color);
        }

        .section-title {
            position: relative;
            text-align: center;
            margin-bottom: 50px;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: var(--secondary-color);
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin: 50px 0;
        }

        .benefit-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .benefit-card:hover {
            transform: translateY(-5px);
        }

        .benefit-icon {
            font-size: 3rem;
            color: var(--secondary-color);
            margin-bottom: 20px;
        }

        .testimonial-section {
            background: var(--light-bg);
            padding: 80px 0;
        }

        .testimonial-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 4px solid var(--premium-gold);
        }

        .testimonial-text {
            font-style: italic;
            margin-bottom: 20px;
            font-size: 1.1rem;
            line-height: 1.8;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
        }

        .author-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 15px;
        }

        .author-info h6 {
            margin: 0;
            color: var(--primary-color);
        }

        .author-info small {
            color: #666;
        }

        .faq-section {
            padding: 80px 0;
        }

        .faq-item {
            background: white;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .faq-question {
            padding: 20px;
            background: var(--light-bg);
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: bold;
            transition: background 0.3s ease;
        }

        .faq-question:hover {
            background: var(--accent-color);
        }

        .faq-answer {
            padding: 0 20px;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .faq-answer.active {
            padding: 20px;
            max-height: 200px;
        }

        .cta-section {
            background: linear-gradient(135deg, var(--premium-purple), var(--primary-color));
            color: white;
            padding: 80px 0;
            text-align: center;
        }

        @media (max-width: 768px) {
            .page-header {
                padding: 100px 0 60px;
            }
            .membership-card {
                padding: 25px;
            }
            .membership-card.featured {
                transform: none;
            }
            .membership-card.featured:hover {
                transform: translateY(-10px);
            }
            .price-amount {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html" style="color: var(--secondary-color);">
                <i class="fas fa-lotus me-2"></i>佛教身心療癒
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">主頁</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">關於我們</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            課程
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses-goals.html">課程目標</a></li>
                            <li><a class="dropdown-item" href="courses-weekly.html">每週主題</a></li>
                            <li><a class="dropdown-item" href="courses-methods.html">進行方式</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            經文選讀
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="scriptures-search.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="scriptures-methods.html">療癒方法</a></li>
                            <li><a class="dropdown-item" href="scriptures-theory.html">療癒理論</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            身心療癒研究
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="research-search.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="research-theory.html">療癒理論</a></li>
                            <li><a class="dropdown-item" href="research-methods.html">療癒方法</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            藏傳佛教
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="tibetan-theory.html">藏醫理論</a></li>
                            <li><a class="dropdown-item" href="tibetan-practice.html">藏醫實習</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            海外實習
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="overseas-members.html">成員</a></li>
                            <li><a class="dropdown-item" href="overseas-research.html">研究成果</a></li>
                            <li><a class="dropdown-item" href="overseas-future.html">未來計畫</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="workshop.html">工作坊</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="membership.html">加入會員</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 頁面標題 -->
    <section class="page-header">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-4">加入會員</h1>
                    <p class="lead">選擇最適合您的會員方案，開啟身心療癒的智慧之旅</p>
                </div>
            </div>
        </div>
    </section>

    <main>
        <!-- 會員方案 -->
        <section class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 class="section-title h1 fw-bold">會員方案選擇</h2>
                        <p class="text-center lead mb-5">三種會員等級，滿足不同學習需求與預算</p>
                    </div>
                </div>

                <div class="row">
                    <!-- 基礎會員 -->
                    <div class="col-lg-4">
                        <div class="membership-card basic">
                            <h3 class="plan-title basic">基礎會員</h3>
                            <div class="plan-price">
                                <span class="price-amount">NT$299</span>
                                <span class="price-period">/月</span>
                            </div>
                            <ul class="feature-list">
                                <li><i class="fas fa-check"></i>完整線上課程存取</li>
                                <li><i class="fas fa-check"></i>基礎冥想指導</li>
                                <li><i class="fas fa-check"></i>學習進度追蹤</li>
                                <li><i class="fas fa-check"></i>社群討論參與</li>
                                <li><i class="fas fa-check"></i>月度電子報</li>
                                <li><i class="fas fa-check"></i>基礎經文資料庫</li>
                            </ul>
                            <button class="btn-membership btn-basic">選擇基礎會員</button>
                        </div>
                    </div>

                    <!-- 進階會員 (推薦) -->
                    <div class="col-lg-4">
                        <div class="membership-card premium featured">
                            <div class="featured-badge">最受歡迎</div>
                            <h3 class="plan-title premium">進階會員</h3>
                            <div class="plan-price">
                                <span class="price-original">NT$599</span>
                                <span class="price-amount">NT$399</span>
                                <span class="price-period">/月</span>
                            </div>
                            <ul class="feature-list">
                                <li><i class="fas fa-star"></i>包含基礎會員所有功能</li>
                                <li><i class="fas fa-star"></i>每週直播課程</li>
                                <li><i class="fas fa-star"></i>一對一指導諮詢</li>
                                <li><i class="fas fa-star"></i>工作坊優先報名</li>
                                <li><i class="fas fa-star"></i>進階經文與研究資料</li>
                                <li><i class="fas fa-star"></i>個人化學習計畫</li>
                                <li><i class="fas fa-star"></i>專屬客服支援</li>
                            </ul>
                            <button class="btn-membership btn-premium">選擇進階會員</button>
                        </div>
                    </div>

                    <!-- VIP會員 -->
                    <div class="col-lg-4">
                        <div class="membership-card vip">
                            <h3 class="plan-title vip">VIP會員</h3>
                            <div class="plan-price">
                                <span class="price-amount">NT$799</span>
                                <span class="price-period">/月</span>
                            </div>
                            <ul class="feature-list">
                                <li><i class="fas fa-crown"></i>包含進階會員所有功能</li>
                                <li><i class="fas fa-crown"></i>海外實習優先資格</li>
                                <li><i class="fas fa-crown"></i>專家一對一深度指導</li>
                                <li><i class="fas fa-crown"></i>認證課程免費參與</li>
                                <li><i class="fas fa-crown"></i>完整研究資料庫存取</li>
                                <li><i class="fas fa-crown"></i>年度會員大會邀請</li>
                                <li><i class="fas fa-crown"></i>終身學習折扣優惠</li>
                                <li><i class="fas fa-crown"></i>專屬VIP客戶經理</li>
                            </ul>
                            <button class="btn-membership btn-vip">選擇VIP會員</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 會員專屬福利 -->
        <section class="py-5" style="background: var(--light-bg);">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 class="section-title h1 fw-bold">會員專屬福利</h2>
                    </div>
                </div>

                <div class="benefits-grid">
                    <div class="benefit-card">
                        <div class="benefit-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h5 class="fw-bold">專業認證</h5>
                        <p>完成課程可獲得國際認可的佛教身心療癒師認證</p>
                    </div>
                    <div class="benefit-card">
                        <div class="benefit-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h5 class="fw-bold">學習社群</h5>
                        <p>加入全球學習者社群，分享經驗與相互支持</p>
                    </div>
                    <div class="benefit-card">
                        <div class="benefit-icon">
                            <i class="fas fa-book-open"></i>
                        </div>
                        <h5 class="fw-bold">豐富資源</h5>
                        <p>存取完整的經文、研究報告與學習教材</p>
                    </div>
                    <div class="benefit-card">
                        <div class="benefit-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h5 class="fw-bold">專業支援</h5>
                        <p>享受24/7專業客服與學習指導支援</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 會員見證 -->
        <section class="testimonial-section">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 class="section-title h1 fw-bold">會員真實見證</h2>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-6">
                        <div class="testimonial-card">
                            <div class="testimonial-text">
                                "成為會員後，我的生活發生了巨大變化。正念冥想幫助我重新找回內心平靜，工作壓力也大幅減輕。一對一指導更是讓我的修習事半功倍！"
                            </div>
                            <div class="testimonial-author">
                                <div class="author-avatar">李</div>
                                <div class="author-info">
                                    <h6>李雅婷</h6>
                                    <small>進階會員，心理師</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="testimonial-card">
                            <div class="testimonial-text">
                                "VIP會員的海外實習機會改變了我的人生觀。在西藏的三個月學習，讓我深度體驗了藏醫文化，現在我已經成為認證的療癒師！"
                            </div>
                            <div class="testimonial-author">
                                <div class="author-avatar">王</div>
                                <div class="author-info">
                                    <h6>王建華</h6>
                                    <small>VIP會員，療癒師</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="testimonial-card">
                            <div class="testimonial-text">
                                "基礎會員方案非常適合初學者。線上課程內容豐富，社群討論讓我學到很多。客服團隊也很專業，總是能及時解答我的疑問。"
                            </div>
                            <div class="testimonial-author">
                                <div class="author-avatar">陳</div>
                                <div class="author-info">
                                    <h6>陳美華</h6>
                                    <small>基礎會員，上班族</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="testimonial-card">
                            <div class="testimonial-text">
                                "進階會員的直播課程品質很高，可以即時與老師互動。個人化學習計畫更是貼心，完全根據我的需求制定，學習效果顯著提升。"
                            </div>
                            <div class="testimonial-author">
                                <div class="author-avatar">張</div>
                                <div class="author-info">
                                    <h6>張志明</h6>
                                    <small>進階會員，教師</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 常見問題 -->
        <section class="faq-section">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 class="section-title h1 fw-bold">常見問題</h2>
                    </div>
                </div>

                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFAQ(this)">
                                <span>如何選擇適合的會員方案？</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>建議根據您的學習目標和時間安排來選擇。初學者可以從基礎會員開始，希望深度學習的選擇進階會員，有志成為專業療癒師的選擇VIP會員。</p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFAQ(this)">
                                <span>是否可以隨時升級或降級會員？</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>是的，您可以隨時升級會員方案。降級則會在下個計費週期生效。我們提供彈性的會員管理，讓您根據需求調整。</p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFAQ(this)">
                                <span>會員費用包含哪些服務？</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>會員費用包含相應等級的所有線上課程、學習資源、技術支援等。額外的工作坊、實習計畫等可能需要另行付費。</p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFAQ(this)">
                                <span>如果不滿意可以退款嗎？</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>我們提供7天無條件退款保證。如果您在加入會員後7天內不滿意，可以申請全額退款。</p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFAQ(this)">
                                <span>會員資格是否有期限限制？</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>會員資格按月計費，無最低使用期限。您可以隨時取消會員資格，取消後仍可使用至當期結束。</p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFAQ(this)">
                                <span>海外學員是否可以加入會員？</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>當然可以！我們歡迎全球學員加入。線上課程支援多語言字幕，海外實習計畫更是我們的特色服務。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 立即加入 -->
        <section class="cta-section">
            <div class="container">
                <div class="row justify-content-center text-center">
                    <div class="col-lg-8">
                        <h2 class="fw-bold mb-4">
                            <i class="fas fa-lotus me-3"></i>
                            立即開啟您的療癒之旅
                        </h2>
                        <p class="lead mb-5">
                            加入我們的會員大家庭，與來自世界各地的學習者一起成長，
                            在佛教身心療癒的智慧中找到內心平靜與生命的意義。
                        </p>
                        
                        <div class="row justify-content-center mb-5">
                            <div class="col-md-3 mb-3">
                                <h3 class="fw-bold">500+</h3>
                                <p>會員學習者</p>
                            </div>
                            <div class="col-md-3 mb-3">
                                <h3 class="fw-bold">95%</h3>
                                <p>滿意度評分</p>
                            </div>
                            <div class="col-md-3 mb-3">
                                <h3 class="fw-bold">24/7</h3>
                                <p>全天候支援</p>
                            </div>
                            <div class="col-md-3 mb-3">
                                <h3 class="fw-bold">18</h3>
                                <p>個國家地區</p>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-center gap-3 flex-wrap">
                            <button class="btn btn-light btn-lg" onclick="scrollToPlans()">查看方案詳情</button>
                            <button class="btn btn-outline-light btn-lg" onclick="startFreeTrial()">7天免費體驗</button>
                        </div>
                        
                        <p class="mt-4 small">
                            <i class="fas fa-shield-alt me-2"></i>
                            安全付款 | 7天退款保證 | 隨時取消
                        </p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // 導航欄滾動效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar-custom');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // FAQ 切換功能
        function toggleFAQ(element) {
            const answer = element.nextElementSibling;
            const icon = element.querySelector('i');
            
            // 關閉其他打開的FAQ
            document.querySelectorAll('.faq-answer.active').forEach(item => {
                if (item !== answer) {
                    item.classList.remove('active');
                    item.previousElementSibling.querySelector('i').classList.remove('fa-chevron-up');
                    item.previousElementSibling.querySelector('i').classList.add('fa-chevron-down');
                }
            });
            
            // 切換當前FAQ
            answer.classList.toggle('active');
            if (answer.classList.contains('active')) {
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
            } else {
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
            }
        }

        // 滾動到方案區域
        function scrollToPlans() {
            document.querySelector('.membership-card').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }

        // 開始免費體驗
        function startFreeTrial() {
            alert('歡迎體驗！我們將為您開啟7天免費試用，請填寫基本資料完成註冊。');
        }

        // 會員方案選擇
        document.querySelectorAll('.btn-membership').forEach(btn => {
            btn.addEventListener('click', function() {
                const planType = this.classList.contains('btn-basic') ? '基礎會員' :
                                this.classList.contains('btn-premium') ? '進階會員' : 'VIP會員';
                alert(`感謝選擇${planType}！我們將引導您完成註冊流程。`);
            });
        });

        // 卡片動畫
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.membership-card, .benefit-card, .testimonial-card, .faq-item');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = `all 0.6s ease ${index * 0.1}s`;
                observer.observe(card);
            });
        });

        // 特色會員卡片效果
        document.querySelector('.membership-card.featured').addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05) translateY(-15px) rotateY(5deg)';
        });

        document.querySelector('.membership-card.featured').addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1.05) translateY(0) rotateY(0deg)';
        });
    </script>
</body>
</html>