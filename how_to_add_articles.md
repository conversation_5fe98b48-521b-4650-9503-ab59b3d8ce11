# 如何新增 dharma.html 文章內容

## 🎯 快速添加步驟

### 1. 打開檔案
用文字編輯器打開：`pages/dharma.html`

### 2. 找到數據區域
搜尋：`const articlesData = [`（約在第680行）

### 3. 添加新文章
在第918行附近找到最後一篇文章的結尾 `}`，添加逗號後插入新文章：

```javascript
            },  // 前一篇文章的結尾
            {
                id: 21,  // 使用下一個編號
                title: "您的文章標題",
                excerpt: "文章摘要，建議100-200字，簡潔描述文章主要內容...",
                category: "practice",  // 選擇合適的分類
                difficulty: "beginner",  // 選擇難度等級
                tags: ["標籤1", "標籤2", "標籤3"],  // 2-3個相關標籤
                date: "2025-01-15",  // 發布日期
                views: 0,
                likes: 0,
                icon: "fas fa-heart"  // 選擇合適的圖標
            }  // 注意：如果這是最後一篇，不要加逗號
```

## 📋 可用選項

### 分類選項 (category)
- `meditation` - 禪修指導
- `philosophy` - 佛教哲學  
- `practice` - 修行方法
- `sutras` - 經典解讀
- `masters` - 大師教導
- `ethics` - 佛教倫理
- `tradition` - 傳承體系
- `stories` - 佛教故事

### 難度選項 (difficulty)
- `beginner` - 入門
- `intermediate` - 進階
- `advanced` - 高級

### 常用圖標 (icon)
- `fas fa-heart` - 慈悲相關
- `fas fa-lotus` - 清淨覺悟
- `fas fa-dharmachakra` - 佛法教義
- `fas fa-meditation` - 禪修相關
- `fas fa-book` - 經典學習
- `fas fa-gem` - 珍貴教法
- `fas fa-mountain` - 修行成就
- `fas fa-scroll` - 經典文獻

## ✅ 檢查清單

添加完成後請檢查：
- [ ] ID 編號是否唯一且連續
- [ ] 所有欄位是否都有值
- [ ] 引號和逗號語法是否正確
- [ ] category 是否為有效選項
- [ ] difficulty 是否為有效選項
- [ ] 日期格式是否正確（YYYY-MM-DD）
- [ ] 最後一篇文章後是否沒有多餘逗號

## 🚀 生效方式

保存檔案後：
1. 重新整理 dharma.html 頁面
2. 新文章會自動出現在文章列表中
3. 可以透過分類和搜索找到新文章
4. 點擊文章會導向到 article.html 頁面

## 💡 小貼士

- **標題**：簡潔明瞭，體現文章核心主題
- **摘要**：100-200字，吸引讀者閱讀興趣
- **標籤**：選擇最相關的2-3個關鍵詞
- **分類**：選擇最符合內容性質的分類
- **難度**：考慮目標讀者的佛學基礎

這樣就可以輕鬆擴展您的佛學文章庫了！