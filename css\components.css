/* 澄源閱讀 - 組件樣式文件 */

/* ===== 按鈕組件 ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1.5;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  user-select: none;
  white-space: nowrap;
  vertical-align: middle;
}

.btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 按鈕尺寸 */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-lg);
}

.btn-xl {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: var(--font-size-xl);
}

/* 按鈕樣式變體 */
.btn-primary {
  background: var(--gradient-primary);
  color: var(--white);
  border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
  background: var(--gradient-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: var(--gray-100);
  color: var(--gray-700);
  border-color: var(--gray-200);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--gray-200);
  color: var(--gray-800);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
  background: var(--primary-color);
  color: var(--white);
}

.btn-ghost {
  background: transparent;
  color: var(--gray-600);
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  background: var(--gray-100);
  color: var(--gray-700);
}

.btn-danger {
  background: var(--error-color);
  color: var(--white);
  border-color: var(--error-color);
}

.btn-danger:hover:not(:disabled) {
  background: #d32f2f;
  border-color: #d32f2f;
}

/* 按鈕載入狀態 */
.btn-loading {
  position: relative;
  color: transparent !important;
}

.btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: btn-spin 0.8s linear infinite;
}

@keyframes btn-spin {
  to { transform: rotate(360deg); }
}

/* ===== 表單組件 ===== */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--gray-700);
}

.form-label.required::after {
  content: ' *';
  color: var(--error-color);
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: var(--font-size-sm);
  line-height: 1.5;
  color: var(--gray-700);
  background-color: var(--white);
  background-image: none;
  transition: all var(--transition-normal);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control:disabled {
  background-color: var(--gray-100);
  opacity: 0.6;
  cursor: not-allowed;
}

.form-control.is-invalid {
  border-color: var(--error-color);
}

.form-control.is-invalid:focus {
  box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
}

/* 表單尺寸 */
.form-control-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
}

.form-control-lg {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-lg);
}

/* 輸入組 */
.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}

.input-group .form-control {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}

.input-group-prepend,
.input-group-append {
  display: flex;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  margin-bottom: 0;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  line-height: 1.5;
  color: var(--gray-500);
  text-align: center;
  white-space: nowrap;
  background-color: var(--gray-100);
  border: 1px solid var(--gray-300);
}

.input-group-prepend .input-group-text {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group-append .input-group-text {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.input-group-prepend + .form-control {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.form-control + .input-group-append {
  margin-left: -1px;
}

.form-control + .input-group-append .input-group-text {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

/* 表單驗證反饋 */
.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: var(--error-color);
}

.valid-feedback {
  display: block;
  width: 100%;
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: var(--success-color);
}

/* ===== 卡片組件 ===== */
.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: var(--white);
  background-clip: border-box;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--spacing-lg);
  margin-bottom: 0;
  background-color: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
  border-top-left-radius: calc(var(--radius-lg) - 1px);
  border-top-right-radius: calc(var(--radius-lg) - 1px);
}

.card-body {
  flex: 1 1 auto;
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-lg);
  background-color: var(--gray-50);
  border-top: 1px solid var(--gray-200);
  border-bottom-left-radius: calc(var(--radius-lg) - 1px);
  border-bottom-right-radius: calc(var(--radius-lg) - 1px);
}

.card-title {
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-700);
}

.card-subtitle {
  margin-top: calc(var(--spacing-md) * -0.5);
  margin-bottom: 0;
  font-size: var(--font-size-sm);
  color: var(--gray-500);
}

.card-text {
  margin-bottom: var(--spacing-md);
  color: var(--gray-600);
  line-height: var(--line-height-relaxed);
}

.card-link {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-normal);
}

.card-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* 卡片變體 */
.card-elevated {
  box-shadow: var(--shadow-lg);
  border: none;
}

.card-outlined {
  border: 2px solid var(--gray-200);
  box-shadow: none;
}

.card-gradient {
  background: var(--gradient-primary);
  color: var(--white);
  border: none;
}

.card-gradient .card-title,
.card-gradient .card-text {
  color: var(--white);
}

/* ===== 徽章組件 ===== */
.badge {
  display: inline-block;
  padding: 0.25em 0.6em;
  font-size: 0.75em;
  font-weight: var(--font-weight-bold);
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: var(--radius-sm);
  transition: all var(--transition-normal);
}

.badge-primary {
  color: var(--white);
  background-color: var(--primary-color);
}

.badge-secondary {
  color: var(--white);
  background-color: var(--gray-500);
}

.badge-success {
  color: var(--white);
  background-color: var(--success-color);
}

.badge-warning {
  color: var(--black);
  background-color: var(--warning-color);
}

.badge-danger {
  color: var(--white);
  background-color: var(--error-color);
}

.badge-info {
  color: var(--white);
  background-color: var(--info-color);
}

.badge-light {
  color: var(--gray-700);
  background-color: var(--gray-100);
}

.badge-dark {
  color: var(--white);
  background-color: var(--gray-700);
}

/* 徽章變體 */
.badge-pill {
  padding-right: 0.6em;
  padding-left: 0.6em;
  border-radius: 10rem;
}

.badge-outline-primary {
  color: var(--primary-color);
  background-color: transparent;
  border: 1px solid var(--primary-color);
}

/* ===== 進度條組件 ===== */
.progress {
  display: flex;
  height: 1rem;
  overflow: hidden;
  font-size: 0.75rem;
  background-color: var(--gray-200);
  border-radius: var(--radius-sm);
}

.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: var(--white);
  text-align: center;
  white-space: nowrap;
  background: var(--gradient-primary);
  transition: width 0.6s ease;
}

.progress-bar-striped {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem;
}

.progress-bar-animated {
  animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
  0% { background-position-x: 1rem; }
}

/* ===== 標籤組件 ===== */
.tab-nav {
  display: flex;
  border-bottom: 1px solid var(--gray-200);
  margin-bottom: var(--spacing-lg);
}

.tab-link {
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--gray-500);
  text-decoration: none;
  border-bottom: 2px solid transparent;
  transition: all var(--transition-normal);
}

.tab-link:hover {
  color: var(--gray-700);
  border-bottom-color: var(--gray-300);
}

.tab-link.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* ===== 下拉選單組件 ===== */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-normal);
}

.dropdown-toggle:hover {
  background-color: var(--gray-100);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: var(--z-dropdown);
  min-width: 160px;
  padding: var(--spacing-sm) 0;
  margin: 2px 0 0;
  background-color: var(--white);
  background-clip: padding-box;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--transition-normal);
}

.dropdown.show .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: var(--spacing-xs) var(--spacing-lg);
  clear: both;
  font-weight: var(--font-weight-normal);
  color: var(--gray-700);
  text-align: inherit;
  text-decoration: none;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.dropdown-item:hover,
.dropdown-item:focus {
  color: var(--gray-800);
  background-color: var(--gray-100);
}

.dropdown-item.active,
.dropdown-item:active {
  color: var(--white);
  background-color: var(--primary-color);
}

.dropdown-item:disabled,
.dropdown-item.disabled {
  color: var(--gray-400);
  pointer-events: none;
  background-color: transparent;
}

.dropdown-divider {
  height: 0;
  margin: var(--spacing-xs) 0;
  overflow: hidden;
  border-top: 1px solid var(--gray-200);
}

/* ===== 模態框組件 ===== */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--z-modal);
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--z-modal-backdrop);
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: var(--spacing-lg);
  pointer-events: none;
  transform: scale(0.9);
  transition: transform var(--transition-normal);
}

.modal.show .modal-dialog {
  transform: scale(1);
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: var(--white);
  background-clip: padding-box;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  outline: 0;
}

.modal-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
  border-top-left-radius: calc(var(--radius-lg) - 1px);
  border-top-right-radius: calc(var(--radius-lg) - 1px);
}

.modal-title {
  margin-bottom: 0;
  line-height: 1.5;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: var(--spacing-lg);
}

.modal-footer {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
  padding: calc(var(--spacing-lg) - var(--spacing-xs) * 0.5);
  border-top: 1px solid var(--gray-200);
  border-bottom-right-radius: calc(var(--radius-lg) - 1px);
  border-bottom-left-radius: calc(var(--radius-lg) - 1px);
}

.modal-footer > * {
  margin: calc(var(--spacing-xs) * 0.5);
}

.modal-close {
  padding: var(--spacing-lg);
  margin: calc(var(--spacing-lg) * -1) calc(var(--spacing-lg) * -1) calc(var(--spacing-lg) * -1) auto;
  background: none;
  border: none;
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  line-height: 1;
  color: var(--gray-400);
  cursor: pointer;
  transition: color var(--transition-normal);
}

.modal-close:hover {
  color: var(--gray-600);
}

/* 模態框尺寸 */
@media (min-width: 576px) {
  .modal-dialog {
    max-width: 500px;
    margin: var(--spacing-3xl) auto;
  }
  
  .modal-dialog-sm {
    max-width: 300px;
  }
}

@media (min-width: 992px) {
  .modal-dialog-lg,
  .modal-dialog-xl {
    max-width: 800px;
  }
}

@media (min-width: 1200px) {
  .modal-dialog-xl {
    max-width: 1140px;
  }
}

/* ===== 工具提示組件 ===== */
.tooltip {
  position: absolute;
  z-index: var(--z-tooltip);
  display: block;
  margin: var(--spacing-xs);
  font-family: var(--font-family);
  font-style: normal;
  font-weight: var(--font-weight-normal);
  line-height: 1.5;
  text-align: left;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: var(--font-size-xs);
  word-wrap: break-word;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.tooltip.show {
  opacity: 1;
  visibility: visible;
}

.tooltip-inner {
  max-width: 200px;
  padding: var(--spacing-xs) var(--spacing-sm);
  color: var(--white);
  text-align: center;
  background-color: var(--gray-800);
  border-radius: var(--radius-sm);
}

/* ===== 分頁組件 ===== */
.pagination {
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: var(--radius-md);
}

.page-item {
  margin: 0 var(--spacing-xs);
}

.page-link {
  position: relative;
  display: block;
  padding: var(--spacing-sm) var(--spacing-md);
  margin-left: -1px;
  line-height: 1.25;
  color: var(--primary-color);
  text-decoration: none;
  background-color: var(--white);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
}

.page-link:hover {
  z-index: 2;
  color: var(--primary-dark);
  background-color: var(--gray-100);
  border-color: var(--gray-300);
}

.page-item.active .page-link {
  z-index: 3;
  color: var(--white);
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.page-item.disabled .page-link {
  color: var(--gray-400);
  pointer-events: none;
  cursor: auto;
  background-color: var(--white);
  border-color: var(--gray-300);
}

/* ===== 響應式工具類 ===== */
@media (max-width: 767.98px) {
  .d-sm-none { display: none !important; }
  .d-sm-block { display: block !important; }
  .d-sm-flex { display: flex !important; }
}

@media (min-width: 768px) {
  .d-md-none { display: none !important; }
  .d-md-block { display: block !important; }
  .d-md-flex { display: flex !important; }
}

@media (min-width: 992px) {
  .d-lg-none { display: none !important; }
  .d-lg-block { display: block !important; }
  .d-lg-flex { display: flex !important; }
}