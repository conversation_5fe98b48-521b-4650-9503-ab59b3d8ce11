<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的收藏 - 澄源閱讀</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 頁面標題 */
        .page-header {
            background: linear-gradient(135deg, #e91e63 0%, #f06292 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            border-radius: 15px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50 10 L60 40 L90 40 L70 60 L80 90 L50 75 L20 90 L30 60 L10 40 L40 40 Z" fill="white" opacity="0.1"/></svg>');
            background-size: 60px 60px;
            animation: float-hearts 20s linear infinite;
        }

        @keyframes float-hearts {
            0% { transform: translateY(0) rotate(0deg); }
            100% { transform: translateY(-60px) rotate(360deg); }
        }

        .page-header-content {
            position: relative;
            z-index: 2;
        }

        .page-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            animation: heart-beat 2s ease-in-out infinite;
        }

        @keyframes heart-beat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .page-subtitle {
            font-size: 1rem;
            opacity: 0.9;
        }

        /* 收藏統計 */
        .favorites-stats {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: #c2185b;
            display: block;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #880e4f;
        }

        /* 工具欄 */
        .favorites-toolbar {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .toolbar-left {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-select {
            padding: 8px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            background: white;
            font-size: 0.9rem;
            min-width: 120px;
        }

        .filter-select:focus {
            outline: none;
            border-color: #e91e63;
        }

        .search-box {
            position: relative;
        }

        .search-input {
            padding: 8px 35px 8px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            width: 200px;
            font-size: 0.9rem;
        }

        .search-input:focus {
            outline: none;
            border-color: #e91e63;
        }

        .search-icon {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }

        .toolbar-right {
            display: flex;
            gap: 10px;
        }

        .toolbar-btn {
            padding: 8px 15px;
            border: 2px solid #e91e63;
            border-radius: 8px;
            background: white;
            color: #e91e63;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .toolbar-btn:hover {
            background: #e91e63;
            color: white;
        }

        .toolbar-btn.active {
            background: #e91e63;
            color: white;
        }

        /* 收藏內容 */
        .favorites-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .view-toggle {
            display: flex;
            gap: 10px;
            margin-bottom: 25px;
            justify-content: flex-end;
        }

        .view-btn {
            padding: 8px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .view-btn.active {
            border-color: #e91e63;
            background: #fce4ec;
            color: #e91e63;
        }

        /* 網格視圖 */
        .favorites-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .favorite-card {
            border: 1px solid #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .favorite-card:hover {
            border-color: #e91e63;
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
        }

        .favorite-card.selected {
            border-color: #e91e63;
            background: #fce4ec;
        }

        .card-checkbox {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 2;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .favorite-card:hover .card-checkbox,
        .favorite-card.selected .card-checkbox {
            opacity: 1;
        }

        .card-image {
            width: 100%;
            height: 150px;
            background: linear-gradient(135deg, #e91e63 0%, #f06292 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            position: relative;
        }

        .card-category {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255,255,255,0.9);
            color: #e91e63;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .card-content {
            padding: 20px;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
            line-height: 1.4;
        }

        .card-excerpt {
            color: #666;
            font-size: 0.8rem;
            line-height: 1.5;
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .card-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.7rem;
            color: #999;
        }

        .card-date {
            display: flex;
            align-items: center;
            gap: 3px;
        }

        .card-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            background: none;
            border: none;
            color: #999;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: #f0f0f0;
            color: #e91e63;
        }

        /* 列表視圖 */
        .favorites-list {
            display: none;
            flex-direction: column;
            gap: 15px;
        }

        .favorite-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 1px solid #f0f0f0;
            border-radius: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .favorite-item:hover {
            border-color: #e91e63;
            background: #fce4ec;
        }

        .item-checkbox {
            margin-right: 15px;
        }

        .item-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #e91e63, #f06292);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            margin-right: 15px;
        }

        .item-content {
            flex: 1;
        }

        .item-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .item-meta {
            font-size: 0.8rem;
            color: #666;
        }

        .item-actions {
            display: flex;
            gap: 8px;
            margin-left: 15px;
        }

        /* 空狀態 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-icon {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 20px;
        }

        .empty-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .empty-description {
            color: #999;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .empty-action {
            background: linear-gradient(135deg, #e91e63, #f06292);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: transform 0.3s ease;
        }

        .empty-action:hover {
            transform: translateY(-2px);
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .page-header {
                padding: 30px 20px;
            }
            
            .favorites-toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .toolbar-left,
            .toolbar-right {
                justify-content: center;
            }
            
            .search-input {
                width: 100%;
            }
            
            .favorites-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .page-title {
                font-size: 1.5rem;
            }
            
            .card-content,
            .favorites-stats {
                padding: 20px;
            }
            
            .favorite-item {
                flex-direction: column;
                align-items: stretch;
                text-align: center;
            }
            
            .item-icon {
                align-self: center;
                margin: 0 0 10px 0;
            }
            
            .item-actions {
                margin: 10px 0 0 0;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 頁面標題 -->
        <header class="page-header">
            <div class="page-header-content">
                <div class="page-icon">
                    <i class="fas fa-heart"></i>
                </div>
                <h1 class="page-title">我的收藏</h1>
                <p class="page-subtitle">珍藏您喜愛的智慧文章，隨時回顧學習</p>
            </div>
        </header>

        <!-- 收藏統計 -->
        <section class="favorites-stats">
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number" id="total-count">0</span>
                    <span class="stat-label">總收藏</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="dharma-count">0</span>
                    <span class="stat-label">佛學智慧</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="healing-count">0</span>
                    <span class="stat-label">身心療癒</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="research-count">0</span>
                    <span class="stat-label">最新研究</span>
                </div>
            </div>
        </section>

        <!-- 工具欄 -->
        <section class="favorites-toolbar">
            <div class="toolbar-left">
                <select class="filter-select" id="category-filter">
                    <option value="">所有分類</option>
                    <option value="dharma">佛學智慧</option>
                    <option value="healing">身心療癒</option>
                    <option value="research">最新研究</option>
                </select>
                
                <select class="filter-select" id="sort-filter">
                    <option value="date">收藏時間</option>
                    <option value="title">標題排序</option>
                    <option value="category">分類排序</option>
                </select>
                
                <div class="search-box">
                    <input type="text" class="search-input" id="search-input" placeholder="搜索收藏...">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="toolbar-right">
                <button class="toolbar-btn" id="select-all-btn">全選</button>
                <button class="toolbar-btn" id="export-btn">導出</button>
                <button class="toolbar-btn" id="delete-btn" style="display: none;">刪除選中</button>
            </div>
        </section>

        <!-- 收藏內容 -->
        <section class="favorites-content">
            <div class="view-toggle">
                <button class="view-btn active" id="grid-view-btn">
                    <i class="fas fa-th"></i>
                </button>
                <button class="view-btn" id="list-view-btn">
                    <i class="fas fa-list"></i>
                </button>
            </div>

            <!-- 網格視圖 -->
            <div class="favorites-grid" id="favorites-grid">
                <!-- 收藏項目將動態生成 -->
            </div>

            <!-- 列表視圖 -->
            <div class="favorites-list" id="favorites-list">
                <!-- 收藏項目將動態生成 -->
            </div>

            <!-- 空狀態 -->
            <div class="empty-state" id="empty-state" style="display: none;">
                <div class="empty-icon">
                    <i class="fas fa-heart-broken"></i>
                </div>
                <h3 class="empty-title">還沒有收藏任何文章</h3>
                <p class="empty-description">開始探索澄源閱讀的精彩內容，<br>將您喜愛的文章加入收藏吧！</p>
                <button class="empty-action" id="explore-btn">
                    <i class="fas fa-compass"></i>
                    開始探索
                </button>
            </div>
        </section>
    </div>

    <script>
        // 模擬收藏數據
        let favoritesData = JSON.parse(localStorage.getItem('favorites') || '[]');

        // 如果沒有收藏數據，添加一些示例
        if (favoritesData.length === 0) {
            favoritesData = [
                {
                    id: 1,
                    title: "藏傳佛教中的慈悲修持法門",
                    category: "dharma",
                    categoryName: "佛學智慧",
                    excerpt: "慈悲心是藏傳佛教修行的核心，也是成就菩薩道的根本。本文將深入探討如何在日常生活中培養真正的慈悲心...",
                    author: "釋智慧",
                    date: "2025-01-10",
                    favoriteDate: "2025-01-12",
                    icon: "fas fa-dharmachakra"
                },
                {
                    id: 2,
                    title: "正念冥想對現代人壓力緩解的應用",
                    category: "healing",
                    categoryName: "身心療癒",
                    excerpt: "結合科學研究與傳統修行，探討正念冥想如何有效緩解現代生活中的各種壓力...",
                    author: "Dr. 陳明",
                    date: "2025-01-08",
                    favoriteDate: "2025-01-11",
                    icon: "fas fa-spa"
                },
                {
                    id: 3,
                    title: "冥想對大腦神經可塑性的影響研究",
                    category: "research",
                    categoryName: "最新研究",
                    excerpt: "最新神經科學研究顯示，長期冥想練習能夠改變大腦結構，提升認知能力...",
                    author: "Dr. 王研究",
                    date: "2025-01-05",
                    favoriteDate: "2025-01-10",
                    icon: "fas fa-microscope"
                }
            ];
            localStorage.setItem('favorites', JSON.stringify(favoritesData));
        }

        let filteredData = [...favoritesData];
        let selectedItems = new Set();
        let currentView = 'grid';

        // 更新統計
        function updateStats() {
            document.getElementById('total-count').textContent = favoritesData.length;
            document.getElementById('dharma-count').textContent = 
                favoritesData.filter(item => item.category === 'dharma').length;
            document.getElementById('healing-count').textContent = 
                favoritesData.filter(item => item.category === 'healing').length;
            document.getElementById('research-count').textContent = 
                favoritesData.filter(item => item.category === 'research').length;
        }

        // 篩選和排序
        function filterAndSort() {
            const category = document.getElementById('category-filter').value;
            const sort = document.getElementById('sort-filter').value;
            const search = document.getElementById('search-input').value.toLowerCase();

            // 篩選
            filteredData = favoritesData.filter(item => {
                const matchCategory = !category || item.category === category;
                const matchSearch = !search || 
                    item.title.toLowerCase().includes(search) ||
                    item.excerpt.toLowerCase().includes(search);
                return matchCategory && matchSearch;
            });

            // 排序
            filteredData.sort((a, b) => {
                switch(sort) {
                    case 'title':
                        return a.title.localeCompare(b.title);
                    case 'category':
                        return a.categoryName.localeCompare(b.categoryName);
                    case 'date':
                    default:
                        return new Date(b.favoriteDate) - new Date(a.favoriteDate);
                }
            });

            renderContent();
        }

        // 渲染內容
        function renderContent() {
            if (filteredData.length === 0) {
                document.getElementById('favorites-grid').style.display = 'none';
                document.getElementById('favorites-list').style.display = 'none';
                document.getElementById('empty-state').style.display = 'block';
                return;
            }

            document.getElementById('empty-state').style.display = 'none';

            if (currentView === 'grid') {
                renderGridView();
            } else {
                renderListView();
            }
        }

        // 渲染網格視圖
        function renderGridView() {
            const grid = document.getElementById('favorites-grid');
            const list = document.getElementById('favorites-list');
            
            grid.style.display = 'grid';
            list.style.display = 'none';

            grid.innerHTML = filteredData.map(item => `
                <div class="favorite-card ${selectedItems.has(item.id) ? 'selected' : ''}" data-id="${item.id}">
                    <input type="checkbox" class="card-checkbox" ${selectedItems.has(item.id) ? 'checked' : ''}>
                    <div class="card-image">
                        <i class="${item.icon}"></i>
                        <div class="card-category">${item.categoryName}</div>
                    </div>
                    <div class="card-content">
                        <h3 class="card-title">${item.title}</h3>
                        <p class="card-excerpt">${item.excerpt}</p>
                        <div class="card-meta">
                            <span class="card-date">
                                <i class="fas fa-calendar"></i>
                                ${item.favoriteDate}
                            </span>
                            <div class="card-actions">
                                <button class="action-btn" title="分享" onclick="shareItem(${item.id})">
                                    <i class="fas fa-share"></i>
                                </button>
                                <button class="action-btn" title="移除收藏" onclick="removeFromFavorites(${item.id})">
                                    <i class="fas fa-heart-broken"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 渲染列表視圖
        function renderListView() {
            const grid = document.getElementById('favorites-grid');
            const list = document.getElementById('favorites-list');
            
            grid.style.display = 'none';
            list.style.display = 'flex';

            list.innerHTML = filteredData.map(item => `
                <div class="favorite-item" data-id="${item.id}">
                    <input type="checkbox" class="item-checkbox" ${selectedItems.has(item.id) ? 'checked' : ''}>
                    <div class="item-icon">
                        <i class="${item.icon}"></i>
                    </div>
                    <div class="item-content">
                        <div class="item-title">${item.title}</div>
                        <div class="item-meta">${item.categoryName} • ${item.author} • 收藏於 ${item.favoriteDate}</div>
                    </div>
                    <div class="item-actions">
                        <button class="action-btn" title="分享" onclick="shareItem(${item.id})">
                            <i class="fas fa-share"></i>
                        </button>
                        <button class="action-btn" title="移除收藏" onclick="removeFromFavorites(${item.id})">
                            <i class="fas fa-heart-broken"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 切換視圖
        function switchView(view) {
            currentView = view;
            
            document.querySelectorAll('.view-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(view + '-view-btn').classList.add('active');
            
            renderContent();
        }

        // 選擇項目
        function toggleSelection(id, checkbox) {
            if (checkbox.checked) {
                selectedItems.add(id);
            } else {
                selectedItems.delete(id);
            }
            
            updateSelectionUI();
        }

        // 更新選擇UI
        function updateSelectionUI() {
            const deleteBtn = document.getElementById('delete-btn');
            const selectAllBtn = document.getElementById('select-all-btn');
            
            if (selectedItems.size > 0) {
                deleteBtn.style.display = 'inline-block';
                deleteBtn.textContent = `刪除選中 (${selectedItems.size})`;
                selectAllBtn.textContent = '取消全選';
            } else {
                deleteBtn.style.display = 'none';
                selectAllBtn.textContent = '全選';
            }

            // 更新卡片狀態
            document.querySelectorAll('.favorite-card, .favorite-item').forEach(card => {
                const id = parseInt(card.getAttribute('data-id'));
                const checkbox = card.querySelector('input[type="checkbox"]');
                
                if (selectedItems.has(id)) {
                    card.classList.add('selected');
                    checkbox.checked = true;
                } else {
                    card.classList.remove('selected');
                    checkbox.checked = false;
                }
            });
        }

        // 全選/取消全選
        function toggleSelectAll() {
            if (selectedItems.size > 0) {
                selectedItems.clear();
            } else {
                filteredData.forEach(item => selectedItems.add(item.id));
            }
            updateSelectionUI();
        }

        // 刪除選中項目
        function deleteSelected() {
            if (selectedItems.size === 0) return;

            if (confirm(`確定要移除 ${selectedItems.size} 個收藏項目嗎？`)) {
                favoritesData = favoritesData.filter(item => !selectedItems.has(item.id));
                localStorage.setItem('favorites', JSON.stringify(favoritesData));
                
                selectedItems.clear();
                filterAndSort();
                updateStats();
                
                showNotification(`已移除 ${selectedItems.size} 個收藏項目`);
            }
        }

        // 移除單個收藏
        function removeFromFavorites(id) {
            if (confirm('確定要移除這個收藏嗎？')) {
                favoritesData = favoritesData.filter(item => item.id !== id);
                localStorage.setItem('favorites', JSON.stringify(favoritesData));
                
                selectedItems.delete(id);
                filterAndSort();
                updateStats();
                
                showNotification('已移除收藏');
            }
        }

        // 分享項目
        function shareItem(id) {
            const item = favoritesData.find(item => item.id === id);
            if (item) {
                const shareText = `${item.title} - 來自澄源閱讀`;
                
                if (navigator.share) {
                    navigator.share({
                        title: item.title,
                        text: shareText,
                        url: window.location.origin
                    });
                } else {
                    navigator.clipboard.writeText(shareText).then(() => {
                        showNotification('分享鏈接已複製到剪貼板');
                    });
                }
            }
        }

        // 導出收藏
        function exportFavorites() {
            const exportData = {
                exportDate: new Date().toISOString(),
                totalCount: favoritesData.length,
                favorites: favoritesData
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });
            
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `澄源閱讀收藏_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            
            URL.revokeObjectURL(url);
            showNotification('收藏數據已導出');
        }

        // 事件監聽
        document.getElementById('category-filter').addEventListener('change', filterAndSort);
        document.getElementById('sort-filter').addEventListener('change', filterAndSort);
        document.getElementById('search-input').addEventListener('input', filterAndSort);

        document.getElementById('grid-view-btn').addEventListener('click', () => switchView('grid'));
        document.getElementById('list-view-btn').addEventListener('click', () => switchView('list'));

        document.getElementById('select-all-btn').addEventListener('click', toggleSelectAll);
        document.getElementById('delete-btn').addEventListener('click', deleteSelected);
        document.getElementById('export-btn').addEventListener('click', exportFavorites);

        document.getElementById('explore-btn').addEventListener('click', function() {
            if (window.parent !== window) {
                window.parent.postMessage({
                    type: 'navigate',
                    page: 'home'
                }, '*');
            }
        });

        // 卡片點擊事件
        document.addEventListener('click', function(e) {
            const checkbox = e.target.closest('input[type="checkbox"]');
            if (checkbox) {
                const card = checkbox.closest('.favorite-card, .favorite-item');
                const id = parseInt(card.getAttribute('data-id'));
                toggleSelection(id, checkbox);
                return;
            }

            const card = e.target.closest('.favorite-card, .favorite-item');
            if (card && !e.target.closest('.action-btn')) {
                const id = parseInt(card.getAttribute('data-id'));
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'navigate',
                        page: 'article',
                        articleId: id
                    }, '*');
                }
            }
        });

        // 通知函數
        function showNotification(message) {
            if (window.parent !== window) {
                window.parent.postMessage({
                    type: 'notification',
                    message: message,
                    level: 'info'
                }, '*');
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            filterAndSort();
        });

        // 監聽收藏狀態變化
        window.addEventListener('storage', function(e) {
            if (e.key === 'favorites') {
                favoritesData = JSON.parse(e.newValue || '[]');
                updateStats();
                filterAndSort();
            }
        });

        // 鍵盤快捷鍵
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'a':
                        e.preventDefault();
                        toggleSelectAll();
                        break;
                    case 'Delete':
                    case 'Backspace':
                        if (selectedItems.size > 0) {
                            e.preventDefault();
                            deleteSelected();
                        }
                        break;
                }
            }
        });
    </script>
</body>
</html>