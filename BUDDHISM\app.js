const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
const compression = require('compression');
const path = require('path');
require('dotenv').config();

const app = express();

// 安全中間件
app.use(helmet());
app.use(cors({
    origin: process.env.NODE_ENV === 'production' 
        ? ['https://your-domain.com'] 
        : ['http://localhost:3000', 'http://127.0.0.1:5500'],
    credentials: true
}));

// 基本中間件
app.use(compression());
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 速率限制
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分鐘
    max: 100, // 每個IP限制100次請求
    message: {
        error: 'Too many requests from this IP, please try again later.'
    }
});

const loginLimiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 5, // 登入限制更嚴格
    skipSuccessfulRequests: true
});

app.use('/api/', limiter);
app.use('/api/auth/login', loginLimiter);

// 靜態文件服務
app.use(express.static(path.join(__dirname, 'public')));

// 資料庫連接
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/buddhist_healing', {
    useNewUrlParser: true,
    useUnifiedTopology: true,
})
.then(() => console.log('MongoDB 連接成功'))
.catch(err => console.error('MongoDB 連接失敗:', err));

// 導入路由
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const membershipRoutes = require('./routes/membership');
const coursesRoutes = require('./routes/courses');
const scripturesRoutes = require('./routes/scriptures');
const researchRoutes = require('./routes/research');
const tibetanRoutes = require('./routes/tibetan');
const overseasRoutes = require('./routes/overseas');
const workshopsRoutes = require('./routes/workshops');
const cmsRoutes = require('./routes/cms');

// 使用路由
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/membership', membershipRoutes);
app.use('/api/courses', coursesRoutes);
app.use('/api/scriptures', scripturesRoutes);
app.use('/api/research', researchRoutes);
app.use('/api/tibetan', tibetanRoutes);
app.use('/api/overseas', overseasRoutes);
app.use('/api/workshops', workshopsRoutes);
app.use('/api/cms', cmsRoutes);

// 健康檢查端點
app.get('/health', async (req, res) => {
    const healthCheck = {
        uptime: process.uptime(),
        timestamp: Date.now(),
        status: 'OK',
        services: {
            database: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
            memory: process.memoryUsage(),
            version: process.version
        }
    };
    
    try {
        // 檢查資料庫連接
        await mongoose.connection.db.admin().ping();
        healthCheck.services.database = 'healthy';
    } catch (error) {
        healthCheck.services.database = 'unhealthy';
        healthCheck.status = 'ERROR';
    }
    
    const statusCode = healthCheck.status === 'OK' ? 200 : 503;
    res.status(statusCode).json(healthCheck);
});

// API文檔路由
app.get('/api/docs', (req, res) => {
    res.json({
        title: '佛教身心療癒網站 API',
        version: '1.0.0',
        description: '完整的後端API文檔',
        endpoints: {
            auth: {
                'POST /api/auth/register': '用戶註冊',
                'POST /api/auth/login': '用戶登入',
                'POST /api/auth/logout': '用戶登出',
                'POST /api/auth/refresh': '刷新token',
                'POST /api/auth/forgot-password': '忘記密碼',
                'POST /api/auth/reset-password': '重置密碼'
            },
            users: {
                'GET /api/users/profile': '獲取用戶資料',
                'PUT /api/users/profile': '更新用戶資料',
                'GET /api/users/progress': '學習進度',
                'PUT /api/users/progress': '更新進度'
            },
            membership: {
                'GET /api/membership/plans': '獲取會員方案',
                'POST /api/membership/subscribe': '訂閱會員',
                'PUT /api/membership/upgrade': '升級會員',
                'DELETE /api/membership/cancel': '取消會員'
            },
            courses: {
                'GET /api/courses': '獲取課程列表',
                'GET /api/courses/:id': '獲取特定課程',
                'POST /api/courses/:id/enroll': '報名課程'
            },
            scriptures: {
                'GET /api/scriptures/search': '搜尋經文',
                'GET /api/scriptures/:id': '獲取特定經文',
                'POST /api/scriptures/bookmark': '收藏經文'
            },
            workshops: {
                'GET /api/workshops': '工作坊列表',
                'POST /api/workshops/register': '報名工作坊'
            }
        }
    });
});

// 首頁路由 - 提供HTML頁面
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// 404處理
app.use('*', (req, res) => {
    if (req.originalUrl.startsWith('/api/')) {
        res.status(404).json({
            error: 'API endpoint not found',
            message: `The endpoint ${req.originalUrl} does not exist`,
            availableEndpoints: '/api/docs'
        });
    } else {
        res.status(404).sendFile(path.join(__dirname, '404.html'));
    }
});

// 全局錯誤處理
app.use((err, req, res, next) => {
    console.error('Error:', err);
    
    // Mongoose驗證錯誤
    if (err.name === 'ValidationError') {
        const errors = Object.values(err.errors).map(e => e.message);
        return res.status(400).json({
            error: 'Validation Error',
            message: 'Data validation failed',
            details: errors
        });
    }
    
    // JWT錯誤
    if (err.name === 'JsonWebTokenError') {
        return res.status(401).json({
            error: 'Invalid Token',
            message: 'Please login again'
        });
    }
    
    // MongoDB重複鍵錯誤
    if (err.code === 11000) {
        const field = Object.keys(err.keyValue)[0];
        return res.status(409).json({
            error: 'Duplicate Entry',
            message: `${field} already exists`
        });
    }
    
    // 預設錯誤
    res.status(err.status || 500).json({
        error: err.message || 'Internal Server Error',
        message: process.env.NODE_ENV === 'production' 
            ? 'Something went wrong' 
            : err.stack
    });
});

// 正常關閉處理
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    mongoose.connection.close(() => {
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    mongoose.connection.close(() => {
        process.exit(0);
    });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`🚀 佛教身心療癒網站後端服務器運行在端口 ${PORT}`);
    console.log(`📚 API文檔: http://localhost:${PORT}/api/docs`);
    console.log(`🏥 健康檢查: http://localhost:${PORT}/health`);
    console.log(`🌐 網站首頁: http://localhost:${PORT}`);
});

module.exports = app;