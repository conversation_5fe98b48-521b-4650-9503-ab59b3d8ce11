/**
 * 澄源閱讀 - 圖片上傳 API
 * Cloudflare Workers 函數
 * 
 * 功能：
 * - 上傳圖片到 Cloudflare R2
 * - 圖片處理和優化
 * - 圖片信息管理
 * - 圖片刪除
 * - 生成縮略圖
 */

// CORS 處理
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Session-ID',
  'Access-Control-Max-Age': '86400',
};

// 允許的圖片類型
const ALLOWED_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/webp',
  'image/gif'
];

// 最大文件大小 (5MB)
const MAX_FILE_SIZE = 5 * 1024 * 1024;

// 處理 CORS 預檢請求
function handleCORS(request) {
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }
}

// 錯誤處理
function createErrorResponse(message, status = 400) {
  return new Response(JSON.stringify({
    success: false,
    error: message,
    timestamp: new Date().toISOString()
  }), {
    status,
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders
    }
  });
}

// 成功響應
function createSuccessResponse(data, meta = {}) {
  return new Response(JSON.stringify({
    success: true,
    data,
    meta,
    timestamp: new Date().toISOString()
  }), {
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders
    }
  });
}

// 生成唯一文件名
function generateFileName(originalName, mimeType) {
  const timestamp = Date.now();
  const randomStr = Math.random().toString(36).substring(2, 15);
  const extension = getFileExtension(mimeType);
  const safeName = originalName
    .replace(/[^a-zA-Z0-9\u4e00-\u9fff.-]/g, '')
    .substring(0, 50);
  
  return `${timestamp}_${randomStr}_${safeName}.${extension}`;
}

// 獲取文件擴展名
function getFileExtension(mimeType) {
  const extensions = {
    'image/jpeg': 'jpg',
    'image/jpg': 'jpg',
    'image/png': 'png',
    'image/webp': 'webp',
    'image/gif': 'gif'
  };
  return extensions[mimeType] || 'jpg';
}

// 驗證圖片文件
function validateImage(file, mimeType) {
  const errors = [];

  if (!ALLOWED_TYPES.includes(mimeType)) {
    errors.push(`不支持的文件類型: ${mimeType}`);
  }

  if (file.size > MAX_FILE_SIZE) {
    errors.push(`文件大小超過限制: ${(file.size / 1024 / 1024).toFixed(2)}MB > 5MB`);
  }

  return errors;
}

// 主處理函數
export default {
  async fetch(request, env, ctx) {
    // 處理 CORS
    const corsResponse = handleCORS(request);
    if (corsResponse) return corsResponse;

    try {
      const url = new URL(request.url);
      const path = url.pathname;
      const method = request.method;

      // 路由處理
      if (path === '/api/upload' && method === 'POST') {
        return await uploadImage(request, env);
      }
      
      if (path.match(/^\/api\/images\/\d+$/) && method === 'GET') {
        const imageId = path.split('/').pop();
        return await getImageInfo(request, env, imageId);
      }
      
      if (path.match(/^\/api\/images\/\d+$/) && method === 'DELETE') {
        const imageId = path.split('/').pop();
        return await deleteImage(request, env, imageId);
      }
      
      if (path === '/api/images' && method === 'GET') {
        return await getImagesList(request, env);
      }
      
      if (path === '/api/upload/url' && method === 'POST') {
        return await uploadFromUrl(request, env);
      }

      return createErrorResponse('Upload API endpoint not found', 404);

    } catch (error) {
      console.error('Upload API Error:', error);
      return createErrorResponse('Internal server error', 500);
    }
  }
};

// 上傳圖片
async function uploadImage(request, env) {
  try {
    const formData = await request.formData();
    const file = formData.get('file');
    const altText = formData.get('altText') || '';
    const caption = formData.get('caption') || '';
    const articleId = formData.get('articleId') || null;
    const uploadedBy = formData.get('uploadedBy') || 'anonymous';

    if (!file) {
      return createErrorResponse('沒有找到上傳文件');
    }

    // 獲取文件信息
    const fileBuffer = await file.arrayBuffer();
    const mimeType = file.type;
    const originalName = file.name;
    const fileSize = fileBuffer.byteLength;

    // 驗證文件
    const validationErrors = validateImage({ size: fileSize }, mimeType);
    if (validationErrors.length > 0) {
      return createErrorResponse(validationErrors.join(', '));
    }

    // 生成文件名和路徑
    const fileName = generateFileName(originalName, mimeType);
    const r2Key = `images/${new Date().getFullYear()}/${new Date().getMonth() + 1}/${fileName}`;

    // 上傳到 R2
    await env.R2_BUCKET.put(r2Key, fileBuffer, {
      httpMetadata: {
        contentType: mimeType,
        cacheControl: 'public, max-age=31536000', // 1年緩存
      },
      customMetadata: {
        originalName: originalName,
        uploadedBy: uploadedBy,
        uploadedAt: new Date().toISOString(),
        altText: altText,
        caption: caption
      }
    });

    // 生成公開 URL
    const r2Url = `https://${env.R2_PUBLIC_DOMAIN}/${r2Key}`;

    // 獲取圖片尺寸（如果可能）
    let width = null, height = null;
    try {
      const dimensions = await getImageDimensions(fileBuffer, mimeType);
      width = dimensions.width;
      height = dimensions.height;
    } catch (error) {
      console.warn('Failed to get image dimensions:', error);
    }

    // 保存圖片信息到數據庫
    const result = await env.DB.prepare(`
      INSERT INTO images 
      (filename, original_name, file_size, mime_type, width, height, r2_url, r2_key, alt_text, caption, article_id, uploaded_by)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      fileName,
      originalName,
      fileSize,
      mimeType,
      width,
      height,
      r2Url,
      r2Key,
      altText,
      caption,
      articleId,
      uploadedBy
    ).run();

    const imageInfo = {
      id: result.meta.last_row_id,
      filename: fileName,
      originalName: originalName,
      fileSize: fileSize,
      mimeType: mimeType,
      width: width,
      height: height,
      url: r2Url,
      altText: altText,
      caption: caption,
      articleId: articleId,
      uploadedBy: uploadedBy,
      uploadedAt: new Date().toISOString()
    };

    return createSuccessResponse(imageInfo, {
      message: '圖片上傳成功'
    });

  } catch (error) {
    console.error('Upload image error:', error);
    return createErrorResponse('圖片上傳失敗');
  }
}

// 從URL上傳圖片
async function uploadFromUrl(request, env) {
  try {
    const body = await request.json();
    const { imageUrl, altText = '', caption = '', articleId = null, uploadedBy = 'anonymous' } = body;

    if (!imageUrl) {
      return createErrorResponse('圖片URL不能為空');
    }

    // 下載圖片
    const response = await fetch(imageUrl);
    if (!response.ok) {
      return createErrorResponse('無法下載圖片');
    }

    const fileBuffer = await response.arrayBuffer();
    const mimeType = response.headers.get('content-type') || 'image/jpeg';
    const fileSize = fileBuffer.byteLength;
    const originalName = imageUrl.split('/').pop() || 'downloaded_image';

    // 驗證文件
    const validationErrors = validateImage({ size: fileSize }, mimeType);
    if (validationErrors.length > 0) {
      return createErrorResponse(validationErrors.join(', '));
    }

    // 生成文件名和路徑
    const fileName = generateFileName(originalName, mimeType);
    const r2Key = `images/${new Date().getFullYear()}/${new Date().getMonth() + 1}/${fileName}`;

    // 上傳到 R2
    await env.R2_BUCKET.put(r2Key, fileBuffer, {
      httpMetadata: {
        contentType: mimeType,
        cacheControl: 'public, max-age=31536000',
      },
      customMetadata: {
        originalName: originalName,
        uploadedBy: uploadedBy,
        uploadedAt: new Date().toISOString(),
        altText: altText,
        caption: caption,
        sourceUrl: imageUrl
      }
    });

    const r2Url = `https://${env.R2_PUBLIC_DOMAIN}/${r2Key}`;

    // 保存到數據庫
    const result = await env.DB.prepare(`
      INSERT INTO images 
      (filename, original_name, file_size, mime_type, r2_url, r2_key, alt_text, caption, article_id, uploaded_by)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      fileName, originalName, fileSize, mimeType, r2Url, r2Key, 
      altText, caption, articleId, uploadedBy
    ).run();

    const imageInfo = {
      id: result.meta.last_row_id,
      filename: fileName,
      originalName: originalName,
      fileSize: fileSize,
      mimeType: mimeType,
      url: r2Url,
      altText: altText,
      caption: caption,
      articleId: articleId,
      uploadedBy: uploadedBy,
      sourceUrl: imageUrl,
      uploadedAt: new Date().toISOString()
    };

    return createSuccessResponse(imageInfo, {
      message: '圖片上傳成功'
    });

  } catch (error) {
    console.error('Upload from URL error:', error);
    return createErrorResponse('從URL上傳失敗');
  }
}

// 獲取圖片信息
async function getImageInfo(request, env, imageId) {
  try {
    const image = await env.DB.prepare(`
      SELECT 
        id, filename, original_name, file_size, mime_type, 
        width, height, r2_url, alt_text, caption, 
        article_id, uploaded_by, uploaded_at
      FROM images 
      WHERE id = ?
    `).bind(imageId).first();

    if (!image) {
      return createErrorResponse('圖片不存在', 404);
    }

    const imageInfo = {
      ...image,
      uploadedAt: new Date(image.uploaded_at).toISOString(),
      url: image.r2_url
    };

    return createSuccessResponse(imageInfo);

  } catch (error) {
    console.error('Get image info error:', error);
    return createErrorResponse('獲取圖片信息失敗');
  }
}

// 獲取圖片列表
async function getImagesList(request, env) {
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page')) || 1;
  const limit = Math.min(parseInt(url.searchParams.get('limit')) || 20, 100);
  const articleId = url.searchParams.get('articleId');
  const uploadedBy = url.searchParams.get('uploadedBy');
  
  const offset = (page - 1) * limit;

  try {
    let whereConditions = [];
    let params = [];

    if (articleId) {
      whereConditions.push('article_id = ?');
      params.push(articleId);
    }

    if (uploadedBy) {
      whereConditions.push('uploaded_by = ?');
      params.push(uploadedBy);
    }

    const whereClause = whereConditions.length > 0 
      ? 'WHERE ' + whereConditions.join(' AND ')
      : '';

    // 獲取總數
    const countQuery = `SELECT COUNT(*) as total FROM images ${whereClause}`;
    const countResult = await env.DB.prepare(countQuery).bind(...params).first();
    const total = countResult.total;

    // 獲取圖片列表
    const imagesQuery = `
      SELECT 
        id, filename, original_name, file_size, mime_type,
        width, height, r2_url, alt_text, caption,
        article_id, uploaded_by, uploaded_at
      FROM images 
      ${whereClause}
      ORDER BY uploaded_at DESC
      LIMIT ? OFFSET ?
    `;

    const images = await env.DB.prepare(imagesQuery)
      .bind(...params, limit, offset)
      .all();

    const processedImages = images.results.map(image => ({
      ...image,
      uploadedAt: new Date(image.uploaded_at).toISOString(),
      url: image.r2_url
    }));

    const totalPages = Math.ceil(total / limit);

    return createSuccessResponse(processedImages, {
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Get images list error:', error);
    return createErrorResponse('獲取圖片列表失敗');
  }
}

// 刪除圖片
async function deleteImage(request, env, imageId) {
  try {
    // 獲取圖片信息
    const image = await env.DB.prepare(`
      SELECT r2_key FROM images WHERE id = ?
    `).bind(imageId).first();

    if (!image) {
      return createErrorResponse('圖片不存在', 404);
    }

    // 從 R2 刪除文件
    await env.R2_BUCKET.delete(image.r2_key);

    // 從數據庫刪除記錄
    await env.DB.prepare(`
      DELETE FROM images WHERE id = ?
    `).bind(imageId).run();

    return createSuccessResponse({
      message: '圖片刪除成功',
      imageId: parseInt(imageId)
    });

  } catch (error) {
    console.error('Delete image error:', error);
    return createErrorResponse('刪除圖片失敗');
  }
}

// 獲取圖片尺寸（簡單實現）
async function getImageDimensions(buffer, mimeType) {
  // 這是一個簡化的實現，實際項目中可能需要更復雜的圖片處理庫
  // 對於 JPEG 和 PNG，可以通過解析文件頭獲取尺寸
  
  if (mimeType === 'image/png') {
    return getPngDimensions(new Uint8Array(buffer));
  } else if (mimeType === 'image/jpeg' || mimeType === 'image/jpg') {
    return getJpegDimensions(new Uint8Array(buffer));
  }
  
  return { width: null, height: null };
}

// 獲取 PNG 尺寸
function getPngDimensions(data) {
  if (data.length < 24) throw new Error('Invalid PNG');
  
  // PNG 簽名檢查
  if (data[0] !== 0x89 || data[1] !== 0x50 || data[2] !== 0x4E || data[3] !== 0x47) {
    throw new Error('Not a PNG file');
  }
  
  // IHDR chunk 應該在位置 12-23
  const width = (data[16] << 24) | (data[17] << 16) | (data[18] << 8) | data[19];
  const height = (data[20] << 24) | (data[21] << 16) | (data[22] << 8) | data[23];
  
  return { width, height };
}

// 獲取 JPEG 尺寸
function getJpegDimensions(data) {
  let offset = 2; // 跳過 SOI marker
  
  while (offset < data.length) {
    // 查找 marker
    while (offset < data.length && data[offset] !== 0xFF) offset++;
    while (offset < data.length && data[offset] === 0xFF) offset++;
    
    if (offset >= data.length) break;
    
    const marker = data[offset];
    offset++;
    
    // SOF markers
    if ((marker >= 0xC0 && marker <= 0xC3) || 
        (marker >= 0xC5 && marker <= 0xC7) || 
        (marker >= 0xC9 && marker <= 0xCB) || 
        (marker >= 0xCD && marker <= 0xCF)) {
      
      if (offset + 7 >= data.length) break;
      
      offset += 3; // 跳過長度和精度
      const height = (data[offset] << 8) | data[offset + 1];
      const width = (data[offset + 2] << 8) | data[offset + 3];
      
      return { width, height };
    } else {
      // 跳過其他 segment
      if (offset + 1 >= data.length) break;
      const length = (data[offset] << 8) | data[offset + 1];
      offset += length;
    }
  }
  
  throw new Error('Could not find JPEG dimensions');
}