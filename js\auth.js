/**
 * 澄源閱讀 - 認證管理模組
 * 處理用戶登錄、權限驗證和會話管理
 */

class AuthManager {
    constructor() {
        this.isInitialized = false;
        this.currentUser = null;
        this.permissions = new Set();
        this.sessionTimeout = 24 * 60 * 60 * 1000; // 24小時
        this.validPasswords = [
            'chengyuan2025',
            'dharma123', 
            'healing2025',
            'buddha@wisdom',
            'mindfulness2025'
        ];
        
        this.init();
    }

    /**
     * 初始化認證管理器
     */
    init() {
        if (this.isInitialized) return;
        
        this.loadStoredAuth();
        this.setupSessionCheck();
        this.bindEvents();
        
        this.isInitialized = true;
        console.log('AuthManager initialized');
    }

    /**
     * 從本地存儲載入認證狀態
     */
    loadStoredAuth() {
        try {
            const authData = Utils.Storage.get('auth');
            const sessionData = Utils.Storage.get('session', null, true);
            
            if (authData && this.isValidSession(authData)) {
                this.currentUser = authData.user;
                this.permissions = new Set(authData.permissions || []);
                this.updateLastActivity();
                
                // 觸發登錄成功事件
                this.dispatchAuthEvent('login_success', { user: this.currentUser });
            } else {
                // 清除無效的認證數據
                this.clearAuth();
            }
        } catch (error) {
            console.error('Error loading stored auth:', error);
            this.clearAuth();
        }
    }

    /**
     * 檢查會話是否有效
     * @param {Object} authData - 認證數據
     * @returns {boolean}
     */
    isValidSession(authData) {
        if (!authData || !authData.timestamp) return false;
        
        const now = Date.now();
        const sessionAge = now - authData.timestamp;
        
        return sessionAge < this.sessionTimeout;
    }

    /**
     * 密碼登錄
     * @param {string} password - 密碼
     * @returns {Promise<Object>}
     */
    async login(password) {
        return new Promise((resolve, reject) => {
            // 模擬網路延遲
            setTimeout(() => {
                if (this.validatePassword(password)) {
                    const userData = this.createUserSession(password);
                    this.setAuthData(userData);
                    
                    this.dispatchAuthEvent('login_success', { user: userData });
                    resolve({
                        success: true,
                        message: '登錄成功',
                        user: userData
                    });
                } else {
                    this.dispatchAuthEvent('login_failed', { reason: 'invalid_password' });
                    reject({
                        success: false,
                        message: '密碼錯誤，請重新輸入',
                        code: 'INVALID_PASSWORD'
                    });
                }
            }, 800); // 模擬網路延遲
        });
    }

    /**
     * 驗證密碼
     * @param {string} password - 輸入的密碼
     * @returns {boolean}
     */
    validatePassword(password) {
        if (!password || typeof password !== 'string') {
            return false;
        }
        
        // 移除前後空格並檢查
        const trimmedPassword = password.trim();
        return this.validPasswords.includes(trimmedPassword);
    }

    /**
     * 創建用戶會話
     * @param {string} password - 驗證成功的密碼
     * @returns {Object}
     */
    createUserSession(password) {
        const now = new Date();
        const sessionId = this.generateSessionId();
        
        // 根據密碼分配不同的角色和權限
        let role = 'guest';
        let permissions = ['read'];
        
        if (password === 'chengyuan2025') {
            role = 'admin';
            permissions = ['read', 'write', 'admin'];
        } else if (password === 'dharma123') {
            role = 'editor';
            permissions = ['read', 'write'];
        } else {
            role = 'reader';
            permissions = ['read'];
        }
        
        return {
            id: sessionId,
            role: role,
            permissions: permissions,
            loginTime: now.toISOString(),
            lastActivity: now.toISOString(),
            browser: this.getBrowserInfo(),
            deviceInfo: this.getDeviceInfo()
        };
    }

    /**
     * 生成會話ID
     * @returns {string}
     */
    generateSessionId() {
        const timestamp = Date.now().toString(36);
        const randomStr = Math.random().toString(36).substr(2, 9);
        return `sess_${timestamp}_${randomStr}`;
    }

    /**
     * 獲取瀏覽器信息
     * @returns {Object}
     */
    getBrowserInfo() {
        const ua = navigator.userAgent;
        return {
            userAgent: ua,
            language: navigator.language,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled,
            onlineStatus: navigator.onLine
        };
    }

    /**
     * 獲取設備信息
     * @returns {Object}
     */
    getDeviceInfo() {
        return {
            screenResolution: `${screen.width}x${screen.height}`,
            viewportSize: `${window.innerWidth}x${window.innerHeight}`,
            pixelRatio: window.devicePixelRatio || 1,
            isMobile: Utils.Device.isMobile(),
            isTablet: Utils.Device.isTablet(),
            isTouchDevice: Utils.Device.isTouchDevice(),
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
        };
    }

    /**
     * 設置認證數據
     * @param {Object} userData - 用戶數據
     */
    setAuthData(userData) {
        this.currentUser = userData;
        this.permissions = new Set(userData.permissions || []);
        
        // 保存到本地存儲
        const authData = {
            user: userData,
            permissions: userData.permissions,
            timestamp: Date.now()
        };
        
        Utils.Storage.set('auth', authData);
        Utils.Storage.set('session', {
            id: userData.id,
            lastActivity: userData.lastActivity
        }, true);
        
        this.updateLastActivity();
    }

    /**
     * 更新最後活動時間
     */
    updateLastActivity() {
        if (!this.currentUser) return;
        
        const now = new Date().toISOString();
        this.currentUser.lastActivity = now;
        
        // 更新存儲中的數據
        const authData = Utils.Storage.get('auth');
        if (authData) {
            authData.user.lastActivity = now;
            authData.timestamp = Date.now();
            Utils.Storage.set('auth', authData);
        }
        
        Utils.Storage.set('session', {
            id: this.currentUser.id,
            lastActivity: now
        }, true);
    }

    /**
     * 登出
     */
    logout() {
        const wasLoggedIn = this.isLoggedIn();
        
        this.clearAuth();
        
        if (wasLoggedIn) {
            this.dispatchAuthEvent('logout', { 
                reason: 'user_logout',
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * 清除認證數據
     */
    clearAuth() {
        this.currentUser = null;
        this.permissions = new Set();
        
        Utils.Storage.remove('auth');
        Utils.Storage.remove('session', true);
    }

    /**
     * 檢查是否已登錄
     * @returns {boolean}
     */
    isLoggedIn() {
        return this.currentUser !== null && this.isValidSession({
            timestamp: Utils.Storage.get('auth')?.timestamp
        });
    }

    /**
     * 檢查用戶權限
     * @param {string} permission - 權限名稱
     * @returns {boolean}
     */
    hasPermission(permission) {
        return this.permissions.has(permission);
    }

    /**
     * 檢查用戶角色
     * @param {string} role - 角色名稱
     * @returns {boolean}
     */
    hasRole(role) {
        return this.currentUser?.role === role;
    }

    /**
     * 獲取當前用戶信息
     * @returns {Object|null}
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * 獲取用戶權限列表
     * @returns {Array}
     */
    getPermissions() {
        return Array.from(this.permissions);
    }

    /**
     * 設置會話檢查
     */
    setupSessionCheck() {
        // 每5分鐘檢查一次會話有效性
        this.sessionCheckInterval = setInterval(() => {
            if (this.isLoggedIn()) {
                this.updateLastActivity();
            } else {
                this.handleSessionExpired();
            }
        }, 5 * 60 * 1000);

        // 在頁面可見性變化時檢查會話
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.isLoggedIn()) {
                this.updateLastActivity();
            }
        });
    }

    /**
     * 處理會話過期
     */
    handleSessionExpired() {
        if (this.currentUser) {
            this.dispatchAuthEvent('session_expired', {
                user: this.currentUser,
                timestamp: new Date().toISOString()
            });
            this.clearAuth();
        }
    }

    /**
     * 綁定事件監聽器
     */
    bindEvents() {
        // 監聽用戶活動
        const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        const updateActivity = Utils.Performance.throttle(() => {
            if (this.isLoggedIn()) {
                this.updateLastActivity();
            }
        }, 30000); // 30秒節流

        activityEvents.forEach(event => {
            document.addEventListener(event, updateActivity, { passive: true });
        });

        // 監聽頁面卸載
        window.addEventListener('beforeunload', () => {
            if (this.isLoggedIn()) {
                this.updateLastActivity();
            }
        });
    }

    /**
     * 觸發認證事件
     * @param {string} eventType - 事件類型
     * @param {Object} data - 事件數據
     */
    dispatchAuthEvent(eventType, data = {}) {
        const event = new CustomEvent(`auth:${eventType}`, {
            detail: { ...data, timestamp: Date.now() }
        });
        document.dispatchEvent(event);
        
        // 同時觸發窗口消息（用於iframe通信）
        if (window.parent !== window) {
            window.parent.postMessage({
                type: eventType,
                ...data
            }, '*');
        }
    }

    /**
     * 驗證訪問權限
     * @param {string} resource - 資源名稱
     * @param {string} action - 操作類型
     * @returns {boolean}
     */
    canAccess(resource, action = 'read') {
        if (!this.isLoggedIn()) return false;
        
        // 管理員擁有所有權限
        if (this.hasRole('admin')) return true;
        
        // 檢查特定權限
        const requiredPermission = `${resource}:${action}`;
        if (this.hasPermission(requiredPermission)) return true;
        
        // 檢查通用權限
        return this.hasPermission(action);
    }

    /**
     * 獲取認證狀態摘要
     * @returns {Object}
     */
    getAuthSummary() {
        return {
            isLoggedIn: this.isLoggedIn(),
            user: this.currentUser ? {
                id: this.currentUser.id,
                role: this.currentUser.role,
                loginTime: this.currentUser.loginTime,
                lastActivity: this.currentUser.lastActivity
            } : null,
            permissions: this.getPermissions(),
            sessionValid: this.isLoggedIn(),
            sessionAge: this.currentUser ? Date.now() - new Date(this.currentUser.loginTime).getTime() : 0
        };
    }

    /**
     * 銷毀認證管理器
     */
    destroy() {
        if (this.sessionCheckInterval) {
            clearInterval(this.sessionCheckInterval);
        }
        this.clearAuth();
        this.isInitialized = false;
    }
}

// 創建全局認證管理器實例
window.AuthManager = new AuthManager();

// 為了兼容性，也導出到 window.Auth
window.Auth = window.AuthManager;

// 添加一些快捷方法到全局範圍
window.isLoggedIn = () => window.Auth.isLoggedIn();
window.getCurrentUser = () => window.Auth.getCurrentUser();
window.hasPermission = (permission) => window.Auth.hasPermission(permission);
window.hasRole = (role) => window.Auth.hasRole(role);

// 監聽認證事件並提供日誌記錄
document.addEventListener('auth:login_success', (event) => {
    console.log('User logged in:', event.detail);
});

document.addEventListener('auth:logout', (event) => {
    console.log('User logged out:', event.detail);
});

document.addEventListener('auth:session_expired', (event) => {
    console.warn('Session expired:', event.detail);
    
    // 可以在這裡顯示會話過期通知
    if (window.showNotification) {
        window.showNotification('會話已過期，請重新登錄', 'warning');
    }
});

document.addEventListener('auth:login_failed', (event) => {
    console.warn('Login failed:', event.detail);
});

console.log('AuthManager loaded and initialized');