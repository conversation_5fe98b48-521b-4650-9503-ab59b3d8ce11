<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>佛教哲學 - 佛學智慧</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.6;
            color: #333;
            background: transparent;
            padding: 20px;
        }

        /* 分類介紹區 */
        .category-intro {
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            color: white;
            padding: 40px 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .category-intro::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="white" opacity="0.1"/><circle cx="80" cy="80" r="1.5" fill="white" opacity="0.1"/><circle cx="50" cy="60" r="0.8" fill="white" opacity="0.1"/></svg>');
            background-size: 50px 50px;
            animation: philosophy-drift 25s linear infinite;
        }

        @keyframes philosophy-drift {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .intro-content {
            position: relative;
            z-index: 2;
        }

        .intro-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            animation: think-pulse 2s infinite;
        }

        @keyframes think-pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .intro-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .intro-description {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.8;
        }

        /* 哲學學派 */
        .philosophy-schools {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .school-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .school-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .school-header {
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            color: white;
            padding: 25px;
            position: relative;
        }

        .school-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .school-name {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .school-origin {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .school-body {
            padding: 25px;
        }

        .school-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .school-features {
            list-style: none;
            margin-bottom: 20px;
        }

        .school-features li {
            padding: 5px 0;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #555;
            font-size: 0.9rem;
        }

        .school-features i {
            color: #8e44ad;
            font-size: 0.8rem;
        }

        .school-count {
            display: flex;
            justify-content: space-between;
            font-size: 0.8rem;
            color: #999;
            border-top: 1px solid #f0f0f0;
            padding-top: 15px;
        }

        /* 核心概念 */
        .core-concepts {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .concepts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .concept-card {
            background: linear-gradient(135deg, #f8f4ff, #f0e6ff);
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #8e44ad;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .concept-card:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(142, 68, 173, 0.2);
        }

        .concept-icon {
            color: #8e44ad;
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .concept-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .concept-description {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* 經典文獻 */
        .classic-texts {
            background: linear-gradient(135deg, #fff9e6, #fff3cc);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid #ffe066;
        }

        .texts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .text-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .text-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .text-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .text-author {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }

        .text-description {
            color: #555;
            font-size: 0.85rem;
            line-height: 1.5;
        }

        /* 哲學問題 */
        .philosophy-questions {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .questions-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 20px;
        }

        .question-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #8e44ad;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .question-item:hover {
            background: #f0e6ff;
            transform: translateX(5px);
        }

        .question-text {
            font-size: 1rem;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }

        .question-context {
            font-size: 0.85rem;
            color: #666;
            line-height: 1.5;
        }

        /* 相關文章 */
        .articles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .article-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid #e9ecef;
        }

        .article-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-color: #8e44ad;
        }

        .article-icon {
            color: #8e44ad;
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .article-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .article-excerpt {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .article-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: #999;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            .category-intro {
                padding: 30px 20px;
            }

            .intro-title {
                font-size: 1.5rem;
            }

            .philosophy-schools,
            .concepts-grid,
            .texts-grid,
            .articles-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 分類介紹 -->
    <div class="category-intro">
        <div class="intro-content">
            <div class="intro-icon">
                <i class="fas fa-brain"></i>
            </div>
            <h1 class="intro-title">佛教哲學</h1>
            <p class="intro-description">
                探索佛教深奧的哲學思想，從緣起性空到唯識學說，從中觀哲學到如來藏思想，
                深入理解佛陀智慧的理論基礎與哲學內涵。
            </p>
        </div>
    </div>

    <!-- 哲學學派 -->
    <div class="philosophy-schools">
        <div class="school-card" data-school="madhyamika">
            <div class="school-header">
                <div class="school-icon">
                    <i class="fas fa-balance-scale"></i>
                </div>
                <h3 class="school-name">中觀學派</h3>
                <p class="school-origin">龍樹菩薩創立 • 2世紀</p>
            </div>
            <div class="school-body">
                <p class="school-description">
                    以緣起性空為核心思想，破除一切戲論，建立中道哲學體系。
                </p>
                <ul class="school-features">
                    <li><i class="fas fa-check"></i> 緣起性空</li>
                    <li><i class="fas fa-check"></i> 二諦理論</li>
                    <li><i class="fas fa-check"></i> 八不中道</li>
                </ul>
                <div class="school-count">
                    <span>代表人物：龍樹、提婆</span>
                    <span>3 篇文章</span>
                </div>
            </div>
        </div>

        <div class="school-card" data-school="yogacara">
            <div class="school-header">
                <div class="school-icon">
                    <i class="fas fa-eye"></i>
                </div>
                <h3 class="school-name">唯識學派</h3>
                <p class="school-origin">彌勒菩薩、無著創立 • 4世紀</p>
            </div>
            <div class="school-body">
                <p class="school-description">
                    強調心識的作用，建立完整的心理學和認識論體系。
                </p>
                <ul class="school-features">
                    <li><i class="fas fa-check"></i> 八識理論</li>
                    <li><i class="fas fa-check"></i> 三性三無性</li>
                    <li><i class="fas fa-check"></i> 轉識成智</li>
                </ul>
                <div class="school-count">
                    <span>代表人物：彌勒、無著、世親</span>
                    <span>2 篇文章</span>
                </div>
            </div>
        </div>

        <div class="school-card" data-school="tathagatagarbha">
            <div class="school-header">
                <div class="school-icon">
                    <i class="fas fa-gem"></i>
                </div>
                <h3 class="school-name">如來藏思想</h3>
                <p class="school-origin">多部經典 • 3-4世紀</p>
            </div>
            <div class="school-body">
                <p class="school-description">
                    闡述眾生本具佛性，探討覺性的本質和修行的根據。
                </p>
                <ul class="school-features">
                    <li><i class="fas fa-check"></i> 佛性論</li>
                    <li><i class="fas fa-check"></i> 本覺始覺</li>
                    <li><i class="fas fa-check"></i> 真如不變</li>
                </ul>
                <div class="school-count">
                    <span>代表經典：如來藏經、勝鬘經</span>
                    <span>1 篇文章</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 核心概念 -->
    <div class="core-concepts">
        <h2 class="section-title">
            <i class="fas fa-lightbulb"></i>
            核心哲學概念
        </h2>
        <div class="concepts-grid">
            <div class="concept-card" data-concept="dependent-origination">
                <div class="concept-icon">
                    <i class="fas fa-link"></i>
                </div>
                <h3 class="concept-title">緣起（依緣起）</h3>
                <p class="concept-description">
                    萬法皆由因緣和合而生，無獨立自性，體現了佛教的根本世界觀。
                </p>
            </div>
            <div class="concept-card" data-concept="emptiness">
                <div class="concept-icon">
                    <i class="fas fa-circle"></i>
                </div>
                <h3 class="concept-title">空性（śūnyatā）</h3>
                <p class="concept-description">
                    一切法無自性空，不是虛無，而是缺乏獨立自存的本質。
                </p>
            </div>
            <div class="concept-card" data-concept="consciousness-only">
                <div class="concept-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <h3 class="concept-title">唯識無境</h3>
                <p class="concept-description">
                    外境皆由心識變現，強調心識在認知過程中的根本作用。
                </p>
            </div>
            <div class="concept-card" data-concept="buddha-nature">
                <div class="concept-icon">
                    <i class="fas fa-seedling"></i>
                </div>
                <h3 class="concept-title">佛性</h3>
                <p class="concept-description">
                    眾生本具成佛的可能性，是修行解脫的內在根據。
                </p>
            </div>
        </div>
    </div>

    <!-- 經典文獻 -->
    <div class="classic-texts">
        <h2 class="section-title">
            <i class="fas fa-scroll"></i>
            重要哲學文獻
        </h2>
        <div class="texts-grid">
            <div class="text-card" data-text="mulamadhyamakakarika">
                <h3 class="text-title">
                    <i class="fas fa-book"></i>
                    《中論》
                </h3>
                <p class="text-author">龍樹菩薩 著</p>
                <p class="text-description">
                    中觀學派的根本論典，以二十七品詳細闡述緣起性空的哲學思想，破除一切戲論。
                </p>
            </div>
            <div class="text-card" data-text="vimsatika">
                <h3 class="text-title">
                    <i class="fas fa-scroll"></i>
                    《唯識二十論》
                </h3>
                <p class="text-author">世親菩薩 著</p>
                <p class="text-description">
                    唯識學派重要論典，論證外境不存在，一切皆為心識所變現的道理。
                </p>
            </div>
            <div class="text-card" data-text="awakening-of-faith">
                <h3 class="text-title">
                    <i class="fas fa-heart"></i>
                    《大乘起信論》
                </h3>
                <p class="text-author">馬鳴菩薩 著</p>
                <p class="text-description">
                    系統闡述如來藏思想，論述一心二門、真如和生滅的關係。
                </p>
            </div>
        </div>
    </div>

    <!-- 哲學問題 -->
    <div class="philosophy-questions">
        <h2 class="section-title">
            <i class="fas fa-question-circle"></i>
            經典哲學問題
        </h2>
        <div class="questions-list">
            <div class="question-item" data-question="existence">
                <div class="question-text">存在的本質是什麼？</div>
                <div class="question-context">
                    佛教認為一切存在皆是緣起性空，沒有獨立不變的本質，這與其他哲學體系有何不同？
                </div>
            </div>
            <div class="question-item" data-question="consciousness">
                <div class="question-text">心識與物質的關係</div>
                <div class="question-context">
                    唯識學派主張「萬法唯識」，這是否意味著佛教是一種唯心主義？
                </div>
            </div>
            <div class="question-item" data-question="liberation">
                <div class="question-text">解脫的哲學基礎</div>
                <div class="question-context">
                    如果一切皆空，那麼解脫的意義何在？空性智慧如何導向究竟的自由？
                </div>
            </div>
        </div>
    </div>

    <!-- 相關文章 -->
    <div class="core-concepts">
        <h2 class="section-title">
            <i class="fas fa-star"></i>
            精選哲學文章
        </h2>
        <div class="articles-grid" id="philosophy-articles">
            <!-- 文章將通過 JavaScript 動態生成 -->
        </div>
    </div>

    <script>
        // 佛教哲學相關文章數據
        const philosophyArticles = [
            {
                id: 2,
                title: "中觀派的空性哲學解析",
                excerpt: "空性是大乘佛教的核心思想，中觀派對此有深刻的闡述。本文從龍樹菩薩的根本思想出發，解釋緣起性空的深義，並探討如何在修行中體悟空性。",
                difficulty: "advanced",
                date: "2025-01-08",
                views: 892,
                likes: 67,
                icon: "fas fa-circle-notch"
            },
            {
                id: 10,
                title: "六道輪迴與業力因果",
                excerpt: "輪迴是佛教的基本觀念，業力是推動輪迴的根本力量。本文探討六道眾生的生命狀態，以及善惡業如何影響未來的生命走向，並提供解脫的修行方法。",
                difficulty: "intermediate",
                date: "2024-12-18",
                views: 2789,
                likes: 203,
                icon: "fas fa-sync"
            },
            {
                id: 12,
                title: "四聖諦：佛陀的根本教義",
                excerpt: "四聖諦是佛陀初轉法輪時所說的根本教義：苦諦、集諦、滅諦、道諦。本文深入分析了苦的本質、苦的成因、解脫的可能性以及解脫的方法。",
                difficulty: "beginner",
                date: "2024-12-12",
                views: 4123,
                likes: 312,
                icon: "fas fa-dharmachakra"
            },
            {
                id: 14,
                title: "唯識學說：心識的奧秘",
                excerpt: "唯識學是大乘佛教重要的哲學體系，由玄奘大師傳入中國。本文介紹了八識的結構、種子說的理論，以及轉識成智的修行目標。",
                difficulty: "advanced",
                date: "2024-12-08",
                views: 1789,
                likes: 143,
                icon: "fas fa-brain"
            },
            {
                id: 19,
                title: "死亡與中陰：生命的終極考驗",
                excerpt: "《西藏度亡經》詳細描述了死亡過程和中陰階段的體驗。本文解釋了死亡八階段、中陰身的特性，以及如何通過修行準備面對死亡和中陰。",
                difficulty: "advanced",
                date: "2024-11-25",
                views: 2876,
                likes: 198,
                icon: "fas fa-hourglass"
            }
        ];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderArticles();
            initializeNavigation();
        });

        // 渲染文章
        function renderArticles() {
            const container = document.getElementById('philosophy-articles');
            
            container.innerHTML = philosophyArticles.map(article => `
                <div class="article-card" data-article-id="${article.id}">
                    <div class="article-icon">
                        <i class="${article.icon}"></i>
                    </div>
                    <h3 class="article-title">${article.title}</h3>
                    <p class="article-excerpt">${article.excerpt}</p>
                    <div class="article-meta">
                        <span>${article.difficulty === 'beginner' ? '入門' : article.difficulty === 'intermediate' ? '進階' : '高級'}</span>
                        <span>${article.date}</span>
                    </div>
                </div>
            `).join('');
        }

        // 初始化導航
        function initializeNavigation() {
            // 學派點擊
            document.querySelectorAll('.school-card').forEach(card => {
                card.addEventListener('click', function() {
                    const school = this.getAttribute('data-school');
                    // 可以導航到特定學派的詳細頁面
                    console.log('Navigate to school:', school);
                });
            });

            // 概念點擊
            document.querySelectorAll('.concept-card').forEach(card => {
                card.addEventListener('click', function() {
                    const concept = this.getAttribute('data-concept');
                    // 可以顯示概念的詳細解釋
                    console.log('Show concept details:', concept);
                });
            });

            // 文獻點擊
            document.querySelectorAll('.text-card').forEach(card => {
                card.addEventListener('click', function() {
                    const text = this.getAttribute('data-text');
                    // 可以導航到文獻詳細頁面
                    console.log('Navigate to text:', text);
                });
            });

            // 問題點擊
            document.querySelectorAll('.question-item').forEach(item => {
                item.addEventListener('click', function() {
                    const question = this.getAttribute('data-question');
                    // 可以展開問題的詳細討論
                    console.log('Expand question:', question);
                });
            });

            // 文章點擊
            document.addEventListener('click', function(e) {
                const articleElement = e.target.closest('[data-article-id]');
                if (articleElement) {
                    const articleId = articleElement.getAttribute('data-article-id');
                    
                    if (window.parent !== window) {
                        window.parent.postMessage({
                            type: 'article_view',
                            articleId: articleId
                        }, '*');
                    }
                }
            });
        }
    </script>
</body>
</html>