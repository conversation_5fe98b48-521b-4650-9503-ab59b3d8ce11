<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>佛教倫理 - 佛學智慧</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.6;
            color: #333;
            background: transparent;
            padding: 20px;
        }

        /* 分類介紹區 */
        .category-intro {
            background: linear-gradient(135deg, #a8edea, #fed6e3);
            color: #333;
            padding: 40px 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .category-intro::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.2) 1px, transparent 1px);
            background-size: 30px 30px;
            animation: float 20s linear infinite;
        }

        @keyframes float {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .intro-content {
            position: relative;
            z-index: 2;
        }

        .intro-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #2c3e50;
            animation: breathe 3s infinite;
        }

        @keyframes breathe {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .intro-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .intro-description {
            font-size: 1.1rem;
            opacity: 0.8;
            line-height: 1.8;
            color: #34495e;
        }

        /* 戒律體系 */
        .precepts-system {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .precept-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border-left: 4px solid transparent;
        }

        .precept-card.five-precepts {
            border-left-color: #27ae60;
        }

        .precept-card.ten-goods {
            border-left-color: #3498db;
        }

        .precept-card.bodhisattva {
            border-left-color: #9b59b6;
        }

        .precept-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .precept-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .precept-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .precept-card.five-precepts .precept-icon {
            background: #27ae60;
        }

        .precept-card.ten-goods .precept-icon {
            background: #3498db;
        }

        .precept-card.bodhisattva .precept-icon {
            background: #9b59b6;
        }

        .precept-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
        }

        .precept-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .precept-list {
            list-style: none;
            padding: 0;
        }

        .precept-list li {
            padding: 5px 0;
            color: #555;
            position: relative;
            padding-left: 20px;
        }

        .precept-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #27ae60;
            font-weight: bold;
        }

        /* 精選文章 */
        .featured-articles {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .articles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .article-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid #e9ecef;
        }

        .article-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-color: #a8edea;
        }

        .article-icon {
            color: #a8edea;
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .article-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .article-excerpt {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .article-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: #999;
        }

        /* 倫理實踐 */
        .ethical-practice {
            background: linear-gradient(135deg, #ffeaa7, #fab1a0);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid #fdcb6e;
        }

        .practice-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .practice-item {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .practice-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #a8edea, #fed6e3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            color: #2c3e50;
            font-size: 1.5rem;
        }

        .practice-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .practice-description {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            .category-intro {
                padding: 30px 20px;
            }

            .intro-title {
                font-size: 1.5rem;
            }

            .precepts-system,
            .articles-grid,
            .practice-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 分類介紹 -->
    <div class="category-intro">
        <div class="intro-content">
            <div class="intro-icon">
                <i class="fas fa-balance-scale"></i>
            </div>
            <h1 class="intro-title">佛教倫理</h1>
            <p class="intro-description">
                佛教倫理是修行者行為的準則，通過持戒培養良好品格，為禪定和智慧的開展奠定基礎。
                戒律不僅是約束，更是通往解脫的善巧方便。
            </p>
        </div>
    </div>

    <!-- 戒律體系 -->
    <div class="precepts-system">
        <div class="precept-card five-precepts" data-precept="five-precepts">
            <div class="precept-header">
                <div class="precept-icon">
                    <i class="fas fa-hand-peace"></i>
                </div>
                <h3 class="precept-title">五戒</h3>
            </div>
            <p class="precept-description">
                在家修行者的基本戒律，培養善良品格的根本。
            </p>
            <ul class="precept-list">
                <li>不殺生 - 愛護一切生命</li>
                <li>不偷盜 - 尊重他人財物</li>
                <li>不邪淫 - 守護身心清淨</li>
                <li>不妄語 - 誠實正直待人</li>
                <li>不飲酒 - 保持清醒理智</li>
            </ul>
        </div>

        <div class="precept-card ten-goods" data-precept="ten-goods">
            <div class="precept-header">
                <div class="precept-icon">
                    <i class="fas fa-star"></i>
                </div>
                <h3 class="precept-title">十善業</h3>
            </div>
            <p class="precept-description">
                身口意三業的正面修持，累積善業的具體行為。
            </p>
            <ul class="precept-list">
                <li>身三善：不殺、不盜、不淫</li>
                <li>口四善：不妄語、不兩舌、不惡口、不綺語</li>
                <li>意三善：不貪、不瞋、不癡</li>
            </ul>
        </div>

        <div class="precept-card bodhisattva" data-precept="bodhisattva">
            <div class="precept-header">
                <div class="precept-icon">
                    <i class="fas fa-lotus"></i>
                </div>
                <h3 class="precept-title">菩薩戒</h3>
            </div>
            <p class="precept-description">
                大乘佛教的高級戒律，發菩提心利益眾生的行為準則。
            </p>
            <ul class="precept-list">
                <li>攝律儀戒 - 防止惡行</li>
                <li>攝善法戒 - 積極行善</li>
                <li>饒益有情戒 - 利益眾生</li>
            </ul>
        </div>
    </div>

    <!-- 精選文章 -->
    <div class="featured-articles">
        <h2 class="section-title">
            <i class="fas fa-star"></i>
            佛教倫理精選
        </h2>
        <div class="articles-grid" id="ethics-articles">
            <!-- 文章將通過 JavaScript 動態生成 -->
        </div>
    </div>

    <!-- 倫理實踐 -->
    <div class="ethical-practice">
        <h2 class="section-title">
            <i class="fas fa-compass"></i>
            日常倫理實踐
        </h2>
        <div class="practice-grid">
            <div class="practice-item">
                <div class="practice-icon">
                    <i class="fas fa-heart"></i>
                </div>
                <h3 class="practice-title">慈悲心</h3>
                <p class="practice-description">
                    以慈悲心對待一切眾生，願眾生離苦得樂。
                </p>
            </div>
            <div class="practice-item">
                <div class="practice-icon">
                    <i class="fas fa-hands-helping"></i>
                </div>
                <h3 class="practice-title">布施</h3>
                <p class="practice-description">
                    財布施、法布施、無畏布施，培養無私奉獻精神。
                </p>
            </div>
            <div class="practice-item">
                <div class="practice-icon">
                    <i class="fas fa-dove"></i>
                </div>
                <h3 class="practice-title">忍辱</h3>
                <p class="practice-description">
                    面對逆境保持平靜，以智慧化解衝突。
                </p>
            </div>
            <div class="practice-item">
                <div class="practice-icon">
                    <i class="fas fa-seedling"></i>
                </div>
                <h3 class="practice-title">精進</h3>
                <p class="practice-description">
                    持續不懈地修行，在善法上努力不退。
                </p>
            </div>
            <div class="practice-item">
                <div class="practice-icon">
                    <i class="fas fa-yin-yang"></i>
                </div>
                <h3 class="practice-title">正念</h3>
                <p class="practice-description">
                    時刻保持清醒覺察，觀照身心的狀態。
                </p>
            </div>
            <div class="practice-item">
                <div class="practice-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <h3 class="practice-title">智慧</h3>
                <p class="practice-description">
                    以佛法智慧觀照世間，明辨是非善惡。
                </p>
            </div>
        </div>
    </div>

    <script>
        // 佛教倫理相關文章數據
        const ethicsArticles = [
            {
                id: 9,
                title: "五戒十善：佛教倫理的基礎",
                excerpt: "五戒是在家佛弟子的基本行為準則，十善業則是身口意三業的正面修持。本文詳細解釋每一戒的深層含義以及在現代生活中的實踐方法。",
                difficulty: "beginner",
                date: "2024-12-30",
                views: 1876,
                likes: 143,
                icon: "fas fa-hand-peace"
            },
            {
                id: 22,
                title: "菩薩戒的慈悲精神",
                excerpt: "菩薩戒是大乘佛教的核心戒律，體現了菩薩道的慈悲精神。文章探討如何在日常生活中實踐菩薩戒，培養利他的菩提心。",
                difficulty: "intermediate",
                date: "2024-12-05",
                views: 1543,
                likes: 128,
                icon: "fas fa-lotus"
            },
            {
                id: 23,
                title: "佛教環保倫理觀",
                excerpt: "佛教的慈悲心和不殺生戒延伸到環境保護，形成獨特的生態倫理觀。文章探討佛教環保思想對現代環境危機的啟示。",
                difficulty: "intermediate",
                date: "2024-11-28",
                views: 1234,
                likes: 97,
                icon: "fas fa-leaf"
            }
        ];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderArticles();
            initializeNavigation();
        });

        // 渲染文章
        function renderArticles() {
            const container = document.getElementById('ethics-articles');
            
            container.innerHTML = ethicsArticles.map(article => `
                <div class="article-card" data-article-id="${article.id}">
                    <div class="article-icon">
                        <i class="${article.icon}"></i>
                    </div>
                    <h3 class="article-title">${article.title}</h3>
                    <p class="article-excerpt">${article.excerpt}</p>
                    <div class="article-meta">
                        <span>${article.difficulty === 'beginner' ? '入門' : article.difficulty === 'intermediate' ? '進階' : '高級'}</span>
                        <span>${article.date}</span>
                    </div>
                </div>
            `).join('');
        }

        // 初始化導航
        function initializeNavigation() {
            // 戒律卡片點擊
            document.querySelectorAll('.precept-card').forEach(card => {
                card.addEventListener('click', function() {
                    const precept = this.getAttribute('data-precept');
                    
                    if (window.parent !== window) {
                        window.parent.postMessage({
                            type: 'filter',
                            filterType: 'precept',
                            value: precept
                        }, '*');
                    }
                });
            });

            // 文章點擊
            document.addEventListener('click', function(e) {
                const articleElement = e.target.closest('[data-article-id]');
                if (articleElement) {
                    const articleId = articleElement.getAttribute('data-article-id');
                    
                    if (window.parent !== window) {
                        window.parent.postMessage({
                            type: 'article_view',
                            articleId: articleId
                        }, '*');
                    }
                }
            });
        }
    </script>
</body>
</html>