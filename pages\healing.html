<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>身心療癒 - 澄源閱讀</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8fffe;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 頁面標題區域 */
        .page-header {
            background: linear-gradient(135deg, #4ecdc4 0%, #2dd4cf 100%);
            color: white;
            padding: 50px 30px;
            text-align: center;
            border-radius: 20px;
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 2px, transparent 2px);
            background-size: 40px 40px;
            animation: gentle-float 25s linear infinite;
        }

        @keyframes gentle-float {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .page-header-content {
            position: relative;
            z-index: 2;
        }

        .page-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            animation: breathe 4s infinite;
        }

        @keyframes breathe {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .page-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        /* 療癒方法分類 */
        .healing-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .category-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .category-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #4ecdc4, #2dd4cf);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .category-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .category-card:hover::before {
            transform: scaleX(1);
        }

        .category-icon {
            font-size: 3rem;
            color: #4ecdc4;
            margin-bottom: 20px;
            display: block;
        }

        .category-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 12px;
            color: #333;
        }

        .category-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .category-count {
            color: #4ecdc4;
            font-weight: 500;
            font-size: 0.9rem;
        }

        /* 精選療癒內容 */
        .featured-content {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 25px;
            color: #333;
            position: relative;
            padding-left: 20px;
        }

        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 30px;
            background: linear-gradient(135deg, #4ecdc4, #2dd4cf);
            border-radius: 2px;
        }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
        }

        .content-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .content-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .content-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #4ecdc4 0%, #2dd4cf 100%);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
        }

        .content-image::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 70% 30%, rgba(255,255,255,0.2) 0%, transparent 50%);
        }

        .content-body {
            padding: 25px;
        }

        .content-category {
            background: #e0f7f7;
            color: #00695c;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            display: inline-block;
            margin-bottom: 15px;
        }

        .content-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 12px;
            color: #333;
            line-height: 1.4;
        }

        .content-excerpt {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .content-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: #999;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }

        .content-difficulty {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .difficulty-level {
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .difficulty-easy {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .difficulty-medium {
            background: #fff3e0;
            color: #ef6c00;
        }

        .difficulty-hard {
            background: #fce4ec;
            color: #c2185b;
        }

        /* 互動式療癒工具 */
        .interactive-tools {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 25px;
        }

        .tool-card {
            background: linear-gradient(135deg, #f0fdfc 0%, #e0f7f7 100%);
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .tool-card:hover {
            transform: translateY(-3px);
            border-color: #4ecdc4;
            box-shadow: 0 8px 20px rgba(78, 205, 196, 0.2);
        }

        .tool-icon {
            font-size: 2.5rem;
            color: #4ecdc4;
            margin-bottom: 15px;
        }

        .tool-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }

        .tool-description {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.5;
        }

        /* 療癒音樂播放器 */
        .music-player {
            background: linear-gradient(135deg, #4ecdc4 0%, #2dd4cf 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
        }

        .music-player::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="20" r="1.5" fill="white" opacity="0.1"/><circle cx="20" cy="80" r="1" fill="white" opacity="0.1"/></svg>');
            animation: gentle-float 20s linear infinite;
        }

        .music-content {
            position: relative;
            z-index: 2;
        }

        .music-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
        }

        .music-tracks {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .track-item {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .track-item:hover {
            background: rgba(255,255,255,0.2);
            transform: scale(1.02);
        }

        .track-name {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .track-duration {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        /* 引導式冥想 */
        .meditation-guide {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .meditation-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 25px;
        }

        .step-card {
            text-align: center;
            padding: 20px;
            border-radius: 12px;
            background: linear-gradient(135deg, #f8ffff 0%, #f0fdfc 100%);
            position: relative;
        }

        .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #4ecdc4, #2dd4cf);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin: 0 auto 15px;
        }

        .step-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }

        .step-description {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.5;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .page-header {
                padding: 40px 20px;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .healing-categories,
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .tools-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }

        @media (max-width: 480px) {
            .page-title {
                font-size: 1.5rem;
            }
            
            .section-title {
                font-size: 1.5rem;
            }
            
            .category-card,
            .content-body,
            .interactive-tools,
            .music-player,
            .meditation-guide {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 頁面標題 -->
        <header class="page-header">
            <div class="page-header-content">
                <div class="page-icon">
                    <i class="fas fa-spa"></i>
                </div>
                <h1 class="page-title">身心療癒</h1>
                <p class="page-subtitle">融合佛教智慧與現代療癒技術<br>重建身心和諧，釋放內在潛能，獲得真正的平靜與喜悅</p>
            </div>
        </header>

        <!-- 療癒方法分類 -->
        <section class="healing-categories">
            <div class="category-card" data-category="meditation">
                <i class="fas fa-lotus-position category-icon"></i>
                <h3 class="category-title">正念冥想</h3>
                <p class="category-description">通過正念練習培養內在覺察，減輕壓力，提升專注力和情緒穩定性</p>
                <span class="category-count">25+ 練習方法</span>
            </div>
            
            <div class="category-card" data-category="breathing">
                <i class="fas fa-wind category-icon"></i>
                <h3 class="category-title">呼吸療癒</h3>
                <p class="category-description">運用古老的呼吸技法調節神經系統，平衡身心能量，促進深度放鬆</p>
                <span class="category-count">18+ 呼吸法</span>
            </div>
            
            <div class="category-card" data-category="movement">
                <i class="fas fa-yin-yang category-icon"></i>
                <h3 class="category-title">身心律動</h3>
                <p class="category-description">結合瑜伽、太極等身體練習，促進氣血流通，增強身體覺知能力</p>
                <span class="category-count">30+ 動作指導</span>
            </div>
            
            <div class="category-card" data-category="sound">
                <i class="fas fa-music category-icon"></i>
                <h3 class="category-title">聲音療癒</h3>
                <p class="category-description">運用頌缽、咒語、自然音頻等聲音振動，清理負面能量，提升振動頻率</p>
                <span class="category-count">20+ 音頻資源</span>
            </div>
            
            <div class="category-card" data-category="visualization">
                <i class="fas fa-eye category-icon"></i>
                <h3 class="category-title">觀想療癒</h3>
                <p class="category-description">透過引導式意象和觀想練習，激發身體自癒能力，轉化內在創傷</p>
                <span class="category-count">15+ 觀想練習</span>
            </div>
            
            <div class="category-card" data-category="energy">
                <i class="fas fa-bolt category-icon"></i>
                <h3 class="category-title">能量平衡</h3>
                <p class="category-description">學習脈輪調整、氣場淨化等能量療癒技術，恢復身心靈的和諧狀態</p>
                <span class="category-count">22+ 能量練習</span>
            </div>
        </section>

        <!-- 精選療癒內容 -->
        <section class="featured-content">
            <h2 class="section-title">
                <i class="fas fa-star"></i>
                精選療癒內容
            </h2>
            <div class="content-grid" id="content-grid">
                <!-- 內容將通過 JavaScript 動態生成 -->
            </div>
        </section>

        <!-- 互動式療癒工具 -->
        <section class="interactive-tools">
            <h2 class="section-title">
                <i class="fas fa-tools"></i>
                互動式療癒工具
            </h2>
            <div class="tools-grid">
                <div class="tool-card" data-tool="breathing-timer">
                    <div class="tool-icon">
                        <i class="fas fa-stopwatch"></i>
                    </div>
                    <h3 class="tool-title">呼吸計時器</h3>
                    <p class="tool-description">引導式呼吸練習，幫助建立正確的呼吸節奏</p>
                </div>
                
                <div class="tool-card" data-tool="meditation-timer">
                    <div class="tool-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="tool-title">冥想計時器</h3>
                    <p class="tool-description">可自定義時間的冥想計時器，含開始結束提示音</p>
                </div>
                
                <div class="tool-card" data-tool="mood-tracker">
                    <div class="tool-icon">
                        <i class="fas fa-heart-pulse"></i>
                    </div>
                    <h3 class="tool-title">情緒追蹤</h3>
                    <p class="tool-description">記錄每日情緒狀態，追蹤療癒進展</p>
                </div>
                
                <div class="tool-card" data-tool="affirmation">
                    <div class="tool-icon">
                        <i class="fas fa-comment-dots"></i>
                    </div>
                    <h3 class="tool-title">正向肯定</h3>
                    <p class="tool-description">每日正向肯定語句，重塑潛意識思維模式</p>
                </div>
            </div>
        </section>

        <!-- 療癒音樂播放器 -->
        <section class="music-player">
            <div class="music-content">
                <h2 class="music-title">
                    <i class="fas fa-headphones"></i>
                    療癒音頻
                </h2>
                <div class="music-tracks">
                    <div class="track-item" data-track="nature">
                        <div class="track-name">自然森林</div>
                        <div class="track-duration">15:30</div>
                    </div>
                    <div class="track-item" data-track="ocean">
                        <div class="track-name">海洋波浪</div>
                        <div class="track-duration">20:45</div>
                    </div>
                    <div class="track-item" data-track="tibetan">
                        <div class="track-name">藏傳頌缽</div>
                        <div class="track-duration">12:20</div>
                    </div>
                    <div class="track-item" data-track="rain">
                        <div class="track-name">雨聲療癒</div>
                        <div class="track-duration">18:15</div>
                    </div>
                    <div class="track-item" data-track="mantra">
                        <div class="track-name">梵語咒語</div>
                        <div class="track-duration">25:00</div>
                    </div>
                    <div class="track-item" data-track="crystal">
                        <div class="track-name">水晶療癒</div>
                        <div class="track-duration">22:30</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 引導式冥想 -->
        <section class="meditation-guide">
            <h2 class="section-title">
                <i class="fas fa-route"></i>
                七步引導冥想
            </h2>
            <div class="meditation-steps">
                <div class="step-card">
                    <div class="step-number">1</div>
                    <h3 class="step-title">準備姿勢</h3>
                    <p class="step-description">找一個安靜的地方，採取舒適的坐姿或躺姿</p>
                </div>
                
                <div class="step-card">
                    <div class="step-number">2</div>
                    <h3 class="step-title">調整呼吸</h3>
                    <p class="step-description">深呼吸三次，讓身體和心靈逐漸放鬆</p>
                </div>
                
                <div class="step-card">
                    <div class="step-number">3</div>
                    <h3 class="step-title">身體掃描</h3>
                    <p class="step-description">從頭到腳感受身體各部位，釋放緊張</p>
                </div>
                
                <div class="step-card">
                    <div class="step-number">4</div>
                    <h3 class="step-title">專注當下</h3>
                    <p class="step-description">將注意力帶回當下，觀察呼吸的自然節奏</p>
                </div>
                
                <div class="step-card">
                    <div class="step-number">5</div>
                    <h3 class="step-title">慈心觀想</h3>
                    <p class="step-description">向自己和他人發送愛與慈悲的祝福</p>
                </div>
                
                <div class="step-card">
                    <div class="step-number">6</div>
                    <h3 class="step-title">深度靜觀</h3>
                    <p class="step-description">進入更深層的覺察狀態，體驗內在平靜</p>
                </div>
                
                <div class="step-card">
                    <div class="step-number">7</div>
                    <h3 class="step-title">回歸整合</h3>
                    <p class="step-description">慢慢回到日常意識，保持覺察狀態</p>
                </div>
            </div>
        </section>
    </div>

    <script>
        // 療癒內容數據
        const healingContent = [
            {
                id: 1,
                title: "正念呼吸法：21天轉化計畫",
                excerpt: "通過系統性的正念呼吸練習，在21天內建立新的呼吸習慣，有效緩解焦慮、改善睡眠質量，提升整體身心健康水平。",
                category: "呼吸療癒",
                difficulty: "easy",
                icon: "fas fa-wind",
                duration: "21天課程"
            },
            {
                id: 2,
                title: "藏傳療癒咒語與觀想",
                excerpt: "學習傳統藏傳佛教的療癒咒語，結合觀想練習，清理負面能量，激發身體自然療癒能力，促進身心靈的深度淨化。",
                category: "聲音療癒",
                difficulty: "medium",
                icon: "fas fa-om",
                duration: "45分鐘"
            },
            {
                id: 3,
                title: "脈輪平衡與能量調整",
                excerpt: "深入了解人體七大脈輪系統，學習如何通過冥想、音頻和水晶等方法平衡脈輪能量，提升生命活力和精神狀態。",
                category: "能量平衡",
                difficulty: "hard",
                icon: "fas fa-circle-nodes",
                duration: "60分鐘"
            },
            {
                id: 4,
                title: "情緒釋放與內在療癒",
                excerpt: "運用佛教心理學原理，學習識別和轉化負面情緒，通過慈悲冥想和自我寬恕練習，實現內在創傷的深度療癒。",
                category: "情緒療癒",
                difficulty: "medium",
                icon: "fas fa-heart",
                duration: "30分鐘"
            },
            {
                id: 5,
                title: "身體覺知瑜伽療癒",
                excerpt: "結合瑜伽體位法與正念覺察，增強身體感知能力，改善姿勢問題，釋放身體深層緊張，促進氣血循環。",
                category: "身心律動",
                difficulty: "easy",
                icon: "fas fa-person-yoga",
                duration: "40分鐘"
            },
            {
                id: 6,
                title: "睡眠療癒引導冥想",
                excerpt: "專為改善睡眠設計的引導冥想，結合身體放鬆技巧和心靈淨化練習，幫助快速入睡並提升睡眠質量。",
                category: "睡眠療癒",
                difficulty: "easy",
                icon: "fas fa-moon",
                duration: "25分鐘"
            }
        ];

        // 渲染療癒內容
        function renderContent() {
            const grid = document.getElementById('content-grid');
            grid.innerHTML = healingContent.map(content => `
                <div class="content-card" data-content="${content.id}">
                    <div class="content-image">
                        <i class="${content.icon}"></i>
                    </div>
                    <div class="content-body">
                        <span class="content-category">${content.category}</span>
                        <h3 class="content-title">${content.title}</h3>
                        <p class="content-excerpt">${content.excerpt}</p>
                        <div class="content-meta">
                            <div class="content-difficulty">
                                <span class="difficulty-level difficulty-${content.difficulty}">
                                    ${getDifficultyText(content.difficulty)}
                                </span>
                            </div>
                            <span class="content-duration">
                                <i class="fas fa-clock"></i>
                                ${content.duration}
                            </span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function getDifficultyText(difficulty) {
            const texts = {
                'easy': '初級',
                'medium': '中級',
                'hard': '高級'
            };
            return texts[difficulty] || '';
        }

        // 分類卡片點擊
        document.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('click', function() {
                const category = this.getAttribute('data-category');
                showNotification(`正在載入${this.querySelector('.category-title').textContent}內容...`);
                
                // 這裡可以根據分類篩選內容
                setTimeout(() => {
                    showNotification(`${this.querySelector('.category-title').textContent}內容載入完成！`);
                }, 1000);
            });
        });

        // 內容卡片點擊
        document.addEventListener('click', function(e) {
            const contentCard = e.target.closest('.content-card');
            if (contentCard) {
                const contentId = contentCard.getAttribute('data-content');
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'navigate',
                        page: 'article',
                        articleId: contentId,
                        category: 'healing'
                    }, '*');
                }
            }
        });

        // 工具卡片點擊
        document.querySelectorAll('.tool-card').forEach(tool => {
            tool.addEventListener('click', function() {
                const toolType = this.getAttribute('data-tool');
                const toolName = this.querySelector('.tool-title').textContent;
                
                switch(toolType) {
                    case 'breathing-timer':
                        startBreathingTimer();
                        break;
                    case 'meditation-timer':
                        startMeditationTimer();
                        break;
                    case 'mood-tracker':
                        openMoodTracker();
                        break;
                    case 'affirmation':
                        showAffirmation();
                        break;
                    default:
                        showNotification(`正在啟動${toolName}...`);
                }
            });
        });

        // 音頻軌道點擊
        document.querySelectorAll('.track-item').forEach(track => {
            track.addEventListener('click', function() {
                const trackName = this.querySelector('.track-name').textContent;
                
                // 移除其他活躍狀態
                document.querySelectorAll('.track-item').forEach(t => {
                    t.style.background = 'rgba(255,255,255,0.1)';
                });
                
                // 設置當前活躍狀態
                this.style.background = 'rgba(255,255,255,0.3)';
                
                showNotification(`正在播放：${trackName}`);
                
                // 模擬播放功能
                setTimeout(() => {
                    showNotification(`${trackName} 播放完成`);
                    this.style.background = 'rgba(255,255,255,0.1)';
                }, 3000);
            });
        });

        // 冥想步驟點擊
        document.querySelectorAll('.step-card').forEach(step => {
            step.addEventListener('click', function() {
                const stepNumber = this.querySelector('.step-number').textContent;
                const stepTitle = this.querySelector('.step-title').textContent;
                
                showNotification(`開始第${stepNumber}步：${stepTitle}`);
            });
        });

        // 療癒工具函數
        function startBreathingTimer() {
            showNotification('呼吸計時器已啟動：吸氣4秒 → 屏息4秒 → 呼氣6秒');
            
            // 模擬呼吸引導
            let cycle = 0;
            const breathingCycle = setInterval(() => {
                cycle++;
                const phase = cycle % 3;
                const messages = ['深深吸氣...', '輕輕屏息...', '慢慢呼氣...'];
                
                showNotification(messages[phase]);
                
                if (cycle >= 15) { // 5個完整週期
                    clearInterval(breathingCycle);
                    showNotification('呼吸練習完成！感受內在的平靜。');
                }
            }, 4000);
        }

        function startMeditationTimer() {
            const duration = prompt('請輸入冥想時間（分鐘）：', '10');
            if (duration && !isNaN(duration)) {
                showNotification(`冥想計時器已設定：${duration}分鐘`);
                
                setTimeout(() => {
                    showNotification('冥想時間結束。感謝您的練習！');
                }, duration * 60 * 1000);
            }
        }

        function openMoodTracker() {
            const moods = ['😊 愉悅', '😌 平靜', '😐 中性', '😔 低落', '😰 焦慮'];
            const selectedMood = prompt('請選擇當前情緒狀態：\n1. 😊 愉悅\n2. 😌 平靜\n3. 😐 中性\n4. 😔 低落\n5. 😰 焦慮\n\n輸入數字(1-5)：');
            
            if (selectedMood && selectedMood >= 1 && selectedMood <= 5) {
                const mood = moods[selectedMood - 1];
                showNotification(`情緒記錄已保存：${mood}`);
            }
        }

        function showAffirmation() {
            const affirmations = [
                '我值得被愛，我選擇愛自己',
                '我內在充滿平靜與智慧',
                '我相信生命的美好與可能',
                '我釋放過去，擁抱當下',
                '我的身心靈處於完美的和諧狀態',
                '我感恩生命中的每一個經歷',
                '我散發著愛與光明的能量'
            ];
            
            const randomAffirmation = affirmations[Math.floor(Math.random() * affirmations.length)];
            showNotification(`今日正向肯定：${randomAffirmation}`, 'success');
        }

        // 通知函數
        function showNotification(message, type = 'info') {
            if (window.parent !== window) {
                window.parent.postMessage({
                    type: 'notification',
                    message: message,
                    level: type
                }, '*');
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderContent();
            
            // 添加進入動畫
            setTimeout(() => {
                document.querySelectorAll('.category-card, .content-card').forEach((card, index) => {
                    setTimeout(() => {
                        card.style.opacity = '0';
                        card.style.transform = 'translateY(20px)';
                        card.style.transition = 'all 0.5s ease';
                        
                        setTimeout(() => {
                            card.style.opacity = '1';
                            card.style.transform = 'translateY(0)';
                        }, 100);
                    }, index * 100);
                });
            }, 500);
        });

        // 響應式音頻播放提示
        function playAudioFeedback() {
            if ('vibrate' in navigator) {
                navigator.vibrate(50);
            }
        }

        // 為所有可點擊元素添加觸覺反饋
        document.addEventListener('click', function(e) {
            if (e.target.closest('.category-card, .content-card, .tool-card, .track-item, .step-card')) {
                playAudioFeedback();
            }
        });
    </script>
</body>
</html>