const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const authenticate = require('../middleware/auth');

// 驗證錯誤處理中間件
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// 模擬工作坊數據
const workshops = [
  {
    id: '1',
    title: '正念減壓工作坊',
    category: 'mindfulness',
    type: 'healing',
    instructor: '陳慧心老師',
    date: '2024-01-20',
    time: '14:00-17:00',
    duration: 180,
    location: '台北禪修中心',
    address: '台北市中正區忠孝東路一段100號',
    maxParticipants: 30,
    currentParticipants: 22,
    price: 1500,
    currency: 'TWD',
    difficulty: 'beginner',
    description: '透過正念練習學習如何處理日常壓力，提升生活品質',
    objectives: [
      '學習基本正念技巧',
      '了解壓力產生機制',
      '建立日常練習習慣',
      '提升情緒管理能力'
    ],
    agenda: [
      { time: '14:00-14:30', activity: '報到與介紹' },
      { time: '14:30-15:30', activity: '正念基礎理論' },
      { time: '15:30-15:45', activity: '茶點時間' },
      { time: '15:45-16:45', activity: '正念練習體驗' },
      { time: '16:45-17:00', activity: '分享與回饋' }
    ],
    materials: ['正念練習手冊', '音頻練習檔案', '練習日誌'],
    prerequisites: '無須任何經驗，適合初學者',
    benefits: ['減少焦慮', '改善睡眠', '提升專注力', '增進人際關係'],
    status: 'open_registration',
    tags: ['正念', '減壓', '初學者友善', '實用技巧'],
    rating: 4.7,
    reviews: 45,
    cancellationPolicy: '開課前3天可全額退費，前1天退費50%',
    requirements: ['舒適服裝', '瑜伽墊或坐墊', '筆記本']
  },
  {
    id: '2',
    title: '藏式頌缽療癒工作坊',
    category: 'sound_healing',
    type: 'healing',
    instructor: '洛桑旺堆',
    date: '2024-01-25',
    time: '19:00-21:00',
    duration: 120,
    location: '心靈療癒中心',
    address: '台北市大安區敦化南路二段200號',
    maxParticipants: 20,
    currentParticipants: 15,
    price: 2000,
    currency: 'TWD',
    difficulty: 'beginner',
    description: '體驗傳統藏式頌缽的療癒力量，平衡身心能量',
    objectives: [
      '認識頌缽歷史文化',
      '學習基本敲擊技巧',
      '體驗聲音療癒',
      '了解脈輪平衡'
    ],
    agenda: [
      { time: '19:00-19:15', activity: '入場與準備' },
      { time: '19:15-19:45', activity: '頌缽文化介紹' },
      { time: '19:45-20:30', activity: '頌缽體驗練習' },
      { time: '20:30-21:00', activity: '團體療癒時光' }
    ],
    materials: ['專業頌缽', '療癒音樂CD', '能量水晶'],
    prerequisites: '無特殊要求，建議空腹1小時參加',
    benefits: ['深度放鬆', '能量平衡', '情緒釋放', '提升直覺'],
    status: 'open_registration',
    tags: ['頌缽', '聲音療癒', '能量平衡', '深度放鬆'],
    rating: 4.9,
    reviews: 32,
    cancellationPolicy: '開課前2天可全額退費',
    requirements: ['寬鬆服裝', '開放心態', '避免飲酒']
  },
  {
    id: '3',
    title: '佛教藝術創作療法',
    category: 'art_therapy',
    type: 'creative',
    instructor: '釋妙藝師父',
    date: '2024-02-03',
    time: '10:00-16:00',
    duration: 360,
    location: '法華藝術中心',
    address: '台北市文山區羅斯福路六段150號',
    maxParticipants: 15,
    currentParticipants: 8,
    price: 3500,
    currency: 'TWD',
    difficulty: 'intermediate',
    description: '結合佛教智慧與藝術創作，透過繪畫與手工藝品找到內在平靜',
    objectives: [
      '學習佛教藝術基礎',
      '體驗創作療癒過程',
      '製作個人祈福作品',
      '培養藝術靜心能力'
    ],
    agenda: [
      { time: '10:00-10:30', activity: '開場與介紹' },
      { time: '10:30-12:00', activity: '佛教藝術賞析' },
      { time: '12:00-13:00', activity: '素食午餐' },
      { time: '13:00-15:00', activity: '創作實作時間' },
      { time: '15:00-15:30', activity: '作品分享' },
      { time: '15:30-16:00', activity: '祈福儀式' }
    ],
    materials: ['繪畫用具', '手工材料', '佛像範本', '午餐'],
    prerequisites: '對藝術創作有興趣，無需專業技能',
    benefits: ['釋放創造力', '內心平靜', '專注提升', '壓力舒緩'],
    status: 'open_registration',
    tags: ['藝術療法', '創作', '手工藝', '靜心'],
    rating: 4.6,
    reviews: 18,
    cancellationPolicy: '開課前5天可全額退費，前2天退費70%',
    requirements: ['舊衣服', '創作熱忱', '耐心']
  }
];

// 獲取工作坊列表
router.get('/', async (req, res) => {
  try {
    const {
      category,
      type,
      difficulty,
      instructor,
      location,
      dateFrom,
      dateTo,
      priceRange,
      status = 'open_registration',
      page = 1,
      limit = 12,
      sort = 'date'
    } = req.query;

    let filteredWorkshops = [...workshops];

    // 應用篩選條件
    if (category) {
      filteredWorkshops = filteredWorkshops.filter(w => w.category === category);
    }
    if (type) {
      filteredWorkshops = filteredWorkshops.filter(w => w.type === type);
    }
    if (difficulty) {
      filteredWorkshops = filteredWorkshops.filter(w => w.difficulty === difficulty);
    }
    if (instructor) {
      filteredWorkshops = filteredWorkshops.filter(w => 
        w.instructor.toLowerCase().includes(instructor.toLowerCase())
      );
    }
    if (location) {
      filteredWorkshops = filteredWorkshops.filter(w => 
        w.location.toLowerCase().includes(location.toLowerCase())
      );
    }
    if (status) {
      filteredWorkshops = filteredWorkshops.filter(w => w.status === status);
    }

    // 日期範圍篩選
    if (dateFrom || dateTo) {
      filteredWorkshops = filteredWorkshops.filter(workshop => {
        const workshopDate = new Date(workshop.date);
        if (dateFrom && workshopDate < new Date(dateFrom)) return false;
        if (dateTo && workshopDate > new Date(dateTo)) return false;
        return true;
      });
    }

    // 價格範圍篩選
    if (priceRange) {
      const [minPrice, maxPrice] = priceRange.split('-').map(Number);
      filteredWorkshops = filteredWorkshops.filter(w => 
        w.price >= minPrice && w.price <= maxPrice
      );
    }

    // 排序
    const sortOptions = {
      'date': (a, b) => new Date(a.date) - new Date(b.date),
      'price_low': (a, b) => a.price - b.price,
      'price_high': (a, b) => b.price - a.price,
      'rating': (a, b) => b.rating - a.rating,
      'popular': (a, b) => b.currentParticipants - a.currentParticipants,
      'title': (a, b) => a.title.localeCompare(b.title)
    };

    if (sortOptions[sort]) {
      filteredWorkshops.sort(sortOptions[sort]);
    }

    // 分頁
    const skip = (page - 1) * limit;
    const paginatedWorkshops = filteredWorkshops.slice(skip, skip + parseInt(limit));

    // 統計信息
    const statistics = {
      totalWorkshops: filteredWorkshops.length,
      upcomingWorkshops: filteredWorkshops.filter(w => new Date(w.date) > new Date()).length,
      averagePrice: Math.round(filteredWorkshops.reduce((sum, w) => sum + w.price, 0) / filteredWorkshops.length),
      categories: [...new Set(workshops.map(w => w.category))],
      types: [...new Set(workshops.map(w => w.type))],
      difficulties: [...new Set(workshops.map(w => w.difficulty))],
      locations: [...new Set(workshops.map(w => w.location))]
    };

    res.json({
      message: 'Workshops retrieved successfully',
      workshops: paginatedWorkshops,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: filteredWorkshops.length,
        pages: Math.ceil(filteredWorkshops.length / limit)
      },
      statistics,
      filters: {
        categories: ['mindfulness', 'sound_healing', 'art_therapy', 'meditation', 'breathing'],
        types: ['healing', 'creative', 'educational', 'spiritual'],
        difficulties: ['beginner', 'intermediate', 'advanced'],
        sortOptions: Object.keys(sortOptions)
      }
    });
  } catch (error) {
    console.error('Get workshops error:', error);
    res.status(500).json({ error: 'Failed to fetch workshops' });
  }
});

// 獲取特定工作坊詳情
router.get('/:id', async (req, res) => {
  try {
    const workshopId = req.params.id;
    const workshop = workshops.find(w => w.id === workshopId);

    if (!workshop) {
      return res.status(404).json({ error: 'Workshop not found' });
    }

    // 計算剩餘名額
    const spotsLeft = workshop.maxParticipants - workshop.currentParticipants;
    
    // 檢查報名截止時間
    const workshopDate = new Date(workshop.date);
    const registrationDeadline = new Date(workshopDate.getTime() - 24 * 60 * 60 * 1000); // 前一天截止
    const isRegistrationOpen = new Date() < registrationDeadline && spotsLeft > 0 && workshop.status === 'open_registration';

    // 獲取相關工作坊
    const relatedWorkshops = workshops
      .filter(w => w.id !== workshopId)
      .filter(w => w.category === workshop.category || w.type === workshop.type)
      .slice(0, 3);

    res.json({
      message: 'Workshop details retrieved successfully',
      workshop: {
        ...workshop,
        spotsLeft,
        registrationDeadline,
        isRegistrationOpen,
        daysUntilWorkshop: Math.ceil((workshopDate - new Date()) / (1000 * 60 * 60 * 24))
      },
      relatedWorkshops,
      instructorInfo: {
        name: workshop.instructor,
        bio: '資深佛教療癒師，擁有15年教學經驗', // 實際應從數據庫獲取
        qualifications: ['正念減壓認證講師', '佛教心理學碩士'],
        experience: '15年'
      }
    });
  } catch (error) {
    console.error('Get workshop details error:', error);
    res.status(500).json({ error: 'Failed to fetch workshop details' });
  }
});

// 報名工作坊
router.post('/:id/register', authenticate, [
  body('emergencyContact').notEmpty().withMessage('Emergency contact is required'),
  body('emergencyPhone').isMobilePhone().withMessage('Valid emergency phone required'),
  body('medicalConditions').optional().isLength({ max: 500 }).withMessage('Medical conditions description too long'),
  body('dietaryRequirements').optional().isLength({ max: 200 }).withMessage('Dietary requirements too long'),
  body('motivation').optional().isLength({ max: 300 }).withMessage('Motivation too long'),
  body('previousExperience').optional().isLength({ max: 300 }).withMessage('Previous experience description too long')
], handleValidationErrors, async (req, res) => {
  try {
    const workshopId = req.params.id;
    const userId = req.user._id;
    const {
      emergencyContact,
      emergencyPhone,
      medicalConditions,
      dietaryRequirements,
      motivation,
      previousExperience,
      specialNeeds
    } = req.body;

    const workshop = workshops.find(w => w.id === workshopId);
    if (!workshop) {
      return res.status(404).json({ error: 'Workshop not found' });
    }

    // 檢查名額
    const spotsLeft = workshop.maxParticipants - workshop.currentParticipants;
    if (spotsLeft <= 0) {
      return res.status(400).json({ error: 'Workshop is fully booked' });
    }

    // 檢查報名狀態
    if (workshop.status !== 'open_registration') {
      return res.status(400).json({ error: 'Registration is not open for this workshop' });
    }

    // 檢查報名截止時間
    const workshopDate = new Date(workshop.date);
    const registrationDeadline = new Date(workshopDate.getTime() - 24 * 60 * 60 * 1000);
    if (new Date() > registrationDeadline) {
      return res.status(400).json({ error: 'Registration deadline has passed' });
    }

    // 檢查會員權限（某些高級工作坊可能需要特定會員等級）
    if (workshop.price > 3000) {
      const membershipLevels = { basic: 1, premium: 2, vip: 3 };
      const userLevel = membershipLevels[req.user.membership.plan];
      if (userLevel < 2) {
        return res.status(403).json({ 
          error: 'Premium membership required for this workshop',
          required: 'premium',
          current: req.user.membership.plan
        });
      }
    }

    // 創建報名記錄
    const registration = {
      id: Date.now().toString(),
      userId,
      workshopId,
      emergencyContact,
      emergencyPhone,
      medicalConditions,
      dietaryRequirements,
      motivation,
      previousExperience,
      specialNeeds,
      status: 'confirmed',
      registeredAt: new Date(),
      paymentStatus: 'pending',
      paymentDueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3天內付款
    };

    // 更新參與人數（模擬）
    workshop.currentParticipants += 1;

    console.log('Workshop registration:', registration);

    res.json({
      message: 'Workshop registration successful',
      registration: {
        id: registration.id,
        workshopTitle: workshop.title,
        workshopDate: workshop.date,
        workshopTime: workshop.time,
        location: workshop.location,
        address: workshop.address,
        status: registration.status,
        paymentDueDate: registration.paymentDueDate,
        totalAmount: workshop.price
      },
      nextSteps: [
        '請於3天內完成付款確認',
        '工作坊前一天會發送提醒通知',
        '請提前15分鐘到場報到',
        '記得攜帶所需用品'
      ],
      paymentInfo: {
        amount: workshop.price,
        currency: workshop.currency,
        methods: ['信用卡', '銀行轉帳', '行動支付']
      }
    });
  } catch (error) {
    console.error('Register workshop error:', error);
    res.status(500).json({ error: 'Failed to register for workshop' });
  }
});

// 取消工作坊報名
router.delete('/:workshopId/registration/:registrationId', authenticate, [
  body('reason').optional().isLength({ max: 300 }).withMessage('Cancellation reason too long')
], handleValidationErrors, async (req, res) => {
  try {
    const { workshopId, registrationId } = req.params;
    const { reason } = req.body;
    const userId = req.user._id;

    const workshop = workshops.find(w => w.id === workshopId);
    if (!workshop) {
      return res.status(404).json({ error: 'Workshop not found' });
    }

    // 計算取消費用
    const workshopDate = new Date(workshop.date);
    const daysUntilWorkshop = Math.ceil((workshopDate - new Date()) / (1000 * 60 * 60 * 24));
    
    let refundRate = 0;
    if (daysUntilWorkshop >= 3) {
      refundRate = 1.0; // 全額退費
    } else if (daysUntilWorkshop >= 1) {
      refundRate = 0.5; // 50%退費
    } else {
      refundRate = 0; // 不退費
    }

    const refundAmount = Math.round(workshop.price * refundRate);
    const cancellationFee = workshop.price - refundAmount;

    // 處理取消（實際應更新數據庫）
    workshop.currentParticipants -= 1;

    console.log(`Cancellation: User ${userId} cancelled workshop registration ${registrationId}`);

    res.json({
      message: 'Workshop registration cancelled successfully',
      cancellationDetails: {
        registrationId,
        workshopTitle: workshop.title,
        originalAmount: workshop.price,
        refundAmount,
        cancellationFee,
        refundRate: Math.round(refundRate * 100),
        reason,
        cancelledAt: new Date(),
        refundProcessingTime: '3-5 business days'
      },
      refundPolicy: workshop.cancellationPolicy
    });
  } catch (error) {
    console.error('Cancel workshop registration error:', error);
    res.status(500).json({ error: 'Failed to cancel workshop registration' });
  }
});

// 獲取用戶的工作坊報名記錄
router.get('/my/registrations', authenticate, async (req, res) => {
  try {
    const userId = req.user._id;
    const { status, upcoming } = req.query;

    // 模擬用戶報名記錄
    const userRegistrations = [
      {
        id: '1',
        workshopId: '1',
        title: '正念減壓工作坊',
        date: '2024-01-20',
        time: '14:00-17:00',
        location: '台北禪修中心',
        status: 'confirmed',
        paymentStatus: 'paid',
        registeredAt: '2023-12-15',
        amount: 1500
      },
      {
        id: '2',
        workshopId: '2',
        title: '藏式頌缽療癒工作坊',
        date: '2024-01-25',
        time: '19:00-21:00',
        location: '心靈療癒中心',
        status: 'confirmed',
        paymentStatus: 'pending',
        registeredAt: '2023-12-20',
        amount: 2000
      }
    ];

    let filteredRegistrations = [...userRegistrations];

    // 狀態篩選
    if (status) {
      filteredRegistrations = filteredRegistrations.filter(r => r.status === status);
    }

    // 即將到來的工作坊
    if (upcoming === 'true') {
      filteredRegistrations = filteredRegistrations.filter(r => 
        new Date(r.date) > new Date()
      );
    }

    // 計算統計
    const statistics = {
      total: userRegistrations.length,
      upcoming: userRegistrations.filter(r => new Date(r.date) > new Date()).length,
      completed: userRegistrations.filter(r => new Date(r.date) < new Date()).length,
      totalSpent: userRegistrations.reduce((sum, r) => sum + r.amount, 0),
      pendingPayments: userRegistrations.filter(r => r.paymentStatus === 'pending').length
    };

    res.json({
      message: 'User workshop registrations retrieved successfully',
      registrations: filteredRegistrations,
      statistics,
      upcomingWorkshops: filteredRegistrations.filter(r => 
        new Date(r.date) > new Date() && new Date(r.date) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
      )
    });
  } catch (error) {
    console.error('Get user registrations error:', error);
    res.status(500).json({ error: 'Failed to fetch user registrations' });
  }
});

// 工作坊評價
router.post('/:id/review', authenticate, [
  body('rating').isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
  body('comment').isLength({ min: 10, max: 500 }).withMessage('Comment must be 10-500 characters'),
  body('aspects').optional().isObject().withMessage('Aspects must be an object')
], handleValidationErrors, async (req, res) => {
  try {
    const workshopId = req.params.id;
    const userId = req.user._id;
    const { rating, comment, aspects } = req.body;

    const workshop = workshops.find(w => w.id === workshopId);
    if (!workshop) {
      return res.status(404).json({ error: 'Workshop not found' });
    }

    // 檢查是否已參加過該工作坊
    const workshopDate = new Date(workshop.date);
    if (workshopDate > new Date()) {
      return res.status(400).json({ error: 'Cannot review workshop before it takes place' });
    }

    // 創建評價記錄
    const review = {
      id: Date.now().toString(),
      userId,
      workshopId,
      rating,
      comment,
      aspects: aspects || {
        content: rating,
        instructor: rating,
        organization: rating,
        value: rating
      },
      helpful: 0,
      createdAt: new Date()
    };

    // 更新工作坊評分（簡化計算）
    const currentRating = workshop.rating;
    const reviewCount = workshop.reviews;
    const newRating = ((currentRating * reviewCount) + rating) / (reviewCount + 1);
    workshop.rating = Math.round(newRating * 10) / 10;
    workshop.reviews += 1;

    console.log('Workshop review:', review);

    res.json({
      message: 'Review submitted successfully',
      review: {
        id: review.id,
        workshopTitle: workshop.title,
        rating: review.rating,
        comment: review.comment,
        aspects: review.aspects,
        submittedAt: review.createdAt
      },
      workshopUpdatedRating: workshop.rating
    });
  } catch (error) {
    console.error('Submit workshop review error:', error);
    res.status(500).json({ error: 'Failed to submit review' });
  }
});

// 搜尋工作坊
router.get('/search/advanced', async (req, res) => {
  try {
    const {
      q: query,
      categories,
      types,
      difficulties,
      locations,
      instructors,
      dateRange,
      priceRange,
      duration,
      page = 1,
      limit = 12,
      sort = 'relevance'
    } = req.query;

    let results = [...workshops];

    // 文本搜尋
    if (query) {
      const searchTerms = query.toLowerCase().split(' ');
      results = results.filter(workshop => {
        const searchableText = `${workshop.title} ${workshop.description} ${workshop.tags.join(' ')} ${workshop.instructor}`.toLowerCase();
        return searchTerms.every(term => searchableText.includes(term));
      });
    }

    // 多選篩選
    if (categories) {
      const categoryArray = categories.split(',');
      results = results.filter(w => categoryArray.includes(w.category));
    }

    if (types) {
      const typeArray = types.split(',');
      results = results.filter(w => typeArray.includes(w.type));
    }

    if (difficulties) {
      const difficultyArray = difficulties.split(',');
      results = results.filter(w => difficultyArray.includes(w.difficulty));
    }

    if (locations) {
      const locationArray = locations.split(',');
      results = results.filter(w => 
        locationArray.some(loc => w.location.toLowerCase().includes(loc.toLowerCase()))
      );
    }

    if (instructors) {
      const instructorArray = instructors.split(',');
      results = results.filter(w => 
        instructorArray.some(inst => w.instructor.toLowerCase().includes(inst.toLowerCase()))
      );
    }

    // 日期範圍篩選
    if (dateRange) {
      const [startDate, endDate] = dateRange.split(',');
      results = results.filter(w => {
        const workshopDate = new Date(w.date);
        return workshopDate >= new Date(startDate) && workshopDate <= new Date(endDate);
      });
    }

    // 價格範圍篩選
    if (priceRange) {
      const [minPrice, maxPrice] = priceRange.split('-').map(Number);
      results = results.filter(w => w.price >= minPrice && w.price <= maxPrice);
    }

    // 時長篩選
    if (duration) {
      const [minDuration, maxDuration] = duration.split('-').map(Number);
      results = results.filter(w => w.duration >= minDuration && w.duration <= maxDuration);
    }

    // 排序
    const sortOptions = {
      'relevance': (a, b) => b.rating - a.rating,
      'date_asc': (a, b) => new Date(a.date) - new Date(b.date),
      'date_desc': (a, b) => new Date(b.date) - new Date(a.date),
      'price_low': (a, b) => a.price - b.price,
      'price_high': (a, b) => b.price - a.price,
      'rating': (a, b) => b.rating - a.rating,
      'popular': (a, b) => b.currentParticipants - a.currentParticipants
    };

    if (sortOptions[sort]) {
      results.sort(sortOptions[sort]);
    }

    // 分頁
    const skip = (page - 1) * limit;
    const paginatedResults = results.slice(skip, skip + parseInt(limit));

    res.json({
      message: 'Advanced search completed successfully',
      query: {
        text: query,
        filters: { categories, types, difficulties, locations, instructors, dateRange, priceRange, duration }
      },
      results: paginatedResults,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: results.length,
        pages: Math.ceil(results.length / limit)
      },
      searchSummary: {
        totalResults: results.length,
        averagePrice: Math.round(results.reduce((sum, w) => sum + w.price, 0) / results.length),
        upcomingCount: results.filter(w => new Date(w.date) > new Date()).length
      }
    });
  } catch (error) {
    console.error('Advanced workshop search error:', error);
    res.status(500).json({ error: 'Failed to perform advanced search' });
  }
});

module.exports = router;