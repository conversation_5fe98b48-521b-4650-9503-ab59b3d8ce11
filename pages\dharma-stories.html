<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>佛教故事 - 佛學智慧</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.6;
            color: #333;
            background: transparent;
            padding: 20px;
        }

        /* 分類介紹區 */
        .category-intro {
            background: linear-gradient(135deg, #7b1fa2, #9c27b0);
            color: white;
            padding: 40px 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .category-intro::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M30,50 Q50,30 70,50 Q50,70 30,50" fill="white" opacity="0.1"/><circle cx="20" cy="80" r="2" fill="white" opacity="0.1"/><circle cx="80" cy="20" r="1.5" fill="white" opacity="0.1"/></svg>');
            background-size: 80px 80px;
            animation: stories-flow 35s linear infinite;
        }

        @keyframes stories-flow {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .intro-content {
            position: relative;
            z-index: 2;
        }

        .intro-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            animation: stories-sparkle 3s infinite;
        }

        @keyframes stories-sparkle {
            0%, 100% { transform: scale(1) rotate(0deg); text-shadow: 0 0 10px rgba(255,255,255,0.3); }
            25% { transform: scale(1.05) rotate(5deg); text-shadow: 0 0 15px rgba(255,255,255,0.5); }
            50% { transform: scale(1.1) rotate(0deg); text-shadow: 0 0 20px rgba(255,255,255,0.7); }
            75% { transform: scale(1.05) rotate(-5deg); text-shadow: 0 0 15px rgba(255,255,255,0.5); }
        }

        .intro-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .intro-description {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.8;
        }

        /* 故事分類 */
        .stories-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .story-category {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 6px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .story-category:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 30px rgba(0,0,0,0.15);
        }

        .category-header {
            background: linear-gradient(135deg, #8e24aa, #ab47bc);
            color: white;
            padding: 25px;
            text-align: center;
            position: relative;
        }

        .category-header::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 10px solid #ab47bc;
        }

        .category-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }

        .category-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .category-subtitle {
            font-size: 0.95rem;
            opacity: 0.9;
        }

        .stories-list {
            padding: 25px;
        }

        .story-item {
            display: flex;
            align-items: flex-start;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 8px;
            margin-bottom: 5px;
        }

        .story-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .story-item:hover {
            background: linear-gradient(135deg, #fce4ec, #f8bbd9);
            padding-left: 15px;
            transform: translateX(5px);
        }

        .story-number {
            background: linear-gradient(135deg, #9c27b0, #e91e63);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.85rem;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .story-content {
            flex: 1;
        }

        .story-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
            font-size: 1rem;
            line-height: 1.4;
        }

        .story-summary {
            font-size: 0.85rem;
            color: #666;
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .story-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .story-tag {
            background: #e1bee7;
            color: #7b1fa2;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        /* 精選故事 */
        .featured-stories {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .featured-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #7b1fa2;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .featured-story {
            background: linear-gradient(135deg, #f3e5f5, #e1bee7);
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            border-left: 5px solid #9c27b0;
            position: relative;
            overflow: hidden;
        }

        .featured-story::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 60px;
            height: 60px;
            background: rgba(156, 39, 176, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .featured-story-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #4a148c;
            margin-bottom: 15px;
            position: relative;
        }

        .featured-story-content {
            font-size: 0.95rem;
            color: #555;
            line-height: 1.7;
            margin-bottom: 15px;
            position: relative;
        }

        .story-moral {
            background: rgba(156, 39, 176, 0.1);
            border-radius: 8px;
            padding: 15px;
            border-left: 3px solid #9c27b0;
            position: relative;
        }

        .moral-title {
            font-weight: 600;
            color: #7b1fa2;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .moral-content {
            font-size: 0.85rem;
            color: #666;
            line-height: 1.6;
            font-style: italic;
        }

        /* 智慧啟示 */
        .wisdom-insights {
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid #ffb74d;
        }

        .insights-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #ef6c00;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .insight-item {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #ff9800;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            position: relative;
        }

        .insight-icon {
            position: absolute;
            top: 15px;
            right: 15px;
            color: #ff9800;
            font-size: 1.2rem;
        }

        .insight-title {
            font-weight: 600;
            color: #e65100;
            margin-bottom: 10px;
            font-size: 1rem;
            margin-right: 30px;
        }

        .insight-description {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.6;
        }

        /* 故事主題篩選 */
        .theme-filters {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        }

        .filter-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .theme-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .theme-tag {
            background: #f5f5f5;
            color: #666;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .theme-tag:hover,
        .theme-tag.active {
            background: #9c27b0;
            color: white;
            border-color: #7b1fa2;
            transform: translateY(-2px);
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }
            
            .category-intro {
                padding: 30px 20px;
            }
            
            .intro-title {
                font-size: 1.5rem;
            }
            
            .stories-categories {
                grid-template-columns: 1fr;
            }
            
            .featured-stories,
            .wisdom-insights {
                padding: 20px;
            }
            
            .story-item:hover {
                padding-left: 10px;
                transform: translateX(3px);
            }
        }

        @media (max-width: 480px) {
            .theme-tags {
                justify-content: center;
            }
            
            .featured-story {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- 分類介紹 -->
    <section class="category-intro">
        <div class="intro-content">
            <div class="intro-icon">
                <i class="fas fa-book-reader"></i>
            </div>
            <h1 class="intro-title">佛教故事</h1>
            <p class="intro-description">
                透過生動的故事領悟深刻的佛理，在情節中體驗智慧<br>
                每個故事都是一盞明燈，照亮我們修行路上的方向
            </p>
        </div>
    </section>

    <!-- 故事主題篩選 -->
    <section class="theme-filters">
        <h3 class="filter-title">
            <i class="fas fa-filter"></i>
            故事主題
        </h3>
        <div class="theme-tags">
            <span class="theme-tag active" data-theme="all">全部故事</span>
            <span class="theme-tag" data-theme="wisdom">智慧覺醒</span>
            <span class="theme-tag" data-theme="compassion">慈悲修持</span>
            <span class="theme-tag" data-theme="karma">因果報應</span>
            <span class="theme-tag" data-theme="perseverance">精進修行</span>
            <span class="theme-tag" data-theme="enlightenment">開悟證道</span>
            <span class="theme-tag" data-theme="humility">謙卑學習</span>
        </div>
    </section>

    <!-- 故事分類 -->
    <section class="stories-categories">
        <div class="story-category" data-category="buddha">
            <div class="category-header">
                <i class="fas fa-crown category-icon"></i>
                <h3 class="category-title">佛陀本生</h3>
                <p class="category-subtitle">釋迦牟尼佛的前世今生</p>
            </div>
            <div class="stories-list">
                <div class="story-item" data-story="1" data-themes="wisdom,compassion">
                    <div class="story-number">1</div>
                    <div class="story-content">
                        <div class="story-title">慈悲的商主</div>
                        <div class="story-summary">佛陀前世作為商主時，如何以慈悲心拯救五百商人的生命...</div>
                        <div class="story-tags">
                            <span class="story-tag">慈悲</span>
                            <span class="story-tag">捨己救人</span>
                        </div>
                    </div>
                </div>
                <div class="story-item" data-story="2" data-themes="perseverance,wisdom">
                    <div class="story-number">2</div>
                    <div class="story-content">
                        <div class="story-title">忍辱仙人</div>
                        <div class="story-summary">面對歌利王的殘酷考驗，菩薩如何以忍辱波羅蜜度過難關...</div>
                        <div class="story-tags">
                            <span class="story-tag">忍辱</span>
                            <span class="story-tag">智慧</span>
                        </div>
                    </div>
                </div>
                <div class="story-item" data-story="3" data-themes="compassion,karma">
                    <div class="story-number">3</div>
                    <div class="story-content">
                        <div class="story-title">鹿王本生</div>
                        <div class="story-summary">九色鹿王救助落水人，卻遭恩將仇報的因果故事...</div>
                        <div class="story-tags">
                            <span class="story-tag">恩恩怨怨</span>
                            <span class="story-tag">因果</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="story-category" data-category="disciples">
            <div class="category-header">
                <i class="fas fa-users category-icon"></i>
                <h3 class="category-title">弟子傳記</h3>
                <p class="category-subtitle">阿羅漢的修證故事</p>
            </div>
            <div class="stories-list">
                <div class="story-item" data-story="4" data-themes="enlightenment,perseverance">
                    <div class="story-number">1</div>
                    <div class="story-content">
                        <div class="story-title">周利槃陀伽的開悟</div>
                        <div class="story-summary">最愚鈍的弟子如何透過掃地禪修而證得阿羅漢果...</div>
                        <div class="story-tags">
                            <span class="story-tag">愚公移山</span>
                            <span class="story-tag">禪修</span>
                        </div>
                    </div>
                </div>
                <div class="story-item" data-story="5" data-themes="wisdom,humility">
                    <div class="story-number">2</div>
                    <div class="story-content">
                        <div class="story-title">舍利弗的智慧</div>
                        <div class="story-summary">智慧第一的舍利弗如何從外道轉為佛弟子...</div>
                        <div class="story-tags">
                            <span class="story-tag">智慧第一</span>
                            <span class="story-tag">謙卑</span>
                        </div>
                    </div>
                </div>
                <div class="story-item" data-story="6" data-themes="compassion,perseverance">
                    <div class="story-number">3</div>
                    <div class="story-content">
                        <div class="story-title">目犍連救母</div>
                        <div class="story-summary">神通第一的目犍連如何以孝心救度餓鬼道的母親...</div>
                        <div class="story-tags">
                            <span class="story-tag">孝順</span>
                            <span class="story-tag">神通</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="story-category" data-category="zen">
            <div class="category-header">
                <i class="fas fa-meditation category-icon"></i>
                <h3 class="category-title">禪宗公案</h3>
                <p class="category-subtitle">直指人心的禪法故事</p>
            </div>
            <div class="stories-list">
                <div class="story-item" data-story="7" data-themes="enlightenment,wisdom">
                    <div class="story-number">1</div>
                    <div class="story-content">
                        <div class="story-title">拈花微笑</div>
                        <div class="story-summary">佛陀拈花，迦葉微笑，心法相傳的禪宗起源...</div>
                        <div class="story-tags">
                            <span class="story-tag">心法傳承</span>
                            <span class="story-tag">不立文字</span>
                        </div>
                    </div>
                </div>
                <div class="story-item" data-story="8" data-themes="enlightenment,humility">
                    <div class="story-number">2</div>
                    <div class="story-content">
                        <div class="story-title">神秀慧能偈</div>
                        <div class="story-summary">六祖壇經中的著名對偈，見性成佛的法門...</div>
                        <div class="story-tags">
                            <span class="story-tag">見性</span>
                            <span class="story-tag">頓悟</span>
                        </div>
                    </div>
                </div>
                <div class="story-item" data-story="9" data-themes="wisdom,humility">
                    <div class="story-number">3</div>
                    <div class="story-content">
                        <div class="story-title">趙州茶去</div>
                        <div class="story-summary">趙州禪師的日常茶禪，平常心即是道的體現...</div>
                        <div class="story-tags">
                            <span class="story-tag">平常心</span>
                            <span class="story-tag">生活禪</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="story-category" data-category="karma">
            <div class="category-header">
                <i class="fas fa-balance-scale category-icon"></i>
                <h3 class="category-title">因果故事</h3>
                <p class="category-subtitle">善惡有報的真實案例</p>
            </div>
            <div class="stories-list">
                <div class="story-item" data-story="10" data-themes="karma,compassion">
                    <div class="story-number">1</div>
                    <div class="story-content">
                        <div class="story-title">安世高的前世</div>
                        <div class="story-summary">高僧安世高如何化解前世的恩怨情仇...</div>
                        <div class="story-tags">
                            <span class="story-tag">前世今生</span>
                            <span class="story-tag">化解冤仇</span>
                        </div>
                    </div>
                </div>
                <div class="story-item" data-story="11" data-themes="karma,wisdom">
                    <div class="story-number">2</div>
                    <div class="story-content">
                        <div class="story-title">琉璃王滅族</div>
                        <div class="story-summary">釋迦族被滅的深層因果，業力不昧的法則...</div>
                        <div class="story-tags">
                            <span class="story-tag">共業</span>
                            <span class="story-tag">因果不昧</span>
                        </div>
                    </div>
                </div>
                <div class="story-item" data-story="12" data-themes="karma,perseverance">
                    <div class="story-number">3</div>
                    <div class="story-content">
                        <div class="story-title">黃庭堅的轉世</div>
                        <div class="story-summary">宋代詩人黃庭堅的前世今生，文字因緣的果報...</div>
                        <div class="story-tags">
                            <span class="story-tag">文字因緣</span>
                            <span class="story-tag">轉世</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 精選故事 -->
    <section class="featured-stories">
        <h2 class="featured-title">
            <i class="fas fa-star"></i>
            本週精選故事
        </h2>
        
        <div class="featured-story">
            <h3 class="featured-story-title">盲龜值木的比喻</h3>
            <div class="featured-story-content">
                佛陀對弟子們說：「在廣大的海洋中，有一隻瞎眼的海龜，每一百年才浮出海面一次。海面上飄著一塊有孔的木頭，隨著海浪四處漂流。這隻盲龜要剛好在浮出海面的時候，將頭伸進木頭的孔中，這是多麼困難的事啊！但是，眾生要投生為人身，比這還要困難千萬倍。」
            </div>
            <div class="story-moral">
                <div class="moral-title">智慧啟示</div>
                <div class="moral-content">這個比喻告訴我們人身的珍貴難得。我們應該珍惜這難得的人身，精進修行，不要浪費這寶貴的解脫機會。人身難得今已得，佛法難聞今已聞，此身不向今生度，更待何生度此身？</div>
            </div>
        </div>
    </section>

    <!-- 智慧啟示 -->
    <section class="wisdom-insights">
        <h2 class="insights-title">
            <i class="fas fa-lightbulb"></i>
            故事中的智慧啟示
        </h2>
        
        <div class="insight-item">
            <i class="fas fa-heart insight-icon"></i>
            <div class="insight-title">慈悲的力量</div>
            <div class="insight-description">佛教故事中反覆提到慈悲的重要性。真正的慈悲不僅是對他人的關愛，更是智慧的體現。當我們以慈悲心對待一切眾生時，就能超越自我的局限，體驗到心靈的廣闊與自由。</div>
        </div>

        <div class="insight-item">
            <i class="fas fa-sync insight-icon"></i>
            <div class="insight-title">因果法則</div>
            <div class="insight-description">善有善報，惡有惡報，不是不報，時候未到。佛教故事中的因果故事教導我們，每一個行為都會帶來相應的結果。理解因果法則能幫助我們建立正確的人生觀和價值觀。</div>
        </div>

        <div class="insight-item">
            <i class="fas fa-mountain insight-icon"></i>
            <div class="insight-title">精進修行</div>
            <div class="insight-description">修行路上充滿挑戰，但只要保持精進不懈的精神，即使資質平庸也能證得聖果。周利槃陀伽的故事告訴我們，真正的智慧來自於持續不斷的努力和正確的方法。</div>
        </div>

        <div class="insight-item">
            <i class="fas fa-eye insight-icon"></i>
            <div class="insight-title">覺醒的心</div>
            <div class="insight-description">開悟不是遙不可及的神話，而是每個人都具有的本性。禪宗公案提醒我們，覺醒的心就在當下，就在平常的生活中。關鍵是要有敏銳的覺察力和純淨的心靈。</div>
        </div>
    </section>

    <script>
        // 故事點擊事件
        document.addEventListener('click', function(e) {
            const storyItem = e.target.closest('.story-item');
            if (storyItem) {
                const storyId = storyItem.getAttribute('data-story');
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'navigate',
                        page: 'article',
                        articleId: storyId,
                        category: 'stories'
                    }, '*');
                }
            }
        });

        // 主題篩選功能
        document.addEventListener('click', function(e) {
            const themeTag = e.target.closest('.theme-tag');
            if (themeTag) {
                // 移除所有活動狀態
                document.querySelectorAll('.theme-tag').forEach(tag => {
                    tag.classList.remove('active');
                });
                
                // 添加當前活動狀態
                themeTag.classList.add('active');
                
                const selectedTheme = themeTag.getAttribute('data-theme');
                filterStoriesByTheme(selectedTheme);
            }
        });

        // 按主題篩選故事
        function filterStoriesByTheme(theme) {
            const allStories = document.querySelectorAll('.story-item');
            
            allStories.forEach(story => {
                if (theme === 'all') {
                    story.style.display = 'flex';
                } else {
                    const storyThemes = story.getAttribute('data-themes');
                    if (storyThemes && storyThemes.includes(theme)) {
                        story.style.display = 'flex';
                    } else {
                        story.style.display = 'none';
                    }
                }
            });
        }

        // 分類點擊展開/收起
        document.addEventListener('click', function(e) {
            const categoryHeader = e.target.closest('.category-header');
            if (categoryHeader) {
                const category = categoryHeader.parentElement;
                const storiesList = category.querySelector('.stories-list');
                
                if (storiesList.style.display === 'none') {
                    storiesList.style.display = 'block';
                    categoryHeader.style.opacity = '1';
                } else {
                    storiesList.style.display = 'none';
                    categoryHeader.style.opacity = '0.7';
                }
            }
        });

        // 添加載入動畫
        document.addEventListener('DOMContentLoaded', function() {
            const categories = document.querySelectorAll('.story-category');
            categories.forEach((category, index) => {
                category.style.animationDelay = `${index * 0.2}s`;
                category.style.animation = 'fadeInUp 0.8s ease forwards';
            });

            const insights = document.querySelectorAll('.insight-item');
            insights.forEach((insight, index) => {
                insight.style.animationDelay = `${index * 0.1}s`;
                insight.style.animation = 'slideInRight 0.6s ease forwards';
            });
        });

        // CSS 動畫
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(40px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            @keyframes slideInRight {
                from {
                    opacity: 0;
                    transform: translateX(30px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
            
            .story-category,
            .insight-item {
                opacity: 0;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>