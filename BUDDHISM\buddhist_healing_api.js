// 佛教身心療癒網站 - 核心API實作
// 基於您的架構設計的完整實現

const express = require('express');
const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');

const app = express();

// 中間件設定
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 基本速率限制
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分鐘
  max: 100,
  message: "Too many requests from this IP"
});

const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 5,
  message: "Too many authentication attempts"
});

app.use('/api/', generalLimiter);
app.use('/api/auth/', authLimiter);

// ========== 資料庫模型 ==========

// 用戶模型
const userSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true
  },
  password: {
    type: String,
    required: true,
    minlength: 8
  },
  profile: {
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    avatar: String,
    phone: String,
    location: String,
    timezone: { type: String, default: 'Asia/Taipei' }
  },
  membership: {
    plan: { type: String, enum: ['basic', 'premium', 'vip'], default: 'basic' },
    status: { type: String, enum: ['active', 'cancelled', 'expired'], default: 'active' },
    startDate: Date,
    endDate: Date,
    autoRenewal: { type: Boolean, default: false }
  },
  preferences: {
    language: { type: String, default: 'zh-TW' },
    notifications: {
      email: { type: Boolean, default: true },
      push: { type: Boolean, default: true },
      studyReminders: { type: Boolean, default: true }
    },
    privacy: {
      profilePublic: { type: Boolean, default: false },
      progressPublic: { type: Boolean, default: false }
    }
  },
  progress: {
    coursesCompleted: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Course' }],
    currentCourses: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Course' }],
    certificates: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Certificate' }],
    totalStudyTime: { type: Number, default: 0 },
    lastActiveDate: { type: Date, default: Date.now }
  },
  created: { type: Date, default: Date.now },
  updated: { type: Date, default: Date.now }
});

// 課程模型
const courseSchema = new mongoose.Schema({
  title: { type: String, required: true },
  description: { type: String, required: true },
  category: { type: String, required: true },
  difficulty: { type: String, enum: ['beginner', 'intermediate', 'advanced'], required: true },
  duration: { type: Number, required: true }, // 分鐘
  instructor: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  content: {
    modules: [{
      title: String,
      description: String,
      videos: [String],
      documents: [String],
      assignments: [mongoose.Schema.Types.Mixed]
    }]
  },
  prerequisites: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Course' }],
  tags: [String],
  pricing: {
    free: { type: Boolean, default: false },
    membershipRequired: { type: String, enum: ['basic', 'premium', 'vip'] },
    price: { type: Number, default: 0 }
  },
  statistics: {
    enrollments: { type: Number, default: 0 },
    completions: { type: Number, default: 0 },
    averageRating: { type: Number, default: 0 },
    reviews: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Review' }]
  },
  status: { type: String, enum: ['draft', 'published', 'archived'], default: 'draft' },
  created: { type: Date, default: Date.now },
  updated: { type: Date, default: Date.now }
});

// 經文模型
const scriptureSchema = new mongoose.Schema({
  title: { type: String, required: true },
  originalTitle: String,
  category: { type: String, required: true },
  subcategory: String,
  content: {
    text: { type: String, required: true },
    translation: String,
    commentary: String,
    audioUrl: String
  },
  metadata: {
    author: String,
    translator: String,
    source: String,
    language: { type: String, default: 'zh-TW' },
    period: String
  },
  healing: {
    applications: [String],
    techniques: [String],
    benefits: [String]
  },
  tags: [String],
  searchKeywords: [String],
  difficulty: { type: String, enum: ['beginner', 'intermediate', 'advanced'] },
  readingTime: Number,
  popularity: { type: Number, default: 0 },
  created: { type: Date, default: Date.now },
  updated: { type: Date, default: Date.now }
});

// 創建模型
const User = mongoose.model('User', userSchema);
const Course = mongoose.model('Course', courseSchema);
const Scripture = mongoose.model('Scripture', scriptureSchema);

// ========== 中間件 ==========

// JWT 驗證中間件
const authenticate = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ error: 'Access denied. No token provided.' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    const user = await User.findById(decoded.sub);
    
    if (!user) {
      return res.status(401).json({ error: 'Invalid token.' });
    }

    req.user = user;
    next();
  } catch (error) {
    res.status(401).json({ error: 'Invalid token.' });
  }
};

// 權限驗證中間件
const authorize = (roles = []) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required.' });
    }

    if (roles.length && !roles.includes(req.user.membership.plan)) {
      return res.status(403).json({ error: 'Insufficient permissions.' });
    }

    next();
  };
};

// 驗證錯誤處理中間件
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// ========== 身份驗證 API ==========

// 用戶註冊
app.post('/api/auth/register', [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/),
  body('firstName').isLength({ min: 2, max: 50 }).trim(),
  body('lastName').isLength({ min: 2, max: 50 }).trim()
], handleValidationErrors, async (req, res) => {
  try {
    const { email, password, firstName, lastName, phone } = req.body;

    // 檢查用戶是否已存在
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ error: 'User already exists' });
    }

    // 加密密碼
    const hashedPassword = await bcrypt.hash(password, 12);

    // 創建新用戶
    const user = new User({
      email,
      password: hashedPassword,
      profile: {
        firstName,
        lastName,
        phone: phone || ''
      },
      membership: {
        plan: 'basic',
        status: 'active',
        startDate: new Date()
      }
    });

    await user.save();

    // 生成 JWT token
    const token = jwt.sign(
      {
        sub: user._id,
        email: user.email,
        role: user.membership.plan
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '7d' }
    );

    res.status(201).json({
      message: 'User registered successfully',
      token,
      user: {
        id: user._id,
        email: user.email,
        profile: user.profile,
        membership: user.membership
      }
    });
  } catch (error) {
    res.status(500).json({ error: 'Server error during registration' });
  }
});

// 用戶登入
app.post('/api/auth/login', [
  body('email').isEmail().normalizeEmail(),
  body('password').notEmpty()
], handleValidationErrors, async (req, res) => {
  try {
    const { email, password } = req.body;

    // 查找用戶
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // 驗證密碼
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // 更新最後活動時間
    user.progress.lastActiveDate = new Date();
    await user.save();

    // 生成 JWT token
    const token = jwt.sign(
      {
        sub: user._id,
        email: user.email,
        role: user.membership.plan
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '7d' }
    );

    res.json({
      message: 'Login successful',
      token,
      user: {
        id: user._id,
        email: user.email,
        profile: user.profile,
        membership: user.membership,
        progress: user.progress
      }
    });
  } catch (error) {
    res.status(500).json({ error: 'Server error during login' });
  }
});

// ========== 用戶管理 API ==========

// 獲取用戶資料
app.get('/api/users/profile', authenticate, async (req, res) => {
  try {
    const user = await User.findById(req.user._id)
      .select('-password')
      .populate('progress.coursesCompleted', 'title difficulty')
      .populate('progress.currentCourses', 'title difficulty progress');

    res.json({
      user: {
        id: user._id,
        email: user.email,
        profile: user.profile,
        membership: user.membership,
        preferences: user.preferences,
        progress: user.progress
      }
    });
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch user profile' });
  }
});

// 更新用戶資料
app.put('/api/users/profile', authenticate, [
  body('firstName').optional().isLength({ min: 2, max: 50 }).trim(),
  body('lastName').optional().isLength({ min: 2, max: 50 }).trim(),
  body('phone').optional().isMobilePhone(),
  body('location').optional().isLength({ max: 100 }).trim()
], handleValidationErrors, async (req, res) => {
  try {
    const { firstName, lastName, phone, location, timezone } = req.body;

    const updateData = {};
    if (firstName) updateData['profile.firstName'] = firstName;
    if (lastName) updateData['profile.lastName'] = lastName;
    if (phone) updateData['profile.phone'] = phone;
    if (location) updateData['profile.location'] = location;
    if (timezone) updateData['profile.timezone'] = timezone;
    
    updateData.updated = new Date();

    const user = await User.findByIdAndUpdate(
      req.user._id,
      { $set: updateData },
      { new: true }
    ).select('-password');

    res.json({
      message: 'Profile updated successfully',
      user: {
        id: user._id,
        email: user.email,
        profile: user.profile,
        membership: user.membership
      }
    });
  } catch (error) {
    res.status(500).json({ error: 'Failed to update profile' });
  }
});

// ========== 課程管理 API ==========

// 獲取課程列表
app.get('/api/courses', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      category,
      difficulty,
      search,
      sort = 'created'
    } = req.query;

    const skip = (page - 1) * limit;
    const query = { status: 'published' };

    // 添加篩選條件
    if (category) query.category = category;
    if (difficulty) query.difficulty = difficulty;
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // 排序邏輯
    const sortOptions = {
      'created': { created: -1 },
      'popularity': { 'statistics.enrollments': -1 },
      'rating': { 'statistics.averageRating': -1 },
      'title': { title: 1 }
    };

    const courses = await Course.find(query)
      .sort(sortOptions[sort] || sortOptions.created)
      .skip(skip)
      .limit(parseInt(limit))
      .populate('instructor', 'profile.firstName profile.lastName')
      .select('-content.modules'); // 不返回完整內容

    const total = await Course.countDocuments(query);

    res.json({
      courses,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch courses' });
  }
});

// 獲取特定課程
app.get('/api/courses/:id', async (req, res) => {
  try {
    const course = await Course.findOne({
      _id: req.params.id,
      status: 'published'
    }).populate('instructor', 'profile.firstName profile.lastName profile.avatar');

    if (!course) {
      return res.status(404).json({ error: 'Course not found' });
    }

    res.json({ course });
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch course' });
  }
});

// 報名課程
app.post('/api/courses/:id/enroll', authenticate, async (req, res) => {
  try {
    const courseId = req.params.id;
    const userId = req.user._id;

    // 檢查課程是否存在
    const course = await Course.findOne({
      _id: courseId,
      status: 'published'
    });

    if (!course) {
      return res.status(404).json({ error: 'Course not found' });
    }

    // 檢查會員權限
    if (course.pricing.membershipRequired && 
        !['premium', 'vip'].includes(req.user.membership.plan) && 
        course.pricing.membershipRequired !== 'basic') {
      return res.status(403).json({ error: 'Membership upgrade required' });
    }

    // 檢查是否已報名
    const user = await User.findById(userId);
    if (user.progress.currentCourses.includes(courseId) || 
        user.progress.coursesCompleted.includes(courseId)) {
      return res.status(400).json({ error: 'Already enrolled in this course' });
    }

    // 報名課程
    await User.findByIdAndUpdate(userId, {
      $addToSet: { 'progress.currentCourses': courseId }
    });

    // 更新課程統計
    await Course.findByIdAndUpdate(courseId, {
      $inc: { 'statistics.enrollments': 1 }
    });

    res.json({ message: 'Successfully enrolled in course' });
  } catch (error) {
    res.status(500).json({ error: 'Failed to enroll in course' });
  }
});

// ========== 經文搜尋 API ==========

// 搜尋經文
app.get('/api/scriptures/search', async (req, res) => {
  try {
    const {
      q: query,
      category,
      difficulty,
      page = 1,
      limit = 20
    } = req.query;

    const skip = (page - 1) * limit;
    const searchQuery = {};

    // 文本搜尋
    if (query) {
      searchQuery.$or = [
        { title: { $regex: query, $options: 'i' } },
        { 'content.text': { $regex: query, $options: 'i' } },
        { 'content.translation': { $regex: query, $options: 'i' } },
        { tags: { $in: [new RegExp(query, 'i')] } },
        { searchKeywords: { $in: [new RegExp(query, 'i')] } }
      ];
    }

    // 分類篩選
    if (category) searchQuery.category = category;
    if (difficulty) searchQuery.difficulty = difficulty;

    const scriptures = await Scripture.find(searchQuery)
      .sort({ popularity: -1, created: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .select('-content.text -content.translation'); // 不返回完整內容

    const total = await Scripture.countDocuments(searchQuery);

    res.json({
      scriptures,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    res.status(500).json({ error: 'Failed to search scriptures' });
  }
});

// 獲取特定經文
app.get('/api/scriptures/:id', async (req, res) => {
  try {
    const scripture = await Scripture.findById(req.params.id);
    
    if (!scripture) {
      return res.status(404).json({ error: 'Scripture not found' });
    }

    // 更新瀏覽次數
    await Scripture.findByIdAndUpdate(req.params.id, {
      $inc: { popularity: 1 }
    });

    res.json({ scripture });
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch scripture' });
  }
});

// ========== 健康檢查 ==========

app.get('/health', async (req, res) => {
  const healthCheck = {
    uptime: process.uptime(),
    timestamp: Date.now(),
    status: 'OK',
    services: {
      database: 'connected', // 實際應檢查 MongoDB 連接
      server: 'running'
    }
  };

  res.status(200).json(healthCheck);
});

// ========== 錯誤處理 ==========

// 404 處理
app.use((req, res) => {
  res.status(404).json({ error: 'API endpoint not found' });
});

// 全域錯誤處理
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// ========== 服務器啟動 ==========

const PORT = process.env.PORT || 3000;
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/buddhist_healing';

mongoose.connect(MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => {
  console.log('Connected to MongoDB');
  app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
  });
})
.catch((error) => {
  console.error('MongoDB connection error:', error);
  process.exit(1);
});

module.exports = app;