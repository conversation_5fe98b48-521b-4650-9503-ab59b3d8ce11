<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>澄源閱讀 - 測試頁面</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #cce8ff; color: #004085; border: 1px solid #bee5eb; }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>澄源閱讀 - 登入測試</h1>
        
        <div id="auth-status" class="status info">
            檢查中...
        </div>
        
        <div>
            <h3>測試密碼</h3>
            <button class="button" onclick="testPassword('chengyuan2025')">chengyuan2025</button>
            <button class="button" onclick="testPassword('dharma123')">dharma123</button>
            <button class="button" onclick="testPassword('healing2025')">healing2025</button>
            <button class="button" onclick="testPassword('buddha@wisdom')">buddha@wisdom</button>
            <button class="button" onclick="testPassword('mindfulness2025')">mindfulness2025</button>
        </div>
        
        <div>
            <h3>導航測試</h3>
            <button class="button" onclick="goToMain()">前往主站</button>
            <button class="button" onclick="clearAuth()">清除登入狀態</button>
        </div>
        
        <div>
            <h3>調試信息</h3>
            <div id="debug-info"></div>
        </div>
    </div>

    <script>
        // 檢查認證狀態
        function checkAuthStatus() {
            const authStatus = localStorage.getItem('chengyuan_auth');
            const statusDiv = document.getElementById('auth-status');
            
            if (authStatus === 'true') {
                statusDiv.className = 'status success';
                statusDiv.textContent = '✓ 已登入 - 可以訪問主站';
            } else {
                statusDiv.className = 'status error';
                statusDiv.textContent = '✗ 未登入 - 需要密碼驗證';
            }
            
            updateDebugInfo();
        }
        
        // 測試密碼
        function testPassword(password) {
            const validPasswords = [
                'chengyuan2025',
                'dharma123', 
                'healing2025',
                'buddha@wisdom',
                'mindfulness2025'
            ];
            
            if (validPasswords.includes(password)) {
                localStorage.setItem('chengyuan_auth', 'true');
                alert('密碼正確！登入狀態已保存');
                checkAuthStatus();
            } else {
                alert('密碼錯誤！');
            }
        }
        
        // 前往主站
        function goToMain() {
            window.location.href = 'index.html';
        }
        
        // 清除認證
        function clearAuth() {
            localStorage.removeItem('chengyuan_auth');
            alert('登入狀態已清除');
            checkAuthStatus();
        }
        
        // 更新調試信息
        function updateDebugInfo() {
            const debugDiv = document.getElementById('debug-info');
            debugDiv.innerHTML = `
                <strong>LocalStorage:</strong><br>
                chengyuan_auth: ${localStorage.getItem('chengyuan_auth')}<br>
                <strong>當前URL:</strong> ${window.location.href}<br>
                <strong>時間:</strong> ${new Date().toLocaleString()}
            `;
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthStatus();
        });
    </script>
</body>
</html>