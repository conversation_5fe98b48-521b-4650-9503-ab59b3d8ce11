const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const authenticate = require('../middleware/auth');

// 驗證錯誤處理中間件
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// 管理員權限檢查中間件
const requireAdmin = (req, res, next) => {
  if (!req.user || req.user.role !== 'admin') {
    return res.status(403).json({ error: 'Admin access required' });
  }
  next();
};

// 編輯權限檢查中間件
const requireEditor = (req, res, next) => {
  if (!req.user || !['admin', 'editor'].includes(req.user.role)) {
    return res.status(403).json({ error: 'Editor access required' });
  }
  next();
};

// 模擬內容數據
const cmsContent = {
  pages: [
    {
      id: '1',
      slug: 'home',
      title: '首頁',
      content: '<h1>歡迎來到佛教身心療癒網站</h1><p>這裡是您心靈成長的園地...</p>',
      metaTitle: '佛教身心療癒 - 心靈成長的園地',
      metaDescription: '專業的佛教療癒課程、經文學習、正念修持，助您找到內在平靜',
      status: 'published',
      author: 'admin',
      createdAt: '2023-10-01',
      updatedAt: '2023-12-01',
      publishedAt: '2023-10-01',
      seoScore: 85,
      viewCount: 15420
    },
    {
      id: '2',
      slug: 'about',
      title: '關於我們',
      content: '<h1>關於佛教身心療癒網站</h1><p>我們致力於推廣佛教療癒智慧...</p>',
      metaTitle: '關於我們 - 佛教身心療癒',
      metaDescription: '了解我們的使命、願景和團隊，一起探索佛教療癒的奧秘',
      status: 'published',
      author: 'editor1',
      createdAt: '2023-10-02',
      updatedAt: '2023-11-15',
      publishedAt: '2023-10-02',
      seoScore: 78,
      viewCount: 8930
    }
  ],
  articles: [
    {
      id: '1',
      title: '正念冥想的科學證據',
      slug: 'mindfulness-scientific-evidence',
      content: '<h1>正念冥想的科學證據</h1><p>近年來，越來越多的科學研究證實...</p>',
      excerpt: '探討正念冥想對身心健康的科學研究成果',
      category: 'research',
      tags: ['正念', '冥想', '科學研究', '健康'],
      author: 'dr_chen',
      status: 'published',
      featured: true,
      readingTime: 8,
      createdAt: '2023-11-01',
      updatedAt: '2023-11-20',
      publishedAt: '2023-11-01',
      viewCount: 2340,
      likes: 156,
      comments: 23,
      shares: 45
    },
    {
      id: '2',
      title: '佛教音樂療法入門指南',
      slug: 'buddhist-music-therapy-guide',
      content: '<h1>佛教音樂療法入門指南</h1><p>音樂在佛教修持中占有重要地位...</p>',
      excerpt: '了解如何運用佛教音樂進行心靈療癒',
      category: 'therapy',
      tags: ['音樂療法', '佛教音樂', '療癒', '修持'],
      author: 'master_hui',
      status: 'published',
      featured: false,
      readingTime: 6,
      createdAt: '2023-11-10',
      updatedAt: '2023-11-25',
      publishedAt: '2023-11-10',
      viewCount: 1870,
      likes: 98,
      comments: 12,
      shares: 28
    }
  ],
  media: [
    {
      id: '1',
      filename: 'meditation-hall.jpg',
      originalName: '禪修大廳.jpg',
      url: '/media/meditation-hall.jpg',
      type: 'image',
      size: 2048576, // bytes
      dimensions: { width: 1920, height: 1080 },
      alt: '寧靜的禪修大廳',
      uploadedBy: 'admin',
      uploadedAt: '2023-10-15',
      usageCount: 5
    },
    {
      id: '2',
      filename: 'chanting-audio.mp3',
      originalName: '心經唱誦.mp3',
      url: '/media/chanting-audio.mp3',
      type: 'audio',
      size: 5242880,
      duration: 300, // seconds
      uploadedBy: 'editor1',
      uploadedAt: '2023-10-20',
      usageCount: 12
    }
  ]
};

// ========== 頁面管理 ==========

// 獲取頁面列表
router.get('/pages', authenticate, requireEditor, async (req, res) => {
  try {
    const { status, author, search, page = 1, limit = 20 } = req.query;
    let pages = [...cmsContent.pages];

    // 篩選
    if (status) pages = pages.filter(p => p.status === status);
    if (author) pages = pages.filter(p => p.author === author);
    if (search) {
      pages = pages.filter(p => 
        p.title.toLowerCase().includes(search.toLowerCase()) ||
        p.content.toLowerCase().includes(search.toLowerCase())
      );
    }

    // 分頁
    const skip = (page - 1) * limit;
    const paginatedPages = pages.slice(skip, skip + parseInt(limit));

    res.json({
      message: 'Pages retrieved successfully',
      pages: paginatedPages,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: pages.length,
        pages: Math.ceil(pages.length / limit)
      },
      statistics: {
        totalPages: cmsContent.pages.length,
        publishedPages: cmsContent.pages.filter(p => p.status === 'published').length,
        draftPages: cmsContent.pages.filter(p => p.status === 'draft').length
      }
    });
  } catch (error) {
    console.error('Get pages error:', error);
    res.status(500).json({ error: 'Failed to fetch pages' });
  }
});

// 創建新頁面
router.post('/pages', authenticate, requireEditor, [
  body('title').isLength({ min: 1, max: 200 }).withMessage('Title must be 1-200 characters'),
  body('slug').isLength({ min: 1, max: 100 }).matches(/^[a-z0-9-]+$/).withMessage('Invalid slug format'),
  body('content').isLength({ min: 1 }).withMessage('Content is required'),
  body('metaTitle').optional().isLength({ max: 60 }).withMessage('Meta title too long'),
  body('metaDescription').optional().isLength({ max: 160 }).withMessage('Meta description too long'),
  body('status').isIn(['draft', 'published']).withMessage('Invalid status')
], handleValidationErrors, async (req, res) => {
  try {
    const {
      title,
      slug,
      content,
      metaTitle,
      metaDescription,
      status = 'draft'
    } = req.body;

    // 檢查slug是否已存在
    const existingPage = cmsContent.pages.find(p => p.slug === slug);
    if (existingPage) {
      return res.status(400).json({ error: 'Slug already exists' });
    }

    const newPage = {
      id: Date.now().toString(),
      slug,
      title,
      content,
      metaTitle: metaTitle || title,
      metaDescription: metaDescription || '',
      status,
      author: req.user.username || req.user._id,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      publishedAt: status === 'published' ? new Date().toISOString() : null,
      seoScore: 0,
      viewCount: 0
    };

    cmsContent.pages.push(newPage);

    res.status(201).json({
      message: 'Page created successfully',
      page: newPage
    });
  } catch (error) {
    console.error('Create page error:', error);
    res.status(500).json({ error: 'Failed to create page' });
  }
});

// 更新頁面
router.put('/pages/:id', authenticate, requireEditor, [
  body('title').optional().isLength({ min: 1, max: 200 }).withMessage('Title must be 1-200 characters'),
  body('content').optional().isLength({ min: 1 }).withMessage('Content cannot be empty'),
  body('metaTitle').optional().isLength({ max: 60 }).withMessage('Meta title too long'),
  body('metaDescription').optional().isLength({ max: 160 }).withMessage('Meta description too long'),
  body('status').optional().isIn(['draft', 'published']).withMessage('Invalid status')
], handleValidationErrors, async (req, res) => {
  try {
    const pageId = req.params.id;
    const updates = req.body;

    const pageIndex = cmsContent.pages.findIndex(p => p.id === pageId);
    if (pageIndex === -1) {
      return res.status(404).json({ error: 'Page not found' });
    }

    const page = cmsContent.pages[pageIndex];
    
    // 更新頁面
    Object.keys(updates).forEach(key => {
      if (updates[key] !== undefined) {
        page[key] = updates[key];
      }
    });
    
    page.updatedAt = new Date().toISOString();
    
    // 如果狀態改為published且之前未發布，設置發布時間
    if (updates.status === 'published' && !page.publishedAt) {
      page.publishedAt = new Date().toISOString();
    }

    res.json({
      message: 'Page updated successfully',
      page
    });
  } catch (error) {
    console.error('Update page error:', error);
    res.status(500).json({ error: 'Failed to update page' });
  }
});

// ========== 文章管理 ==========

// 獲取文章列表
router.get('/articles', authenticate, requireEditor, async (req, res) => {
  try {
    const { 
      status, 
      category, 
      author, 
      featured,
      search, 
      page = 1, 
      limit = 20,
      sort = 'newest'
    } = req.query;

    let articles = [...cmsContent.articles];

    // 篩選
    if (status) articles = articles.filter(a => a.status === status);
    if (category) articles = articles.filter(a => a.category === category);
    if (author) articles = articles.filter(a => a.author === author);
    if (featured === 'true') articles = articles.filter(a => a.featured === true);
    if (search) {
      articles = articles.filter(a => 
        a.title.toLowerCase().includes(search.toLowerCase()) ||
        a.content.toLowerCase().includes(search.toLowerCase()) ||
        a.tags.some(tag => tag.toLowerCase().includes(search.toLowerCase()))
      );
    }

    // 排序
    const sortOptions = {
      'newest': (a, b) => new Date(b.createdAt) - new Date(a.createdAt),
      'oldest': (a, b) => new Date(a.createdAt) - new Date(b.createdAt),
      'most_viewed': (a, b) => b.viewCount - a.viewCount,
      'most_liked': (a, b) => b.likes - a.likes,
      'title': (a, b) => a.title.localeCompare(b.title)
    };

    if (sortOptions[sort]) {
      articles.sort(sortOptions[sort]);
    }

    // 分頁
    const skip = (page - 1) * limit;
    const paginatedArticles = articles.slice(skip, skip + parseInt(limit));

    res.json({
      message: 'Articles retrieved successfully',
      articles: paginatedArticles,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: articles.length,
        pages: Math.ceil(articles.length / limit)
      },
      statistics: {
        totalArticles: cmsContent.articles.length,
        publishedArticles: cmsContent.articles.filter(a => a.status === 'published').length,
        draftArticles: cmsContent.articles.filter(a => a.status === 'draft').length,
        featuredArticles: cmsContent.articles.filter(a => a.featured).length
      }
    });
  } catch (error) {
    console.error('Get articles error:', error);
    res.status(500).json({ error: 'Failed to fetch articles' });
  }
});

// 創建新文章
router.post('/articles', authenticate, requireEditor, [
  body('title').isLength({ min: 1, max: 200 }).withMessage('Title must be 1-200 characters'),
  body('slug').isLength({ min: 1, max: 100 }).matches(/^[a-z0-9-]+$/).withMessage('Invalid slug format'),
  body('content').isLength({ min: 1 }).withMessage('Content is required'),
  body('excerpt').optional().isLength({ max: 300 }).withMessage('Excerpt too long'),
  body('category').isIn(['research', 'therapy', 'meditation', 'philosophy', 'news']).withMessage('Invalid category'),
  body('tags').isArray().withMessage('Tags must be an array'),
  body('status').isIn(['draft', 'published']).withMessage('Invalid status')
], handleValidationErrors, async (req, res) => {
  try {
    const {
      title,
      slug,
      content,
      excerpt,
      category,
      tags,
      status = 'draft',
      featured = false
    } = req.body;

    // 檢查slug是否已存在
    const existingArticle = cmsContent.articles.find(a => a.slug === slug);
    if (existingArticle) {
      return res.status(400).json({ error: 'Slug already exists' });
    }

    // 計算閱讀時間（簡化計算：每分鐘250字）
    const wordCount = content.replace(/<[^>]*>/g, '').length;
    const readingTime = Math.ceil(wordCount / 250);

    const newArticle = {
      id: Date.now().toString(),
      title,
      slug,
      content,
      excerpt: excerpt || content.replace(/<[^>]*>/g, '').substring(0, 150) + '...',
      category,
      tags,
      author: req.user.username || req.user._id,
      status,
      featured,
      readingTime,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      publishedAt: status === 'published' ? new Date().toISOString() : null,
      viewCount: 0,
      likes: 0,
      comments: 0,
      shares: 0
    };

    cmsContent.articles.push(newArticle);

    res.status(201).json({
      message: 'Article created successfully',
      article: newArticle
    });
  } catch (error) {
    console.error('Create article error:', error);
    res.status(500).json({ error: 'Failed to create article' });
  }
});

// ========== 媒體管理 ==========

// 獲取媒體文件列表
router.get('/media', authenticate, requireEditor, async (req, res) => {
  try {
    const { type, search, page = 1, limit = 20 } = req.query;
    let media = [...cmsContent.media];

    // 篩選
    if (type) media = media.filter(m => m.type === type);
    if (search) {
      media = media.filter(m => 
        m.filename.toLowerCase().includes(search.toLowerCase()) ||
        m.originalName.toLowerCase().includes(search.toLowerCase()) ||
        (m.alt && m.alt.toLowerCase().includes(search.toLowerCase()))
      );
    }

    // 分頁
    const skip = (page - 1) * limit;
    const paginatedMedia = media.slice(skip, skip + parseInt(limit));

    // 統計
    const statistics = {
      totalFiles: cmsContent.media.length,
      totalSize: cmsContent.media.reduce((sum, m) => sum + m.size, 0),
      typeDistribution: {
        image: cmsContent.media.filter(m => m.type === 'image').length,
        video: cmsContent.media.filter(m => m.type === 'video').length,
        audio: cmsContent.media.filter(m => m.type === 'audio').length,
        document: cmsContent.media.filter(m => m.type === 'document').length
      }
    };

    res.json({
      message: 'Media files retrieved successfully',
      media: paginatedMedia,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: media.length,
        pages: Math.ceil(media.length / limit)
      },
      statistics
    });
  } catch (error) {
    console.error('Get media error:', error);
    res.status(500).json({ error: 'Failed to fetch media files' });
  }
});

// 上傳媒體文件
router.post('/media/upload', authenticate, requireEditor, [
  body('filename').notEmpty().withMessage('Filename is required'),
  body('type').isIn(['image', 'video', 'audio', 'document']).withMessage('Invalid file type'),
  body('size').isInt({ min: 1 }).withMessage('File size must be positive'),
  body('alt').optional().isLength({ max: 200 }).withMessage('Alt text too long')
], handleValidationErrors, async (req, res) => {
  try {
    const { filename, originalName, type, size, alt, dimensions, duration } = req.body;

    // 檢查文件大小限制
    const maxSizes = {
      image: 10 * 1024 * 1024, // 10MB
      video: 100 * 1024 * 1024, // 100MB
      audio: 50 * 1024 * 1024, // 50MB
      document: 20 * 1024 * 1024 // 20MB
    };

    if (size > maxSizes[type]) {
      return res.status(400).json({ 
        error: `File too large. Maximum size for ${type} is ${maxSizes[type] / 1024 / 1024}MB` 
      });
    }

    const newMedia = {
      id: Date.now().toString(),
      filename,
      originalName: originalName || filename,
      url: `/media/${filename}`,
      type,
      size,
      dimensions: type === 'image' ? dimensions : undefined,
      duration: type === 'audio' || type === 'video' ? duration : undefined,
      alt: alt || '',
      uploadedBy: req.user.username || req.user._id,
      uploadedAt: new Date().toISOString(),
      usageCount: 0
    };

    cmsContent.media.push(newMedia);

    res.status(201).json({
      message: 'File uploaded successfully',
      media: newMedia
    });
  } catch (error) {
    console.error('Upload media error:', error);
    res.status(500).json({ error: 'Failed to upload file' });
  }
});

// ========== 系統設定 ==========

// 獲取網站設定
router.get('/settings', authenticate, requireAdmin, async (req, res) => {
  try {
    const settings = {
      site: {
        title: '佛教身心療癒網站',
        tagline: '心靈成長的園地',
        description: '專業的佛教療癒課程、經文學習、正念修持',
        url: 'https://buddhist-healing.com',
        email: '<EMAIL>',
        timezone: 'Asia/Taipei',
        language: 'zh-TW'
      },
      seo: {
        metaTitle: '佛教身心療癒 - 心靈成長的園地',
        metaDescription: '專業的佛教療癒課程、經文學習、正念修持，助您找到內在平靜',
        keywords: ['佛教', '療癒', '正念', '冥想', '心靈成長'],
        googleAnalytics: 'GA-XXXXX-X',
        googleSearchConsole: 'verified',
        robotsTxt: 'allow'
      },
      security: {
        twoFactorAuth: true,
        sessionTimeout: 3600,
        maxLoginAttempts: 5,
        passwordPolicy: {
          minLength: 8,
          requireUppercase: true,
          requireLowercase: true,
          requireNumbers: true,
          requireSymbols: false
        }
      },
      features: {
        userRegistration: true,
        guestComments: false,
        socialLogin: true,
        newsletter: true,
        maintenance: false
      }
    };

    res.json({
      message: 'Settings retrieved successfully',
      settings
    });
  } catch (error) {
    console.error('Get settings error:', error);
    res.status(500).json({ error: 'Failed to fetch settings' });
  }
});

// 更新網站設定
router.put('/settings/:category', authenticate, requireAdmin, async (req, res) => {
  try {
    const { category } = req.params;
    const updates = req.body;

    const validCategories = ['site', 'seo', 'security', 'features'];
    if (!validCategories.includes(category)) {
      return res.status(400).json({ error: 'Invalid settings category' });
    }

    // 模擬設定更新
    console.log(`Updating ${category} settings:`, updates);

    res.json({
      message: `${category} settings updated successfully`,
      category,
      updates,
      updatedAt: new Date()
    });
  } catch (error) {
    console.error('Update settings error:', error);
    res.status(500).json({ error: 'Failed to update settings' });
  }
});

// ========== 分析統計 ==========

// 獲取網站統計
router.get('/analytics', authenticate, requireEditor, async (req, res) => {
  try {
    const { period = '30d' } = req.query;

    // 模擬統計數據
    const analytics = {
      overview: {
        totalViews: 125430,
        uniqueVisitors: 45670,
        pageviews: 198250,
        bounceRate: 0.35,
        avgSessionDuration: 245, // seconds
        conversionRate: 0.12
      },
      content: {
        topPages: [
          { path: '/', views: 15420, title: '首頁' },
          { path: '/courses', views: 8930, title: '課程列表' },
          { path: '/scriptures', views: 7650, title: '經文搜尋' }
        ],
        topArticles: [
          { id: '1', title: '正念冥想的科學證據', views: 2340 },
          { id: '2', title: '佛教音樂療法入門指南', views: 1870 }
        ]
      },
      traffic: {
        sources: [
          { source: 'direct', visits: 18500, percentage: 35 },
          { source: 'search', visits: 15000, percentage: 28 },
          { source: 'social', visits: 12000, percentage: 23 },
          { source: 'referral', visits: 7500, percentage: 14 }
        ],
        devices: [
          { device: 'mobile', visits: 28000, percentage: 53 },
          { device: 'desktop', visits: 20000, percentage: 38 },
          { device: 'tablet', visits: 5000, percentage: 9 }
        ]
      },
      users: {
        totalUsers: 8750,
        newUsers: 1250,
        activeUsers: 3400,
        retentionRate: 0.68
      }
    };

    res.json({
      message: 'Analytics data retrieved successfully',
      period,
      analytics,
      generatedAt: new Date()
    });
  } catch (error) {
    console.error('Get analytics error:', error);
    res.status(500).json({ error: 'Failed to fetch analytics data' });
  }
});

module.exports = router;