/**
 * 澄源閱讀 - 主要 API 路由處理器
 * Cloudflare Workers 統一入口點
 * 
 * 統一處理所有 API 請求並路由到相應的處理函數
 */

import articleHandler from './articles.js';
import searchHandler from './search.js';
import uploadHandler from './upload.js';
import favoritesHandler from './favorites.js';

// CORS 處理
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Session-ID',
  'Access-Control-Max-Age': '86400',
};

// 處理 CORS 預檢請求
function handleCORS(request) {
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }
}

// 錯誤處理
function createErrorResponse(message, status = 400) {
  return new Response(JSON.stringify({
    success: false,
    error: message,
    timestamp: new Date().toISOString(),
    version: 'v1'
  }), {
    status,
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders
    }
  });
}

// 成功響應
function createSuccessResponse(data, meta = {}) {
  return new Response(JSON.stringify({
    success: true,
    data,
    meta: {
      ...meta,
      version: 'v1',
      server: 'Cloudflare Workers'
    },
    timestamp: new Date().toISOString()
  }), {
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders
    }
  });
}

// 健康檢查
async function healthCheck(request, env) {
  try {
    // 檢查數據庫連接
    const dbCheck = await env.DB.prepare('SELECT 1').first();
    
    // 檢查 R2 存儲（可選）
    let r2Check = true;
    try {
      await env.R2_BUCKET.head('health-check');
    } catch (error) {
      r2Check = false;
    }

    return createSuccessResponse({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: dbCheck ? 'connected' : 'disconnected',
        r2_storage: r2Check ? 'connected' : 'warning',
        cache: env.CACHE ? 'connected' : 'disabled'
      },
      environment: env.ENVIRONMENT || 'development',
      version: '1.0.0'
    });

  } catch (error) {
    console.error('Health check failed:', error);
    return createErrorResponse('Service unavailable', 503);
  }
}

// API 文檔
async function getApiDocs(request, env) {
  const docs = {
    name: "澄源閱讀 API",
    version: "1.0.0",
    description: "佛學智慧與身心療癒內容管理 API",
    baseUrl: env.SITE_URL || "https://chengyuan-reading.pages.dev",
    endpoints: {
      health: {
        path: "/api/health",
        method: "GET",
        description: "健康檢查"
      },
      articles: {
        list: {
          path: "/api/articles",
          method: "GET",
          description: "獲取文章列表",
          parameters: {
            page: "頁碼 (默認: 1)",
            limit: "每頁數量 (默認: 12, 最大: 50)",
            category: "分類篩選",
            tag: "標籤篩選",
            sort: "排序方式 (published_at, views, likes)",
            order: "排序順序 (ASC, DESC)"
          }
        },
        detail: {
          path: "/api/articles/{id}",
          method: "GET",
          description: "獲取文章詳情"
        },
        byCategory: {
          path: "/api/articles/category/{category}",
          method: "GET",
          description: "按分類獲取文章"
        },
        featured: {
          path: "/api/articles/featured",
          method: "GET",
          description: "獲取精選文章"
        },
        popular: {
          path: "/api/articles/popular",
          method: "GET",
          description: "獲取熱門文章"
        },
        like: {
          path: "/api/articles/{id}/like",
          method: "POST",
          description: "點讚/取消點讚文章"
        },
        view: {
          path: "/api/articles/{id}/view",
          method: "POST",
          description: "記錄文章瀏覽"
        }
      },
      search: {
        search: {
          path: "/api/search",
          method: "GET/POST",
          description: "搜索文章",
          parameters: {
            q: "搜索關鍵詞",
            category: "分類篩選",
            sort: "排序方式 (relevance, date, views, likes)",
            page: "頁碼",
            limit: "每頁數量"
          }
        },
        suggestions: {
          path: "/api/search/suggestions",
          method: "GET",
          description: "搜索建議"
        },
        popular: {
          path: "/api/search/popular",
          method: "GET",
          description: "熱門搜索"
        },
        history: {
          path: "/api/search/history",
          method: "GET",
          description: "搜索歷史"
        },
        stats: {
          path: "/api/search/stats",
          method: "GET",
          description: "搜索統計"
        }
      },
      upload: {
        upload: {
          path: "/api/upload",
          method: "POST",
          description: "上傳圖片",
          contentType: "multipart/form-data",
          fields: {
            file: "圖片文件",
            altText: "圖片描述",
            caption: "圖片標題",
            articleId: "關聯文章ID"
          }
        },
        fromUrl: {
          path: "/api/upload/url",
          method: "POST",
          description: "從URL上傳圖片"
        },
        images: {
          path: "/api/images",
          method: "GET",
          description: "獲取圖片列表"
        },
        imageInfo: {
          path: "/api/images/{id}",
          method: "GET",
          description: "獲取圖片信息"
        },
        deleteImage: {
          path: "/api/images/{id}",
          method: "DELETE",
          description: "刪除圖片"
        }
      },
      categories: {
        path: "/api/categories",
        method: "GET",
        description: "獲取分類列表"
      },
      tags: {
        path: "/api/tags",
        method: "GET",
        description: "獲取標籤列表"
      }
    },
    responseFormat: {
      success: {
        success: true,
        data: "響應數據",
        meta: "元數據（分頁信息等）",
        timestamp: "響應時間戳"
      },
      error: {
        success: false,
        error: "錯誤信息",
        timestamp: "響應時間戳"
      }
    }
  };

  return createSuccessResponse(docs);
}

// 統計信息
async function getStats(request, env) {
  try {
    // 獲取文章統計
    const articleStats = await env.DB.prepare(`
      SELECT 
        COUNT(*) as total_articles,
        COUNT(CASE WHEN status = 'published' THEN 1 END) as published_articles,
        COUNT(CASE WHEN created_at > datetime('now', '-7 days') THEN 1 END) as articles_this_week,
        SUM(views) as total_views,
        SUM(likes) as total_likes
      FROM articles
    `).first();

    // 獲取分類統計
    const categoryStats = await env.DB.prepare(`
      SELECT 
        category,
        COUNT(*) as article_count,
        SUM(views) as total_views,
        SUM(likes) as total_likes
      FROM articles 
      WHERE status = 'published'
      GROUP BY category
      ORDER BY article_count DESC
    `).all();

    // 獲取搜索統計
    const searchStats = await env.DB.prepare(`
      SELECT 
        COUNT(*) as total_searches,
        COUNT(DISTINCT session_id) as unique_users,
        COUNT(CASE WHEN created_at > datetime('now', '-24 hours') THEN 1 END) as searches_today
      FROM search_logs
      WHERE created_at > datetime('now', '-30 days')
    `).first();

    // 獲取圖片統計
    const imageStats = await env.DB.prepare(`
      SELECT 
        COUNT(*) as total_images,
        SUM(file_size) as total_size,
        COUNT(CASE WHEN uploaded_at > datetime('now', '-7 days') THEN 1 END) as uploads_this_week
      FROM images
    `).first();

    return createSuccessResponse({
      articles: {
        total: articleStats.total_articles,
        published: articleStats.published_articles,
        thisWeek: articleStats.articles_this_week,
        totalViews: articleStats.total_views,
        totalLikes: articleStats.total_likes
      },
      categories: categoryStats.results.map(cat => ({
        category: cat.category,
        articleCount: cat.article_count,
        totalViews: cat.total_views,
        totalLikes: cat.total_likes
      })),
      search: {
        totalSearches: searchStats.total_searches,
        uniqueUsers: searchStats.unique_users,
        searchesToday: searchStats.searches_today
      },
      images: {
        totalImages: imageStats.total_images,
        totalSize: imageStats.total_size,
        uploadsThisWeek: imageStats.uploads_this_week
      },
      generatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get stats error:', error);
    return createErrorResponse('Failed to fetch statistics');
  }
}

// 清理任務（定時執行）
async function cleanupTasks(request, env) {
  try {
    const results = {
      oldSearchLogs: 0,
      oldArticleViews: 0,
      unusedImages: 0
    };

    // 清理30天前的搜索日誌
    const searchCleanup = await env.DB.prepare(`
      DELETE FROM search_logs 
      WHERE created_at < datetime('now', '-30 days')
    `).run();
    results.oldSearchLogs = searchCleanup.changes;

    // 清理90天前的瀏覽記錄
    const viewsCleanup = await env.DB.prepare(`
      DELETE FROM article_views 
      WHERE created_at < datetime('now', '-90 days')
    `).run();
    results.oldArticleViews = viewsCleanup.changes;

    // 清理未使用的圖片（90天前且沒有關聯文章）
    const unusedImages = await env.DB.prepare(`
      DELETE FROM images 
      WHERE uploaded_at < datetime('now', '-90 days') 
        AND article_id IS NULL
    `).run();
    results.unusedImages = unusedImages.changes;

    return createSuccessResponse(results, {
      message: 'Cleanup tasks completed successfully'
    });

  } catch (error) {
    console.error('Cleanup tasks error:', error);
    return createErrorResponse('Cleanup tasks failed');
  }
}

// 主處理函數
export default {
  async fetch(request, env, ctx) {
    // 處理 CORS
    const corsResponse = handleCORS(request);
    if (corsResponse) return corsResponse;

    try {
      const url = new URL(request.url);
      const path = url.pathname;
      const method = request.method;

      // 根路徑 - 返回 API 歡迎信息
      if (path === '/' || path === '/api') {
        return createSuccessResponse({
          name: "澄源閱讀 API",
          version: "1.0.0",
          message: "歡迎使用澄源閱讀 API",
          documentation: `${env.SITE_URL}/api/docs`,
          endpoints: {
            health: "/api/health",
            docs: "/api/docs",
            stats: "/api/stats"
          }
        });
      }

      // 健康檢查
      if (path === '/api/health') {
        return await healthCheck(request, env);
      }

      // API 文檔
      if (path === '/api/docs') {
        return await getApiDocs(request, env);
      }

      // 統計信息
      if (path === '/api/stats') {
        return await getStats(request, env);
      }

      // 清理任務
      if (path === '/api/cleanup' && method === 'POST') {
        return await cleanupTasks(request, env);
      }

      // 文章相關 API
      if (path.startsWith('/api/articles') || path.startsWith('/api/categories') || path.startsWith('/api/tags')) {
        return await articleHandler.fetch(request, env, ctx);
      }

      // 搜索相關 API
      if (path.startsWith('/api/search')) {
        return await searchHandler.fetch(request, env, ctx);
      }

      // 上傳和圖片相關 API
      if (path.startsWith('/api/upload') || path.startsWith('/api/images')) {
        return await uploadHandler.fetch(request, env, ctx);
      }

      // 收藏相關 API
      if (path.startsWith('/api/favorites')) {
        return await favoritesHandler.fetch(request, env, ctx);
      }

      // 404 - API 端點不存在
      return createErrorResponse('API endpoint not found', 404);

    } catch (error) {
      console.error('Main API Error:', error);
      return createErrorResponse('Internal server error', 500);
    }
  },

  // 定時任務處理
  async scheduled(controller, env, ctx) {
    try {
      console.log('Running scheduled cleanup tasks...');
      
      // 創建模擬請求執行清理任務
      const fakeRequest = new Request('https://api.chengyuan-reading.com/api/cleanup', {
        method: 'POST'
      });

      const result = await cleanupTasks(fakeRequest, env);
      console.log('Cleanup completed:', await result.text());

    } catch (error) {
      console.error('Scheduled task failed:', error);
    }
  }
};