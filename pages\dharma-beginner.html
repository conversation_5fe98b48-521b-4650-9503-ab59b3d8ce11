<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>入門佛學 - 佛學智慧</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.6;
            color: #333;
            background: transparent;
            padding: 20px;
        }

        /* 入門介紹區 */
        .beginner-intro {
            background: linear-gradient(135deg, #a8e6cf, #dcedc8);
            color: #2e7d32;
            padding: 40px 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .beginner-intro::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.3) 2px, transparent 2px);
            background-size: 40px 40px;
            animation: gentleFloat 25s linear infinite;
        }

        @keyframes gentleFloat {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .intro-content {
            position: relative;
            z-index: 2;
        }

        .intro-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            animation: gentleBreathe 4s infinite;
        }

        @keyframes gentleBreathe {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .intro-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .intro-description {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.8;
        }

        /* 學習路徑 */
        .learning-path {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .path-step {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            border-left: 4px solid #4caf50;
        }

        .path-step:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .step-number {
            position: absolute;
            top: -15px;
            left: 20px;
            width: 30px;
            height: 30px;
            background: #4caf50;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .step-icon {
            color: #4caf50;
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        .step-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .step-description {
            color: #666;
            line-height: 1.6;
            font-size: 0.9rem;
        }

        /* 入門文章 */
        .beginner-articles {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .articles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .article-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid #e9ecef;
            border-left: 4px solid #4caf50;
        }

        .article-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-color: #a8e6cf;
        }

        .article-icon {
            color: #4caf50;
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .article-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .article-excerpt {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .article-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: #999;
        }

        .difficulty-badge {
            background: #4caf50;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
        }

        /* 基礎概念 */
        .basic-concepts {
            background: linear-gradient(135deg, #f0f8e8, #e8f5e8);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid #c8e6c9;
        }

        .concepts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .concept-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .concept-icon {
            width: 50px;
            height: 50px;
            background: #4caf50;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 1.5rem;
        }

        .concept-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .concept-description {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            .beginner-intro {
                padding: 30px 20px;
            }

            .intro-title {
                font-size: 1.5rem;
            }

            .learning-path,
            .articles-grid,
            .concepts-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 入門介紹 -->
    <div class="beginner-intro">
        <div class="intro-content">
            <div class="intro-icon">
                <i class="fas fa-seedling"></i>
            </div>
            <h1 class="intro-title">入門佛學</h1>
            <p class="intro-description">
                歡迎踏上佛學的學習之路！這裡為初學者精心準備了基礎概念和修行指導，
                幫助您建立正確的佛法認知，開啟智慧之門。
            </p>
        </div>
    </div>

    <!-- 學習路徑 -->
    <div class="learning-path">
        <div class="path-step">
            <div class="step-number">1</div>
            <div class="step-icon">
                <i class="fas fa-book-open"></i>
            </div>
            <h3 class="step-title">了解基礎概念</h3>
            <p class="step-description">
                學習四聖諦、三寶、因果業力等佛教基本概念，建立正確的佛法認知基礎。
            </p>
        </div>

        <div class="path-step">
            <div class="step-number">2</div>
            <div class="step-icon">
                <i class="fas fa-heart"></i>
            </div>
            <h3 class="step-title">培養慈悲心</h3>
            <p class="step-description">
                從日常生活中培養對眾生的慈悲心，這是修行路上最重要的基礎。
            </p>
        </div>

        <div class="path-step">
            <div class="step-number">3</div>
            <div class="step-icon">
                <i class="fas fa-hands-praying"></i>
            </div>
            <h3 class="step-title">基礎持戒</h3>
            <p class="step-description">
                學習並實踐五戒十善，培養良好的道德品格和行為習慣。
            </p>
        </div>

        <div class="path-step">
            <div class="step-number">4</div>
            <div class="step-icon">
                <i class="fas fa-meditation"></i>
            </div>
            <h3 class="step-title">簡單禪修</h3>
            <p class="step-description">
                開始簡單的呼吸觀察和正念練習，培養內心的平靜與專注。
            </p>
        </div>
    </div>

    <!-- 入門文章 -->
    <div class="beginner-articles">
        <h2 class="section-title">
            <i class="fas fa-graduation-cap"></i>
            入門必讀文章
        </h2>
        <div class="articles-grid" id="beginner-articles-list">
            <!-- 文章將通過 JavaScript 動態生成 -->
        </div>
    </div>

    <!-- 基礎概念 -->
    <div class="basic-concepts">
        <h2 class="section-title">
            <i class="fas fa-lightbulb"></i>
            重要基礎概念
        </h2>
        <div class="concepts-grid">
            <div class="concept-card">
                <div class="concept-icon">
                    <i class="fas fa-gem"></i>
                </div>
                <h3 class="concept-title">三寶</h3>
                <p class="concept-description">
                    佛、法、僧三寶是佛教的核心，是修行者的皈依對象。
                </p>
            </div>
            <div class="concept-card">
                <div class="concept-icon">
                    <i class="fas fa-dharmachakra"></i>
                </div>
                <h3 class="concept-title">四聖諦</h3>
                <p class="concept-description">
                    苦、集、滅、道四聖諦是佛陀的根本教義，揭示了解脫之道。
                </p>
            </div>
            <div class="concept-card">
                <div class="concept-icon">
                    <i class="fas fa-recycle"></i>
                </div>
                <h3 class="concept-title">因果業力</h3>
                <p class="concept-description">
                    善有善報，惡有惡報，了解因果關係有助於正確的人生觀。
                </p>
            </div>
            <div class="concept-card">
                <div class="concept-icon">
                    <i class="fas fa-circle-notch"></i>
                </div>
                <h3 class="concept-title">輪迴</h3>
                <p class="concept-description">
                    生死流轉的過程，理解輪迴有助於珍惜人身，精進修行。
                </p>
            </div>
            <div class="concept-card">
                <div class="concept-icon">
                    <i class="fas fa-eye"></i>
                </div>
                <h3 class="concept-title">正念</h3>
                <p class="concept-description">
                    時刻保持清醒覺察，正念是修行的重要基礎。
                </p>
            </div>
            <div class="concept-card">
                <div class="concept-icon">
                    <i class="fas fa-infinity"></i>
                </div>
                <h3 class="concept-title">空性</h3>
                <p class="concept-description">
                    萬法皆空，理解空性是智慧的最高體現。
                </p>
            </div>
        </div>
    </div>

    <script>
        // 入門級文章數據
        const beginnerArticles = [
            {
                id: 5,
                title: "正念覺察在日常生活中的應用",
                excerpt: "正念是佛教修行的基礎，也是現代心理治療的重要方法。文章探討如何將佛教的正念修持融入現代生活，包括正念飲食、正念行走、正念工作等實用技巧。",
                date: "2025-01-01",
                views: 2134,
                likes: 178,
                icon: "fas fa-eye"
            },
            {
                id: 7,
                title: "禪修入門：止觀雙運的修持方法",
                excerpt: "止觀是佛教禪修的核心，止能令心安定，觀能生起智慧。本文詳細介紹了九住心的修持階段，以及如何在日常生活中培養專注力和觀察力。",
                date: "2024-12-25",
                views: 2456,
                likes: 189,
                icon: "fas fa-meditation"
            },
            {
                id: 9,
                title: "五戒十善：佛教倫理的基礎",
                excerpt: "五戒是在家佛弟子的基本行為準則，十善業則是身口意三業的正面修持。本文詳細解釋每一戒的深層含義以及在現代生活中的實踐方法。",
                date: "2024-12-30",
                views: 1876,
                likes: 143,
                icon: "fas fa-hand-peace"
            },
            {
                id: 12,
                title: "四聖諦：佛陀的根本教義",
                excerpt: "四聖諦是佛教的核心教義，包括苦諦、集諦、滅諦、道諦。本文深入淺出地解釋四聖諦的含義，以及如何在修行中體驗和實踐這些真理。",
                date: "2024-12-22",
                views: 2287,
                likes: 201,
                icon: "fas fa-dharmachakra"
            },
            {
                id: 16,
                title: "念佛法門：淨土修行指南",
                excerpt: "念佛法門是專注於阿彌陀佛的修持方法，適合各種根器的修行者。本文詳細介紹了念佛的方法、心態調整，以及如何在日常生活中持續念佛修行。",
                date: "2024-12-18",
                views: 1923,
                likes: 156,
                icon: "fas fa-pray"
            },
            {
                id: 20,
                title: "慈悲喜捨四無量心修持",
                excerpt: "四無量心是大乘佛教的重要修行法門，透過觀修慈悲喜捨，培養廣大的菩提心。文章詳細指導如何逐步修持四無量心的觀想方法。",
                date: "2024-12-10",
                views: 1654,
                likes: 134,
                icon: "fas fa-heart"
            }
        ];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderArticles();
            initializeNavigation();
        });

        // 渲染文章
        function renderArticles() {
            const container = document.getElementById('beginner-articles-list');
            
            container.innerHTML = beginnerArticles.map(article => `
                <div class="article-card" data-article-id="${article.id}">
                    <div class="article-icon">
                        <i class="${article.icon}"></i>
                    </div>
                    <h3 class="article-title">${article.title}</h3>
                    <p class="article-excerpt">${article.excerpt}</p>
                    <div class="article-meta">
                        <span class="difficulty-badge">入門</span>
                        <span>${article.date}</span>
                    </div>
                </div>
            `).join('');
        }

        // 初始化導航
        function initializeNavigation() {
            // 文章點擊
            document.addEventListener('click', function(e) {
                const articleElement = e.target.closest('[data-article-id]');
                if (articleElement) {
                    const articleId = articleElement.getAttribute('data-article-id');
                    
                    if (window.parent !== window) {
                        window.parent.postMessage({
                            type: 'article_view',
                            articleId: articleId
                        }, '*');
                    }
                }
            });

            // 概念卡片點擊
            document.querySelectorAll('.concept-card').forEach(card => {
                card.addEventListener('click', function() {
                    const conceptTitle = this.querySelector('.concept-title').textContent;
                    
                    if (window.parent !== window) {
                        window.parent.postMessage({
                            type: 'search',
                            query: conceptTitle
                        }, '*');
                    }
                });
            });
        }
    </script>
</body>
</html>