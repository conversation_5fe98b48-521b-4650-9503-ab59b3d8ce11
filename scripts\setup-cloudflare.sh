#!/bin/bash

# 澄源閱讀 - Cloudflare 環境設置腳本
# 此腳本用於自動化設置 Cloudflare 服務

set -e

echo "🚀 開始設置澄源閱讀 Cloudflare 環境..."

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 檢查 wrangler 是否已安裝
if ! command -v wrangler &> /dev/null; then
    echo -e "${RED}❌ Wrangler CLI 未安裝，請先運行: npm install -g wrangler${NC}"
    exit 1
fi

# 檢查是否已登錄
if ! wrangler whoami &> /dev/null; then
    echo -e "${YELLOW}🔐 請先登錄 Cloudflare: wrangler login${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Wrangler CLI 檢查通過${NC}"

# 1. 創建 D1 數據庫
echo -e "${YELLOW}📊 創建 D1 數據庫...${NC}"
DB_OUTPUT=$(wrangler d1 create chengyuan-reading-db 2>/dev/null || echo "數據庫可能已存在")
if [[ $DB_OUTPUT == *"Created"* ]]; then
    echo -e "${GREEN}✅ D1 數據庫創建成功${NC}"
    echo "$DB_OUTPUT"
else
    echo -e "${YELLOW}⚠️  數據庫可能已存在，繼續執行...${NC}"
fi

# 2. 創建 R2 存儲桶
echo -e "${YELLOW}🪣 創建 R2 存儲桶...${NC}"
R2_OUTPUT=$(wrangler r2 bucket create chengyuan-images 2>/dev/null || echo "存儲桶可能已存在")
if [[ $R2_OUTPUT == *"Created"* ]]; then
    echo -e "${GREEN}✅ R2 存儲桶創建成功${NC}"
else
    echo -e "${YELLOW}⚠️  存儲桶可能已存在，繼續執行...${NC}"
fi

# 3. 創建 KV 命名空間
echo -e "${YELLOW}🗃️  創建 KV 命名空間...${NC}"
KV_OUTPUT=$(wrangler kv:namespace create "CACHE" 2>/dev/null || echo "KV 命名空間可能已存在")
if [[ $KV_OUTPUT == *"created"* ]]; then
    echo -e "${GREEN}✅ KV 命名空間創建成功${NC}"
    echo "$KV_OUTPUT"
else
    echo -e "${YELLOW}⚠️  KV 命名空間可能已存在，繼續執行...${NC}"
fi

# 4. 執行數據庫遷移
echo -e "${YELLOW}🔄 執行數據庫遷移...${NC}"
if wrangler d1 execute chengyuan-reading-db --file=database/schema.sql; then
    echo -e "${GREEN}✅ 數據庫遷移完成${NC}"
else
    echo -e "${RED}❌ 數據庫遷移失敗${NC}"
fi

# 5. 插入初始數據
echo -e "${YELLOW}📝 插入初始數據...${NC}"
if wrangler d1 execute chengyuan-reading-db --file=database/seed.sql; then
    echo -e "${GREEN}✅ 初始數據插入完成${NC}"
else
    echo -e "${RED}❌ 初始數據插入失敗${NC}"
fi

# 6. 設置 R2 公開訪問
echo -e "${YELLOW}🌐 設置 R2 公開訪問...${NC}"
cat > r2-cors.json << EOF
[
  {
    "allowedOrigins": ["*"],
    "allowedMethods": ["GET", "HEAD"],
    "allowedHeaders": ["*"],
    "exposeHeaders": [],
    "maxAgeSeconds": 3600
  }
]
EOF

if wrangler r2 bucket cors put chengyuan-images --file=r2-cors.json; then
    echo -e "${GREEN}✅ R2 CORS 設置完成${NC}"
    rm r2-cors.json
else
    echo -e "${YELLOW}⚠️  R2 CORS 設置失敗，請手動配置${NC}"
fi

# 7. 部署 Workers
echo -e "${YELLOW}🚀 部署 Workers...${NC}"

# 部署文章 API
if wrangler deploy functions/articles.js --name chengyuan-articles; then
    echo -e "${GREEN}✅ 文章 API 部署成功${NC}"
else
    echo -e "${RED}❌ 文章 API 部署失敗${NC}"
fi

# 部署搜索 API
if wrangler deploy functions/search.js --name chengyuan-search; then
    echo -e "${GREEN}✅ 搜索 API 部署成功${NC}"
else
    echo -e "${RED}❌ 搜索 API 部署失敗${NC}"
fi

# 部署上傳 API
if wrangler deploy functions/upload.js --name chengyuan-upload; then
    echo -e "${GREEN}✅ 上傳 API 部署成功${NC}"
else
    echo -e "${RED}❌ 上傳 API 部署失敗${NC}"
fi

# 8. 部署靜態網站
echo -e "${YELLOW}📦 部署靜態網站到 Cloudflare Pages...${NC}"
if wrangler pages deploy . --project-name=chengyuan-reading; then
    echo -e "${GREEN}✅ 靜態網站部署成功${NC}"
else
    echo -e "${RED}❌ 靜態網站部署失敗${NC}"
fi

echo ""
echo -e "${GREEN}🎉 澄源閱讀 Cloudflare 環境設置完成！${NC}"
echo ""
echo "📋 部署信息："
echo "   - 網站: https://chengyuan-reading.pages.dev"
echo "   - 文章 API: https://chengyuan-articles.your-subdomain.workers.dev"
echo "   - 搜索 API: https://chengyuan-search.your-subdomain.workers.dev"
echo "   - 上傳 API: https://chengyuan-upload.your-subdomain.workers.dev"
echo ""
echo "🔧 後續步驟："
echo "   1. 更新 wrangler.toml 中的數據庫和存儲桶 ID"
echo "   2. 配置自定義域名（可選）"
echo "   3. 設置環境變量"
echo "   4. 測試所有功能"
echo ""
echo "📚 查看部署日誌："
echo "   wrangler tail chengyuan-articles"
echo "   wrangler tail chengyuan-search"
echo "   wrangler tail chengyuan-upload"