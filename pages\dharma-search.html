<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索結果 - 佛學智慧</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.6;
            color: #333;
            background: transparent;
            padding: 20px;
        }

        /* 搜索區域 */
        .search-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .search-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .search-box {
            position: relative;
            max-width: 500px;
            margin: 0 auto;
        }

        .search-input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            outline: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .search-btn {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            background: #667eea;
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .search-btn:hover {
            background: #5a6fd8;
        }

        /* 搜索結果統計 */
        .search-stats {
            margin-bottom: 20px;
            color: #666;
            font-size: 0.9rem;
        }

        .search-query {
            color: #667eea;
            font-weight: 600;
        }

        /* 篩選器 */
        .search-filters {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .filters-row {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-label {
            font-size: 0.8rem;
            color: #666;
            font-weight: 500;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 0.9rem;
            outline: none;
        }

        .filter-select:focus {
            border-color: #667eea;
        }

        /* 搜索結果 */
        .search-results {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .result-item {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border-left: 4px solid #667eea;
        }

        .result-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .result-header {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin-bottom: 15px;
        }

        .result-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            flex-shrink: 0;
        }

        .result-content {
            flex: 1;
        }

        .result-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .result-title:hover {
            color: #667eea;
        }

        .result-meta {
            display: flex;
            gap: 15px;
            font-size: 0.8rem;
            color: #999;
            margin-bottom: 10px;
        }

        .result-excerpt {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .search-highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 500;
        }

        .result-tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .tag {
            background: #f8f9fa;
            color: #666;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            border: 1px solid #e9ecef;
        }

        .tag.difficulty {
            background: #e3f2fd;
            color: #1976d2;
            border-color: #bbdefb;
        }

        .tag.category {
            background: #f3e5f5;
            color: #7b1fa2;
            border-color: #e1bee7;
        }

        /* 無結果 */
        .no-results {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .no-results-icon {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 20px;
        }

        .no-results-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .no-results-text {
            line-height: 1.6;
        }

        /* 載入中 */
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading-spinner {
            display: inline-block;
            width: 30px;
            height: 30px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            .search-header {
                padding: 20px;
            }

            .search-title {
                font-size: 1.4rem;
            }

            .filters-row {
                flex-direction: column;
                align-items: stretch;
            }

            .result-header {
                flex-direction: column;
                align-items: center;
                text-align: center;
            }

            .result-meta {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- 搜索標題 -->
    <div class="search-header">
        <h1 class="search-title">佛學智慧搜索</h1>
        <div class="search-box">
            <input type="text" id="search-input" class="search-input" placeholder="搜索佛學內容...">
            <button id="search-btn" class="search-btn">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>

    <!-- 搜索統計 -->
    <div id="search-stats" class="search-stats" style="display: none;">
        為 "<span class="search-query" id="current-query"></span>" 找到 <span id="result-count">0</span> 個結果
    </div>

    <!-- 篩選器 -->
    <div id="search-filters" class="search-filters" style="display: none;">
        <div class="filters-row">
            <div class="filter-group">
                <label class="filter-label">分類</label>
                <select id="category-filter" class="filter-select">
                    <option value="">所有分類</option>
                    <option value="meditation">禪修指導</option>
                    <option value="philosophy">佛教哲學</option>
                    <option value="practice">修行方法</option>
                    <option value="sutras">經典解讀</option>
                    <option value="masters">大師教導</option>
                    <option value="ethics">佛教倫理</option>
                    <option value="tradition">傳承體系</option>
                    <option value="stories">佛教故事</option>
                </select>
            </div>
            <div class="filter-group">
                <label class="filter-label">難度</label>
                <select id="difficulty-filter" class="filter-select">
                    <option value="">所有難度</option>
                    <option value="beginner">入門</option>
                    <option value="intermediate">進階</option>
                    <option value="advanced">高級</option>
                </select>
            </div>
            <div class="filter-group">
                <label class="filter-label">排序</label>
                <select id="sort-filter" class="filter-select">
                    <option value="relevance">相關性</option>
                    <option value="date">發布日期</option>
                    <option value="views">瀏覽量</option>
                    <option value="likes">點讚數</option>
                </select>
            </div>
        </div>
    </div>

    <!-- 載入中 -->
    <div id="loading" class="loading" style="display: none;">
        <div class="loading-spinner"></div>
        <p>正在搜索中...</p>
    </div>

    <!-- 搜索結果 -->
    <div id="search-results" class="search-results">
        <!-- 結果將通過 JavaScript 動態生成 -->
    </div>

    <!-- 無結果 -->
    <div id="no-results" class="no-results" style="display: none;">
        <div class="no-results-icon">
            <i class="fas fa-search"></i>
        </div>
        <h3 class="no-results-title">未找到相關結果</h3>
        <p class="no-results-text">
            請嘗試使用不同的關鍵詞，或查看我們的文章分類瀏覽內容。
        </p>
    </div>

    <script>
        // 模擬文章數據
        const allArticles = [
            {
                id: 5,
                title: "正念覺察在日常生活中的應用",
                excerpt: "正念是佛教修行的基礎，也是現代心理治療的重要方法。文章探討如何將佛教的正念修持融入現代生活，包括正念飲食、正念行走、正念工作等實用技巧。",
                category: "meditation",
                difficulty: "beginner",
                tags: ["正念", "日常修行", "心理治療"],
                date: "2025-01-01",
                views: 2134,
                likes: 178,
                icon: "fas fa-eye"
            },
            {
                id: 7,
                title: "禪修入門：止觀雙運的修持方法",
                excerpt: "止觀是佛教禪修的核心，止能令心安定，觀能生起智慧。本文詳細介紹了九住心的修持階段，以及如何在日常生活中培養專注力和觀察力。",
                category: "meditation",
                difficulty: "beginner",
                tags: ["禪修", "止觀", "九住心"],
                date: "2024-12-25",
                views: 2456,
                likes: 189,
                icon: "fas fa-meditation"
            },
            {
                id: 12,
                title: "四聖諦：佛陀的根本教義",
                excerpt: "四聖諦是佛教的核心教義，包括苦諦、集諦、滅諦、道諦。本文深入淺出地解釋四聖諦的含義，以及如何在修行中體驗和實踐這些真理。",
                category: "philosophy",
                difficulty: "beginner",
                tags: ["四聖諦", "苦集滅道", "基礎教義"],
                date: "2024-12-22",
                views: 2287,
                likes: 201,
                icon: "fas fa-dharmachakra"
            },
            {
                id: 15,
                title: "《金剛經》的無住生心智慧",
                excerpt: "《金剛經》教導「應無所住而生其心」的甚深智慧，是般若經典的代表作。文章深入解析金剛經的核心思想，指導如何在修行中實踐無住生心。",
                category: "sutras",
                difficulty: "advanced",
                tags: ["金剛經", "般若", "無住生心"],
                date: "2024-12-20",
                views: 2543,
                likes: 234,
                icon: "fas fa-gem"
            },
            {
                id: 18,
                title: "藏傳佛教的傳承體系",
                excerpt: "藏傳佛教分為四大教派：格魯派、噶舉派、寧瑪派和薩迦派。每個教派都有其獨特的修行方法、教學體系和文化特色，形成了豐富多元的傳承體系。",
                category: "tradition",
                difficulty: "intermediate",
                tags: ["藏傳佛教", "四大教派", "傳承"],
                date: "2024-12-12",
                views: 2134,
                likes: 189,
                icon: "fas fa-tree"
            }
        ];

        let currentQuery = '';
        let filteredResults = [];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSearch();
            setupFilters();
            
            // 檢查 URL 參數
            const urlParams = new URLSearchParams(window.location.search);
            const query = urlParams.get('q');
            if (query) {
                document.getElementById('search-input').value = query;
                performSearch(query);
            }
        });

        // 初始化搜索
        function initializeSearch() {
            const searchInput = document.getElementById('search-input');
            const searchBtn = document.getElementById('search-btn');

            searchBtn.addEventListener('click', function() {
                const query = searchInput.value.trim();
                if (query) {
                    performSearch(query);
                }
            });

            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    const query = this.value.trim();
                    if (query) {
                        performSearch(query);
                    }
                }
            });
        }

        // 設置篩選器
        function setupFilters() {
            const filters = ['category-filter', 'difficulty-filter', 'sort-filter'];
            
            filters.forEach(filterId => {
                document.getElementById(filterId).addEventListener('change', function() {
                    if (currentQuery) {
                        applyFilters();
                    }
                });
            });
        }

        // 執行搜索
        function performSearch(query) {
            currentQuery = query;
            
            // 顯示載入中
            showLoading(true);
            
            // 模擬搜索延遲
            setTimeout(() => {
                const results = searchArticles(query);
                displayResults(results);
                showLoading(false);
            }, 800);
        }

        // 搜索文章
        function searchArticles(query) {
            const lowerQuery = query.toLowerCase();
            
            return allArticles.filter(article => {
                return article.title.toLowerCase().includes(lowerQuery) ||
                       article.excerpt.toLowerCase().includes(lowerQuery) ||
                       article.tags.some(tag => tag.toLowerCase().includes(lowerQuery));
            });
        }

        // 應用篩選器
        function applyFilters() {
            const categoryFilter = document.getElementById('category-filter').value;
            const difficultyFilter = document.getElementById('difficulty-filter').value;
            const sortFilter = document.getElementById('sort-filter').value;

            let results = searchArticles(currentQuery);

            // 分類篩選
            if (categoryFilter) {
                results = results.filter(article => article.category === categoryFilter);
            }

            // 難度篩選
            if (difficultyFilter) {
                results = results.filter(article => article.difficulty === difficultyFilter);
            }

            // 排序
            results.sort((a, b) => {
                switch (sortFilter) {
                    case 'date':
                        return new Date(b.date) - new Date(a.date);
                    case 'views':
                        return b.views - a.views;
                    case 'likes':
                        return b.likes - a.likes;
                    default: // relevance
                        return 0;
                }
            });

            displayResults(results);
        }

        // 顯示結果
        function displayResults(results) {
            filteredResults = results;
            
            // 更新統計
            document.getElementById('current-query').textContent = currentQuery;
            document.getElementById('result-count').textContent = results.length;
            document.getElementById('search-stats').style.display = 'block';
            document.getElementById('search-filters').style.display = 'block';

            const resultsContainer = document.getElementById('search-results');
            const noResults = document.getElementById('no-results');

            if (results.length === 0) {
                resultsContainer.innerHTML = '';
                noResults.style.display = 'block';
                return;
            }

            noResults.style.display = 'none';
            
            resultsContainer.innerHTML = results.map(article => `
                <div class="result-item" data-article-id="${article.id}">
                    <div class="result-header">
                        <div class="result-icon">
                            <i class="${article.icon}"></i>
                        </div>
                        <div class="result-content">
                            <h3 class="result-title">${highlightText(article.title, currentQuery)}</h3>
                            <div class="result-meta">
                                <span><i class="fas fa-calendar"></i> ${article.date}</span>
                                <span><i class="fas fa-eye"></i> ${article.views}</span>
                                <span><i class="fas fa-heart"></i> ${article.likes}</span>
                            </div>
                        </div>
                    </div>
                    <p class="result-excerpt">${highlightText(article.excerpt, currentQuery)}</p>
                    <div class="result-tags">
                        <span class="tag difficulty">${getDifficultyText(article.difficulty)}</span>
                        <span class="tag category">${getCategoryText(article.category)}</span>
                        ${article.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                    </div>
                </div>
            `).join('');

            // 添加點擊事件
            resultsContainer.querySelectorAll('.result-item').forEach(item => {
                item.addEventListener('click', function() {
                    const articleId = this.getAttribute('data-article-id');
                    if (window.parent !== window) {
                        window.parent.postMessage({
                            type: 'article_view',
                            articleId: articleId
                        }, '*');
                    }
                });
            });
        }

        // 高亮文本
        function highlightText(text, query) {
            if (!query) return text;
            
            const regex = new RegExp(`(${query})`, 'gi');
            return text.replace(regex, '<span class="search-highlight">$1</span>');
        }

        // 獲取難度文本
        function getDifficultyText(difficulty) {
            const difficultyMap = {
                'beginner': '入門',
                'intermediate': '進階',
                'advanced': '高級'
            };
            return difficultyMap[difficulty] || difficulty;
        }

        // 獲取分類文本
        function getCategoryText(category) {
            const categoryMap = {
                'meditation': '禪修指導',
                'philosophy': '佛教哲學',
                'practice': '修行方法',
                'sutras': '經典解讀',
                'masters': '大師教導',
                'ethics': '佛教倫理',
                'tradition': '傳承體系',
                'stories': '佛教故事'
            };
            return categoryMap[category] || category;
        }

        // 顯示載入狀態
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
            document.getElementById('search-results').style.display = show ? 'none' : 'block';
        }

        // 監聽來自父框架的消息
        window.addEventListener('message', function(event) {
            if (event.data.type === 'search') {
                document.getElementById('search-input').value = event.data.query;
                performSearch(event.data.query);
            }
        });
    </script>
</body>
</html>