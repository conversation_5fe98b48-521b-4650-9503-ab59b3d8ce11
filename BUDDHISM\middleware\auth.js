const jwt = require('jsonwebtoken');
const User = require('../models/User');

/**
 * JWT 認證中間件
 * 驗證用戶的 access token 並將用戶資訊添加到 req.user
 */
const auth = async (req, res, next) => {
    try {
        // 從 header 中獲取 token
        const authHeader = req.header('Authorization');
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                error: 'Access Denied',
                message: '請提供有效的認證令牌'
            });
        }

        const token = authHeader.substring(7); // 移除 'Bearer ' 前綴

        // 驗證 token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // 查找用戶
        const user = await User.findById(decoded.id).select('-password');
        
        if (!user) {
            return res.status(401).json({
                error: 'Invalid Token',
                message: '令牌對應的用戶不存在'
            });
        }

        // 檢查用戶狀態
        if (user.status === 'suspended') {
            return res.status(403).json({
                error: 'Account Suspended',
                message: '您的帳戶已被暫停'
            });
        }

        if (user.status === 'inactive') {
            return res.status(403).json({
                error: 'Account Inactive',
                message: '您的帳戶已停用'
            });
        }

        // 將用戶資訊添加到請求對象
        req.user = user;
        req.token = token;
        
        next();
    } catch (error) {
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({
                error: 'Invalid Token',
                message: '無效的認證令牌'
            });
        }
        
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({
                error: 'Token Expired',
                message: '認證令牌已過期，請重新登入'
            });
        }

        console.error('Auth middleware error:', error);
        res.status(500).json({
            error: 'Authentication Failed',
            message: '認證過程中發生錯誤'
        });
    }
};

/**
 * 可選認證中間件
 * 如果提供了 token 則驗證，如果沒有則繼續但不設置 req.user
 */
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.header('Authorization');
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return next(); // 沒有 token，繼續處理
        }

        const token = authHeader.substring(7);
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findById(decoded.id).select('-password');
        
        if (user && user.status === 'active') {
            req.user = user;
            req.token = token;
        }
        
        next();
    } catch (error) {
        // 可選認證失敗時不返回錯誤，只是不設置用戶
        next();
    }
};

/**
 * 角色授權中間件
 * 檢查用戶是否具有指定的角色
 */
const authorize = (...roles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                error: 'Authentication Required',
                message: '請先登入'
            });
        }

        if (!roles.includes(req.user.role)) {
            return res.status(403).json({
                error: 'Insufficient Permissions',
                message: '您沒有執行此操作的權限'
            });
        }

        next();
    };
};

/**
 * 會員等級授權中間件
 * 檢查用戶是否具有指定的會員等級或更高
 */
const requireMembership = (...plans) => {
    const planHierarchy = {
        'basic': 1,
        'premium': 2,
        'vip': 3
    };

    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                error: 'Authentication Required',
                message: '請先登入'
            });
        }

        // 檢查會員狀態
        if (!req.user.membershipActive) {
            return res.status(403).json({
                error: 'Membership Required',
                message: '需要有效的會員資格'
            });
        }

        const userPlanLevel = planHierarchy[req.user.membership.plan] || 0;
        const requiredLevel = Math.min(...plans.map(plan => planHierarchy[plan] || 999));

        if (userPlanLevel < requiredLevel) {
            return res.status(403).json({
                error: 'Membership Upgrade Required',
                message: `此功能需要 ${plans.join(' 或 ')} 會員資格`,
                currentPlan: req.user.membership.plan,
                requiredPlans: plans
            });
        }

        next();
    };
};

/**
 * 郵件驗證檢查中間件
 * 檢查用戶是否已驗證郵件
 */
const requireEmailVerified = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({
            error: 'Authentication Required',
            message: '請先登入'
        });
    }

    if (!req.user.security.emailVerified) {
        return res.status(403).json({
            error: 'Email Verification Required',
            message: '請先驗證您的郵件地址',
            action: 'resend_verification'
        });
    }

    next();
};

/**
 * 用戶本人驗證中間件
 * 檢查是否為用戶本人或管理員
 */
const requireSelfOrAdmin = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({
            error: 'Authentication Required',
            message: '請先登入'
        });
    }

    const targetUserId = req.params.userId || req.params.id;
    const isOwner = req.user._id.toString() === targetUserId;
    const isAdmin = ['admin', 'moderator'].includes(req.user.role);

    if (!isOwner && !isAdmin) {
        return res.status(403).json({
            error: 'Access Denied',
            message: '您只能訪問自己的資料'
        });
    }

    next();
};

/**
 * API Key 認證中間件
 * 用於第三方服務或內部系統調用
 */
const apiKeyAuth = (req, res, next) => {
    const apiKey = req.header('X-API-Key');
    
    if (!apiKey || apiKey !== process.env.API_KEY) {
        return res.status(401).json({
            error: 'Invalid API Key',
            message: '無效的 API 密鑰'
        });
    }

    // 為 API 調用設置特殊標識
    req.isApiCall = true;
    next();
};

/**
 * 開發環境授權中間件
 * 僅在開發環境中允許訪問
 */
const devOnly = (req, res, next) => {
    if (process.env.NODE_ENV === 'production') {
        return res.status(404).json({
            error: 'Not Found',
            message: '此端點僅在開發環境中可用'
        });
    }
    next();
};

/**
 * 管理面板認證中間件
 * 結合多種檢查的複合中間件
 */
const adminAuth = [
    auth,
    requireEmailVerified,
    authorize('admin', 'moderator')
];

/**
 * VIP 功能認證中間件
 * VIP 會員專用功能
 */
const vipAuth = [
    auth,
    requireEmailVerified,
    requireMembership('vip')
];

/**
 * 進階功能認證中間件
 * 進階會員及以上
 */
const premiumAuth = [
    auth,
    requireEmailVerified,
    requireMembership('premium', 'vip')
];

module.exports = {
    auth,
    optionalAuth,
    authorize,
    requireMembership,
    requireEmailVerified,
    requireSelfOrAdmin,
    apiKeyAuth,
    devOnly,
    adminAuth,
    vipAuth,
    premiumAuth
};