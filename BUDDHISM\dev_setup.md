# 佛教身心療癒網站 - 開發環境設置指南

## 🏗️ 項目初始化

### 1. 創建項目結構
```bash
# 創建項目目錄
mkdir buddhist-healing-backend
cd buddhist-healing-backend

# 初始化 npm 項目
npm init -y

# 創建標準項目結構
mkdir -p src/{controllers,models,routes,middleware,services,utils,config}
mkdir -p tests/{unit,integration,fixtures}
mkdir -p docs/{api,deployment}
mkdir -p scripts/{deploy,backup,seed}
mkdir -p logs
mkdir -p public/{uploads,static}

# 創建基本文件
touch .env.example .env .gitignore README.md
touch src/app.js src/server.js
touch docker-compose.yml Dockerfile
touch jest.config.js .eslintrc.js
```

### 2. 項目結構說明
```
buddhist-healing-backend/
├── src/                          # 主要源代碼
│   ├── app.js                   # Express 應用配置
│   ├── server.js                # 服務器啟動文件
│   ├── config/                  # 配置文件
│   │   ├── database.js          # 資料庫配置
│   │   ├── redis.js             # Redis 配置
│   │   ├── elasticsearch.js     # ES 配置
│   │   └── auth.js              # 認證配置
│   ├── controllers/             # 控制器
│   │   ├── authController.js    # 認證控制器
│   │   ├── userController.js    # 用戶控制器
│   │   ├── courseController.js  # 課程控制器
│   │   └── scriptureController.js # 經文控制器
│   ├── models/                  # 資料模型
│   │   ├── User.js              # 用戶模型
│   │   ├── Course.js            # 課程模型
│   │   ├── Scripture.js         # 經文模型
│   │   └── Analytics.js         # 分析模型
│   ├── routes/                  # 路由定義
│   │   ├── index.js             # 主路由
│   │   ├── auth.js              # 認證路由
│   │   ├── users.js             # 用戶路由
│   │   ├── courses.js           # 課程路由
│   │   └── scriptures.js        # 經文路由
│   ├── middleware/              # 中間件
│   │   ├── auth.js              # 認證中間件
│   │   ├── validation.js        # 驗證中間件
│   │   ├── rateLimit.js         # 速率限制
│   │   └── errorHandler.js      # 錯誤處理
│   ├── services/                # 業務邏輯服務
│   │   ├── authService.js       # 認證服務
│   │   ├── emailService.js      # 郵件服務
│   │   ├── paymentService.js    # 支付服務
│   │   └── analyticsService.js  # 分析服務
│   └── utils/                   # 工具函數
│       ├── logger.js            # 日誌工具
│       ├── helpers.js           # 幫助函數
│       └── constants.js         # 常數定義
├── tests/                       # 測試文件
│   ├── unit/                    # 單元測試
│   ├── integration/             # 整合測試
│   └── fixtures/                # 測試數據
├── docs/                        # 文檔
├── scripts/                     # 腳本
├── docker-compose.yml           # Docker Compose
├── Dockerfile                   # Docker 配置
├── package.json                 # 依賴管理
└── README.md                    # 項目說明
```

## 📦 依賴安裝

### 3. 核心依賴
```bash
# 核心框架和資料庫
npm install express mongoose redis ioredis

# 身份驗證和安全
npm install jsonwebtoken bcryptjs passport passport-jwt
npm install helmet cors express-rate-limit express-validator

# 工具和中間件
npm install dotenv morgan compression cookie-parser
npm install multer uuid moment-timezone

# 搜尋和分析
npm install @elastic/elasticsearch

# 通訊和通知
npm install socket.io nodemailer

# 支付整合
npm install stripe

# 監控和指標
npm install prom-client

# 工作排程
npm install node-cron bull

# 開發依賴
npm install -D nodemon jest supertest eslint prettier
npm install -D mongodb-memory-server @types/jest
```

### 4. package.json 腳本配置
```json
{
  "scripts": {
    "start": "node src/server.js",
    "dev": "nodemon src/server.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:integration": "jest --testPathPattern=integration",
    "lint": "eslint src/ --ext .js",
    "lint:fix": "eslint src/ --ext .js --fix",
    "format": "prettier --write src/**/*.js",
    "build": "echo 'No build step required for Node.js'",
    "seed": "node scripts/seed.js",
    "migrate": "node scripts/migrate.js",
    "backup": "bash scripts/backup.sh",
    "docker:build": "docker build -t buddhist-healing-api .",
    "docker:up": "docker-compose up -d",
    "docker:down": "docker-compose down",
    "docker:logs": "docker-compose logs -f"
  }
}
```

## 🔐 環境變數配置

### 5. .env.example
```bash
# 應用配置
NODE_ENV=development
PORT=3000
APP_NAME=Buddhist Healing API
APP_VERSION=1.0.0

# 資料庫配置
MONGODB_URI=mongodb://localhost:27017/buddhist_healing
MONGODB_TEST_URI=mongodb://localhost:27017/buddhist_healing_test

# Redis 配置
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 加密配置
BCRYPT_ROUNDS=12
ENCRYPTION_KEY=your-32-character-encryption-key

# 郵件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>

# 支付配置
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# 雲端儲存配置
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=ap-northeast-1
AWS_S3_BUCKET=buddhist-healing-media

# 搜尋引擎配置
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=

# Google Services
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_ANALYTICS_ID=GA-MEASUREMENT-ID

# 監控配置
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=info

# 開發工具
DEBUG=app:*
```

### 6. .gitignore
```gitignore
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.production
.env.test

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Dependency directories
.npm
.eslintcache

# Optional npm cache directory
.npm

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.docker/

# Uploads
public/uploads/*
!public/uploads/.gitkeep

# SSL certificates
ssl/
certificates/

# Backups
backups/
*.sql
*.dump

# Testing
test-results/
.coverage/
```

## 🔧 開發工具配置

### 7. ESLint 配置 (.eslintrc.js)
```javascript
module.exports = {
  env: {
    browser: false,
    commonjs: true,
    es2021: true,
    node: true,
    jest: true
  },
  extends: [
    'eslint:recommended'
  ],
  parserOptions: {
    ecmaVersion: 12,
    sourceType: 'module'
  },
  rules: {
    // 錯誤級別
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
    'no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    
    // 代碼風格
    'indent': ['error', 2],
    'quotes': ['error', 'single'],
    'semi': ['error', 'always'],
    'comma-dangle': ['error', 'never'],
    'no-trailing-spaces': 'error',
    'eol-last': 'error',
    
    // 最佳實踐
    'prefer-const': 'error',
    'no-var': 'error',
    'object-shorthand': 'error',
    'prefer-template': 'error',
    'no-duplicate-imports': 'error',
    
    // Node.js 特定
    'no-process-exit': 'error',
    'handle-callback-err': 'error'
  },
  globals: {
    'process': 'readonly',
    '__dirname': 'readonly',
    '__filename': 'readonly',
    'Buffer': 'readonly'
  }
};
```

### 8. Prettier 配置 (.prettierrc)
```json
{
  "semi": true,
  "trailingComma": "none",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "arrowParens": "avoid",
  "endOfLine": "lf"
}
```

### 9. Jest 配置 (jest.config.js)
```javascript
module.exports = {
  testEnvironment: 'node',
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/server.js',
    '!src/config/**',
    '!**/node_modules/**',
    '!**/tests/**'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  testMatch: [
    '**/tests/**/*.test.js',
    '**/tests/**/*.spec.js'
  ],
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  testTimeout: 30000,
  verbose: true,
  forceExit: true,
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true
};
```

## 🚀 快速啟動

### 10. 啟動開發環境
```bash
# 1. 複製環境變數
cp .env.example .env

# 2. 編輯 .env 文件，填入實際配置

# 3. 啟動 MongoDB (使用 Docker)
docker run -d --name mongodb -p 27017:27017 mongo:6.0

# 4. 啟動 Redis (使用 Docker)
docker run -d --name redis -p 6379:6379 redis:7.0-alpine

# 5. 啟動 Elasticsearch (使用 Docker)
docker run -d --name elasticsearch \
  -p 9200:9200 \
  -e "discovery.type=single-node" \
  -e "xpack.security.enabled=false" \
  elasticsearch:8.9.0

# 6. 安裝依賴
npm install

# 7. 初始化資料庫
npm run seed

# 8. 啟動開發服務器
npm run dev
```

### 11. 開發工作流程
```bash
# 日常開發命令
npm run dev          # 啟動開發服務器
npm run test:watch   # 監視模式運行測試
npm run lint:fix     # 自動修復 ESLint 錯誤

# 代碼質量檢查
npm run test         # 運行所有測試
npm run test:coverage # 生成測試覆蓋率報告
npm run lint         # 檢查代碼風格

# Docker 開發
npm run docker:up    # 啟動所有服務
npm run docker:logs  # 查看日誌
npm run docker:down  # 停止所有服務
```

## 📊 監控設置

### 12. 健康檢查端點
服務啟動後可以訪問：
- `http://localhost:3000/health` - 健康檢查
- `http://localhost:3000/metrics` - Prometheus 指標
- `http://localhost:3000/api/v1/status` - API 狀態

### 13. 日誌配置
```bash
# 查看實時日誌
tail -f logs/app.log

# 查看錯誤日誌
tail -f logs/error.log

# 查看訪問日誌
tail -f logs/access.log
```

## 🔍 故障排除

### 14. 常見問題解決
```bash
# MongoDB 連接問題
# 檢查 MongoDB 是否運行
docker ps | grep mongo

# Redis 連接問題
# 測試 Redis 連接
redis-cli ping

# 端口被佔用
# 查找佔用進程
lsof -i :3000
# 殺死進程
kill -9 <PID>

# 依賴問題
# 清理並重新安裝
rm -rf node_modules package-lock.json
npm install

# 權限問題
# 修復文件權限
chmod +x scripts/*.sh
```

## ✅ 環境驗證

### 15. 驗證安裝
```bash
# 檢查 Node.js 版本
node --version  # 應該 >= 16.0.0

# 檢查 npm 版本
npm --version   # 應該 >= 8.0.0

# 檢查服務連接
curl http://localhost:3000/health

# 檢查資料庫連接
npm run test -- --testNamePattern="database"
```

環境設置完成後，您將擁有一個完全配置好的現代化 Node.js 開發環境，包含所有必要的工具和服務！