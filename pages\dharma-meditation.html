<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>禪修指導 - 佛學智慧</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.6;
            color: #333;
            background: transparent;
            padding: 20px;
        }

        /* 分類介紹區 */
        .category-intro {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 40px 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .category-intro::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 30px 30px;
            animation: float 20s linear infinite;
        }

        @keyframes float {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .intro-content {
            position: relative;
            z-index: 2;
        }

        .intro-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            animation: breathe 3s infinite;
        }

        @keyframes breathe {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .intro-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .intro-description {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.8;
        }

        /* 修行層次 */
        .practice-levels {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .level-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border-left: 4px solid transparent;
        }

        .level-card.beginner {
            border-left-color: #28a745;
        }

        .level-card.intermediate {
            border-left-color: #ffc107;
        }

        .level-card.advanced {
            border-left-color: #dc3545;
        }

        .level-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .level-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .level-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .level-card.beginner .level-icon {
            background: #28a745;
        }

        .level-card.intermediate .level-icon {
            background: #ffc107;
        }

        .level-card.advanced .level-icon {
            background: #dc3545;
        }

        .level-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
        }

        .level-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .level-count {
            font-size: 0.9rem;
            color: #999;
        }

        /* 精選文章 */
        .featured-articles {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .articles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .article-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid #e9ecef;
        }

        .article-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-color: #667eea;
        }

        .article-icon {
            color: #667eea;
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .article-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .article-excerpt {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .article-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: #999;
        }

        /* 修行指南 */
        .practice-guide {
            background: linear-gradient(135deg, #f0f8ff, #e6f3ff);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid #b3d9ff;
        }

        .guide-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .step-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin: 0 auto 15px;
        }

        .step-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .step-description {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            .category-intro {
                padding: 30px 20px;
            }

            .intro-title {
                font-size: 1.5rem;
            }

            .practice-levels,
            .articles-grid,
            .guide-steps {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 分類介紹 -->
    <div class="category-intro">
        <div class="intro-content">
            <div class="intro-icon">
                <i class="fas fa-meditation"></i>
            </div>
            <h1 class="intro-title">禪修指導</h1>
            <p class="intro-description">
                禪修是通往內在平靜與智慧的道路。在這裡，您將學習到從基礎的專注練習到深層的觀察修持，
                系統性地培養心靈的寧靜與覺察力。
            </p>
        </div>
    </div>

    <!-- 修行層次 -->
    <div class="practice-levels">
        <div class="level-card beginner" data-level="beginner">
            <div class="level-header">
                <div class="level-icon">
                    <i class="fas fa-seedling"></i>
                </div>
                <h3 class="level-title">入門修持</h3>
            </div>
            <p class="level-description">
                適合初學者的基礎禪修方法，包括呼吸觀察、身體覺察等基本技巧，建立穩定的修行基礎。
            </p>
            <div class="level-count">2 篇文章</div>
        </div>

        <div class="level-card intermediate" data-level="intermediate">
            <div class="level-header">
                <div class="level-icon">
                    <i class="fas fa-tree"></i>
                </div>
                <h3 class="level-title">深化修持</h3>
            </div>
            <p class="level-description">
                進階的禪修技巧，包括止觀雙運、內觀修持等，深化專注力和觀察力的培養。
            </p>
            <div class="level-count">3 篇文章</div>
        </div>

        <div class="level-card advanced" data-level="advanced">
            <div class="level-header">
                <div class="level-icon">
                    <i class="fas fa-mountain"></i>
                </div>
                <h3 class="level-title">高深修持</h3>
            </div>
            <p class="level-description">
                高級禪修指導，包括無分別智的修持、空性觀修等深層的修行方法。
            </p>
            <div class="level-count">1 篇文章</div>
        </div>
    </div>

    <!-- 精選文章 -->
    <div class="featured-articles">
        <h2 class="section-title">
            <i class="fas fa-star"></i>
            精選禪修文章
        </h2>
        <div class="articles-grid" id="meditation-articles">
            <!-- 文章將通過 JavaScript 動態生成 -->
        </div>
    </div>

    <!-- 修行指南 -->
    <div class="practice-guide">
        <h2 class="section-title">
            <i class="fas fa-compass"></i>
            禪修入門指南
        </h2>
        <div class="guide-steps">
            <div class="step-card">
                <div class="step-number">1</div>
                <h3 class="step-title">建立正確姿勢</h3>
                <p class="step-description">
                    選擇舒適的坐姿，保持脊椎挺直，雙手自然放置，眼睛輕閉或半開。
                </p>
            </div>
            <div class="step-card">
                <div class="step-number">2</div>
                <h3 class="step-title">調節呼吸</h3>
                <p class="step-description">
                    觀察自然呼吸，不刻意改變，只是單純地覺察呼吸的進出。
                </p>
            </div>
            <div class="step-card">
                <div class="step-number">3</div>
                <h3 class="step-title">培養專注</h3>
                <p class="step-description">
                    當心念散亂時，溫和地將注意力帶回呼吸，不要批判或強迫。
                </p>
            </div>
            <div class="step-card">
                <div class="step-number">4</div>
                <h3 class="step-title">保持覺察</h3>
                <p class="step-description">
                    在日常生活中延續禪修的覺察狀態，將正念融入每個當下。
                </p>
            </div>
        </div>
    </div>

    <script>
        // 禪修相關文章數據
        const meditationArticles = [
            {
                id: 5,
                title: "正念覺察在日常生活中的應用",
                excerpt: "正念是佛教修行的基礎，也是現代心理治療的重要方法。文章探討如何將佛教的正念修持融入現代生活，包括正念飲食、正念行走、正念工作等實用技巧。",
                difficulty: "beginner",
                date: "2025-01-01",
                views: 2134,
                likes: 178,
                icon: "fas fa-eye"
            },
            {
                id: 7,
                title: "禪修入門：止觀雙運的修持方法",
                excerpt: "止觀是佛教禪修的核心，止能令心安定，觀能生起智慧。本文詳細介紹了九住心的修持階段，以及如何在日常生活中培養專注力和觀察力。",
                difficulty: "beginner",
                date: "2024-12-25",
                views: 2456,
                likes: 189,
                icon: "fas fa-meditation"
            },
            {
                id: 21,
                title: "內觀禪修的深層體驗",
                excerpt: "內觀禪修是直接觀察身心現象的修行方法。本文指導如何深入觀察感受、心念的生滅，培養對無常、苦、無我的直接體悟。",
                difficulty: "intermediate",
                date: "2024-12-20",
                views: 1876,
                likes: 143,
                icon: "fas fa-search"
            }
        ];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderArticles();
            initializeNavigation();
        });

        // 渲染文章
        function renderArticles() {
            const container = document.getElementById('meditation-articles');
            
            container.innerHTML = meditationArticles.map(article => `
                <div class="article-card" data-article-id="${article.id}">
                    <div class="article-icon">
                        <i class="${article.icon}"></i>
                    </div>
                    <h3 class="article-title">${article.title}</h3>
                    <p class="article-excerpt">${article.excerpt}</p>
                    <div class="article-meta">
                        <span>${article.difficulty === 'beginner' ? '入門' : article.difficulty === 'intermediate' ? '進階' : '高級'}</span>
                        <span>${article.date}</span>
                    </div>
                </div>
            `).join('');
        }

        // 初始化導航
        function initializeNavigation() {
            // 難度級別點擊
            document.querySelectorAll('.level-card').forEach(card => {
                card.addEventListener('click', function() {
                    const level = this.getAttribute('data-level');
                    
                    if (window.parent !== window) {
                        window.parent.postMessage({
                            type: 'navigate',
                            page: `dharma-${level}`,
                            options: { category: 'meditation', difficulty: level }
                        }, '*');
                    }
                });
            });

            // 文章點擊
            document.addEventListener('click', function(e) {
                const articleElement = e.target.closest('[data-article-id]');
                if (articleElement) {
                    const articleId = articleElement.getAttribute('data-article-id');
                    
                    if (window.parent !== window) {
                        window.parent.postMessage({
                            type: 'article_view',
                            articleId: articleId
                        }, '*');
                    }
                }
            });
        }
    </script>
</body>
</html>