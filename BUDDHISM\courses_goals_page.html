<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>課程目標 - 佛教身心療癒網站</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2C3E50;
            --secondary-color: #F39C12;
            --accent-color: #E8F4F8;
            --text-color: #2C3E50;
            --light-bg: #F8F9FA;
            --success-color: #27AE60;
            --info-color: #3498DB;
        }

        body {
            font-family: 'Noto Sans CJK TC', 'Microsoft JhengHei', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            padding: 120px 0 80px;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            opacity: 0.3;
        }

        .navbar-custom.scrolled {
            background: var(--primary-color);
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
        }

        .nav-link {
            color: white !important;
            font-weight: 500;
            margin: 0 10px;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--secondary-color) !important;
        }

        .section-title {
            position: relative;
            text-align: center;
            margin-bottom: 50px;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: var(--secondary-color);
        }

        .goal-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-top: 4px solid var(--secondary-color);
            position: relative;
            overflow: hidden;
        }

        .goal-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(243, 156, 18, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .goal-card:hover::before {
            left: 100%;
        }

        .goal-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .goal-icon {
            font-size: 3.5rem;
            color: var(--secondary-color);
            margin-bottom: 25px;
        }

        .goal-number {
            position: absolute;
            top: 20px;
            right: 20px;
            background: var(--secondary-color);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .learning-path {
            background: var(--light-bg);
            padding: 80px 0;
        }

        .path-step {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            position: relative;
        }

        .path-step:hover {
            transform: translateY(-5px);
        }

        .step-number {
            position: absolute;
            top: -15px;
            left: 30px;
            background: var(--info-color);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .step-arrow {
            text-align: center;
            color: var(--secondary-color);
            font-size: 2rem;
            margin: 20px 0;
        }

        .skills-grid {
            padding: 80px 0;
        }

        .skill-item {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .skill-item:hover {
            border-left-color: var(--secondary-color);
            transform: translateX(10px);
        }

        .skill-icon {
            font-size: 2rem;
            margin-bottom: 15px;
        }

        .skill-beginner { border-left-color: var(--success-color); }
        .skill-intermediate { border-left-color: var(--secondary-color); }
        .skill-advanced { border-left-color: var(--primary-color); }

        .expected-outcomes {
            background: var(--primary-color);
            color: white;
            padding: 80px 0;
        }

        .outcome-card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }

        .outcome-card:hover {
            transform: translateY(-5px);
            background: rgba(255,255,255,0.15);
        }

        .outcome-icon {
            font-size: 2.5rem;
            color: var(--secondary-color);
            margin-bottom: 20px;
        }

        .progress-bar-custom {
            height: 8px;
            border-radius: 10px;
            background: rgba(255,255,255,0.2);
            margin-top: 15px;
        }

        .progress-fill {
            height: 100%;
            border-radius: 10px;
            background: var(--secondary-color);
            transition: width 2s ease;
        }

        .cta-section {
            background: var(--light-bg);
            padding: 80px 0;
            text-align: center;
        }

        .btn-primary-custom {
            background: var(--secondary-color);
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            color: white;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary-custom:hover {
            background: #e67e22;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(243, 156, 18, 0.4);
            color: white;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            border-radius: 10px;
        }

        .dropdown-item:hover {
            background: var(--accent-color);
            color: var(--primary-color);
        }

        @media (max-width: 768px) {
            .page-header {
                padding: 100px 0 60px;
            }
            
            .goal-card {
                padding: 25px;
            }
            
            .goal-icon {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html" style="color: var(--secondary-color);">
                <i class="fas fa-lotus me-2"></i>佛教身心療癒
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">主頁</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">關於我們</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown">
                            課程
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="courses-goals.html">課程目標</a></li>
                            <li><a class="dropdown-item" href="courses-weekly.html">每週主題</a></li>
                            <li><a class="dropdown-item" href="courses-methods.html">進行方式</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            經文選讀
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="scriptures-search.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="scriptures-methods.html">療癒方法</a></li>
                            <li><a class="dropdown-item" href="scriptures-theory.html">療癒理論</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            身心療癒研究
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="research-search.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="research-theory.html">療癒理論</a></li>
                            <li><a class="dropdown-item" href="research-methods.html">療癒方法</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            藏傳佛教
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="tibetan-theory.html">藏醫理論</a></li>
                            <li><a class="dropdown-item" href="tibetan-practice.html">藏醫實習</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            海外實習
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="overseas-members.html">成員</a></li>
                            <li><a class="dropdown-item" href="overseas-research.html">研究成果</a></li>
                            <li><a class="dropdown-item" href="overseas-future.html">未來計畫</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="workshop.html">工作坊</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="membership.html">加入會員</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 頁面標題 -->
    <section class="page-header">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-4">課程目標</h1>
                    <p class="lead">系統性學習佛教身心療癒，達成全方位的身心和諧</p>
                </div>
            </div>
        </div>
    </section>

    <main>
        <!-- 核心學習目標 -->
        <section class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 class="section-title h1 fw-bold">核心學習目標</h2>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-6 col-md-6">
                        <div class="goal-card">
                            <div class="goal-number">1</div>
                            <div class="goal-icon text-center">
                                <i class="fas fa-brain"></i>
                            </div>
                            <h4 class="fw-bold text-center mb-3">建立正念覺察</h4>
                            <p class="text-center">培養持續的正念覺察能力，學會觀察自己的思維、情緒和身體感受，建立內在觀照的習慣。</p>
                            <ul class="list-unstyled mt-4">
                                <li><i class="fas fa-check text-success me-2"></i>學習基礎正念技巧</li>
                                <li><i class="fas fa-check text-success me-2"></i>建立日常觀察習慣</li>
                                <li><i class="fas fa-check text-success me-2"></i>提升專注力與覺察力</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6">
                        <div class="goal-card">
                            <div class="goal-number">2</div>
                            <div class="goal-icon text-center">
                                <i class="fas fa-heart"></i>
                            </div>
                            <h4 class="fw-bold text-center mb-3">情緒調節能力</h4>
                            <p class="text-center">掌握情緒調節的佛教方法，學會以慈悲與智慧面對各種情緒起伏，達到內心平靜。</p>
                            <ul class="list-unstyled mt-4">
                                <li><i class="fas fa-check text-success me-2"></i>理解情緒的本質</li>
                                <li><i class="fas fa-check text-success me-2"></i>學習情緒轉化技巧</li>
                                <li><i class="fas fa-check text-success me-2"></i>培養慈悲與耐心</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6">
                        <div class="goal-card">
                            <div class="goal-number">3</div>
                            <div class="goal-icon text-center">
                                <i class="fas fa-balance-scale"></i>
                            </div>
                            <h4 class="fw-bold text-center mb-3">身心平衡整合</h4>
                            <p class="text-center">整合身體與心靈的療癒方法，建立健康的生活模式，達到身心靈的完整和諧。</p>
                            <ul class="list-unstyled mt-4">
                                <li><i class="fas fa-check text-success me-2"></i>學習身體覺察技巧</li>
                                <li><i class="fas fa-check text-success me-2"></i>掌握呼吸調節方法</li>
                                <li><i class="fas fa-check text-success me-2"></i>建立健康作息習慣</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6">
                        <div class="goal-card">
                            <div class="goal-number">4</div>
                            <div class="goal-icon text-center">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <h4 class="fw-bold text-center mb-3">智慧洞察力</h4>
                            <p class="text-center">深化對佛教教義的理解，培養智慧洞察力，能夠以佛法觀點分析和解決生活問題。</p>
                            <ul class="list-unstyled mt-4">
                                <li><i class="fas fa-check text-success me-2"></i>理解四聖諦與八正道</li>
                                <li><i class="fas fa-check text-success me-2"></i>掌握緣起性空概念</li>
                                <li><i class="fas fa-check text-success me-2"></i>應用佛法解決問題</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 學習路徑 -->
        <section class="learning-path">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 class="section-title h1 fw-bold">學習路徑規劃</h2>
                    </div>
                </div>
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="path-step">
                            <div class="step-number">1</div>
                            <h5 class="fw-bold mb-3">基礎入門階段 (1-3個月)</h5>
                            <p>學習佛教基本概念與正念基礎，建立良好的學習習慣與心態。</p>
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">主要內容：</small>
                                    <ul class="small">
                                        <li>佛教基本教義介紹</li>
                                        <li>正念呼吸練習</li>
                                        <li>基礎冥想技巧</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted">學習成果：</small>
                                    <ul class="small">
                                        <li>建立日常修習習慣</li>
                                        <li>掌握基礎正念技巧</li>
                                        <li>理解佛教基本概念</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="step-arrow">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                        <div class="path-step">
                            <div class="step-number">2</div>
                            <h5 class="fw-bold mb-3">進階學習階段 (4-8個月)</h5>
                            <p>深入學習經文教義與療癒方法，開始實際應用於日常生活中。</p>
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">主要內容：</small>
                                    <ul class="small">
                                        <li>經典文獻研讀</li>
                                        <li>進階冥想練習</li>
                                        <li>情緒療癒技巧</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted">學習成果：</small>
                                    <ul class="small">
                                        <li>深化佛法理解</li>
                                        <li>掌握療癒方法</li>
                                        <li>提升修習品質</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="step-arrow">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                        <div class="path-step">
                            <div class="step-number">3</div>
                            <h5 class="fw-bold mb-3">專業實踐階段 (9-12個月)</h5>
                            <p>整合所學知識，參與實習與工作坊，具備指導他人的基礎能力。</p>
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">主要內容：</small>
                                    <ul class="small">
                                        <li>實習指導經驗</li>
                                        <li>個案討論分析</li>
                                        <li>研究方法學習</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted">學習成果：</small>
                                    <ul class="small">
                                        <li>具備指導能力</li>
                                        <li>完成研究專案</li>
                                        <li>獲得認證資格</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 技能發展目標 -->
        <section class="skills-grid">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 class="section-title h1 fw-bold">技能發展目標</h2>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-4">
                        <h5 class="fw-bold mb-4 text-success">
                            <i class="fas fa-star me-2"></i>初級技能
                        </h5>
                        <div class="skill-item skill-beginner">
                            <div class="skill-icon text-success">
                                <i class="fas fa-seedling"></i>
                            </div>
                            <h6 class="fw-bold">正念覺察</h6>
                            <p class="small">建立基礎的覺察能力，能夠觀察當下的身心狀態。</p>
                        </div>
                        <div class="skill-item skill-beginner">
                            <div class="skill-icon text-success">
                                <i class="fas fa-wind"></i>
                            </div>
                            <h6 class="fw-bold">呼吸調節</h6>
                            <p class="small">掌握基本呼吸技巧，能夠透過呼吸穩定情緒。</p>
                        </div>
                        <div class="skill-item skill-beginner">
                            <div class="skill-icon text-success">
                                <i class="fas fa-book"></i>
                            </div>
                            <h6 class="fw-bold">經文理解</h6>
                            <p class="small">理解基礎佛教概念，能夠閱讀簡單經文。</p>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <h5 class="fw-bold mb-4" style="color: var(--secondary-color);">
                            <i class="fas fa-star me-2"></i>中級技能
                        </h5>
                        <div class="skill-item skill-intermediate">
                            <div class="skill-icon" style="color: var(--secondary-color);">
                                <i class="fas fa-tree"></i>
                            </div>
                            <h6 class="fw-bold">深度冥想</h6>
                            <p class="small">能夠進行長時間冥想，達到深層的內心寧靜。</p>
                        </div>
                        <div class="skill-item skill-intermediate">
                            <div class="skill-icon" style="color: var(--secondary-color);">
                                <i class="fas fa-hands-helping"></i>
                            </div>
                            <h6 class="fw-bold">情緒療癒</h6>
                            <p class="small">掌握各種情緒調節技巧，能夠幫助自己和他人。</p>
                        </div>
                        <div class="skill-item skill-intermediate">
                            <div class="skill-icon" style="color: var(--secondary-color);">
                                <i class="fas fa-scroll"></i>
                            </div>
                            <h6 class="fw-bold">經典研讀</h6>
                            <p class="small">能夠深入研讀佛教經典，理解深層含義。</p>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <h5 class="fw-bold mb-4" style="color: var(--primary-color);">
                            <i class="fas fa-star me-2"></i>高級技能
                        </h5>
                        <div class="skill-item skill-advanced">
                            <div class="skill-icon" style="color: var(--primary-color);">
                                <i class="fas fa-mountain"></i>
                            </div>
                            <h6 class="fw-bold">指導教學</h6>
                            <p class="small">具備指導他人修習的能力，能夠設計課程內容。</p>
                        </div>
                        <div class="skill-item skill-advanced">
                            <div class="skill-icon" style="color: var(--primary-color);">
                                <i class="fas fa-microscope"></i>
                            </div>
                            <h6 class="fw-bold">研究分析</h6>
                            <p class="small">能夠進行學術研究，分析療癒方法的有效性。</p>
                        </div>
                        <div class="skill-item skill-advanced">
                            <div class="skill-icon" style="color: var(--primary-color);">
                                <i class="fas fa-globe"></i>
                            </div>
                            <h6 class="fw-bold">跨文化應用</h6>
                            <p class="small">理解不同文化背景下的佛教療癒應用方式。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 預期學習成果 -->
        <section class="expected-outcomes">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-5">
                        <h2 class="fw-bold" style="color: var(--secondary-color);">預期學習成果</h2>
                        <p class="lead">完成課程後，您將獲得以下能力與認證</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-6">
                        <div class="outcome-card">
                            <div class="outcome-icon text-center">
                                <i class="fas fa-certificate"></i>
                            </div>
                            <h5 class="fw-bold text-center mb-3">專業認證</h5>
                            <p class="text-center">獲得佛教身心療癒指導師認證，具備專業指導資格。</p>
                            <div class="progress-bar-custom">
                                <div class="progress-fill" data-width="95%"></div>
                            </div>
                            <small class="d-block text-center mt-2">認證通過率：95%</small>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="outcome-card">
                            <div class="outcome-icon text-center">
                                <i class="fas fa-users"></i>
                            </div>
                            <h5 class="fw-bold text-center mb-3">社群網絡</h5>
                            <p class="text-center">加入專業學習社群，與同道一起持續成長與分享。</p>
                            <div class="progress-bar-custom">
                                <div class="progress-fill" data-width="100%"></div>
                            </div>
                            <small class="d-block text-center mt-2">社群參與度：100%</small>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="outcome-card">
                            <div class="outcome-icon text-center">
                                <i class="fas fa-brain"></i>
                            </div>
                            <h5 class="fw-bold text-center mb-3">個人成長</h5>
                            <p class="text-center">顯著提升身心健康水平，建立穩定的修習習慣。</p>
                            <div class="progress-bar-custom">
                                <div class="progress-fill" data-width="90%"></div>
                            </div>
                            <small class="d-block text-center mt-2">滿意度：90%</small>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="outcome-card">
                            <div class="outcome-icon text-center">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <h5 class="fw-bold text-center mb-3">發展機會</h5>
                            <p class="text-center">開啟療癒師職業發展道路，或進階學術研究機會。</p>
                            <div class="progress-bar-custom">
                                <div class="progress-fill" data-width="85%"></div>
                            </div>
                            <small class="d-block text-center mt-2">就業成功率：85%</small>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 行動呼籲 -->
        <section class="cta-section">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8 text-center">
                        <h2 class="fw-bold mb-4">準備開始您的學習之旅嗎？</h2>
                        <p class="lead mb-5">立即加入我們的課程，開啟身心療癒的智慧之路。專業導師將陪伴您完成每一個學習階段。</p>
                        <div class="d-flex justify-content-center gap-3 flex-wrap">
                            <a href="membership.html" class="btn-primary-custom">立即報名</a>
                            <a href="courses-weekly.html" class="btn btn-outline-primary btn-lg">查看課程大綱</a>
                            <a href="courses-methods.html" class="btn btn-outline-secondary btn-lg">了解學習方式</a>
                        </div>
                        <div class="mt-5">
                            <div class="row text-center">
                                <div class="col-lg-3 col-6">
                                    <h3 class="fw-bold text-primary">500+</h3>
                                    <small class="text-muted">已完成學員</small>
                                </div>
                                <div class="col-lg-3 col-6">
                                    <h3 class="fw-bold text-primary">95%</h3>
                                    <small class="text-muted">課程滿意度</small>
                                </div>
                                <div class="col-lg-3 col-6">
                                    <h3 class="fw-bold text-primary">12</h3>
                                    <small class="text-muted">月完整課程</small>
                                </div>
                                <div class="col-lg-3 col-6">
                                    <h3 class="fw-bold text-primary">24/7</h3>
                                    <small class="text-muted">學習支援</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // 導航欄滾動效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar-custom');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // 進度條動畫
        const progressBars = document.querySelectorAll('.progress-fill');
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        };

        const progressObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const progressBar = entry.target;
                    const targetWidth = progressBar.getAttribute('data-width');
                    setTimeout(() => {
                        progressBar.style.width = targetWidth;
                    }, 500);
                }
            });
        }, observerOptions);

        progressBars.forEach(bar => {
            bar.style.width = '0%';
            progressObserver.observe(bar);
        });

        // 卡片動畫
        const cardObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        // 為各種元素添加動畫
        document.addEventListener('DOMContentLoaded', function() {
            const animatedElements = document.querySelectorAll('.goal-card, .path-step, .skill-item, .outcome-card');
            
            animatedElements.forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = 'all 0.6s ease';
                el.style.transitionDelay = `${index * 0.1}s`;
                cardObserver.observe(el);
            });
        });

        // 技能卡片hover效果增強
        document.querySelectorAll('.skill-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.boxShadow = '0 15px 35px rgba(0,0,0,0.15)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
            });
        });

        // 目標卡片的特殊效果
        document.querySelectorAll('.goal-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                const icon = this.querySelector('.goal-icon i');
                icon.style.transform = 'scale(1.1) rotate(5deg)';
                icon.style.transition = 'transform 0.3s ease';
            });
            
            card.addEventListener('mouseleave', function() {
                const icon = this.querySelector('.goal-icon i');
                icon.style.transform = 'scale(1) rotate(0deg)';
            });
        });
    </script>
</body>
</html>
                            bar-custom {
            background: rgba(44, 62, 80, 0.95);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .nav