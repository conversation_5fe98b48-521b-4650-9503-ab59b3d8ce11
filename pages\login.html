<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登錄 - 澄源閱讀</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            overflow: hidden;
        }

        /* 背景動畫 */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 8s infinite ease-in-out;
        }

        .floating-element:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .floating-element:nth-child(2) { top: 20%; right: 10%; animation-delay: 2s; }
        .floating-element:nth-child(3) { bottom: 20%; left: 20%; animation-delay: 4s; }
        .floating-element:nth-child(4) { bottom: 10%; right: 20%; animation-delay: 6s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* 登錄容器 */
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        /* 標題區域 */
        .login-header {
            margin-bottom: 30px;
        }

        .logo {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 10px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .subtitle {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* 表單樣式 */
        .login-form {
            margin-bottom: 20px;
        }

        .input-group {
            position: relative;
            margin-bottom: 20px;
        }

        .input-field {
            width: 100%;
            padding: 15px 20px 15px 50px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .input-field:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: #667eea;
            font-size: 1.1rem;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #667eea;
            cursor: pointer;
            font-size: 1.1rem;
            padding: 5px;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        /* 載入動畫 */
        .btn-loading {
            display: none;
            margin-right: 10px;
        }

        .loading .btn-loading {
            display: inline-block;
        }

        .loading .btn-text {
            display: none;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .btn-loading i {
            animation: spin 1s linear infinite;
        }

        /* 錯誤提示 */
        .error-message {
            background: #fee;
            color: #d32f2f;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #d32f2f;
            display: none;
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .error-message.show {
            display: block;
        }

        /* 幫助信息 */
        .help-info {
            background: #e3f2fd;
            color: #1976d2;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-size: 0.9rem;
            line-height: 1.5;
            border-left: 4px solid #1976d2;
        }

        .help-info i {
            margin-right: 8px;
        }

        /* 底部信息 */
        .login-footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
            color: #666;
            font-size: 0.8rem;
        }

        /* 響應式設計 */
        @media (max-width: 480px) {
            .login-container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .title {
                font-size: 1.5rem;
            }
            
            .logo {
                font-size: 2.5rem;
            }
        }

        /* 深色模式支持 */
        @media (prefers-color-scheme: dark) {
            body {
                background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            }
            
            .login-container {
                background: rgba(44, 62, 80, 0.95);
                color: #ecf0f1;
            }
            
            .title {
                color: #ecf0f1;
            }
            
            .input-field {
                background: #34495e;
                border-color: #4a6741;
                color: #ecf0f1;
            }
            
            .input-field:focus {
                background: #2c3e50;
                border-color: #667eea;
            }
        }
    </style>
</head>
<body>
    <!-- 背景動畫 -->
    <div class="bg-animation">
        <div class="floating-element">
            <i class="fas fa-lotus" style="font-size: 30px;"></i>
        </div>
        <div class="floating-element">
            <i class="fas fa-dharmachakra" style="font-size: 25px;"></i>
        </div>
        <div class="floating-element">
            <i class="fas fa-spa" style="font-size: 35px;"></i>
        </div>
        <div class="floating-element">
            <i class="fas fa-om" style="font-size: 28px;"></i>
        </div>
    </div>

    <!-- 登錄容器 -->
    <div class="login-container">
        <div class="login-header">
            <div class="logo">
                <i class="fas fa-lotus"></i>
            </div>
            <h1 class="title">澄源閱讀</h1>
            <p class="subtitle">佛學與身心療癒智慧<br>請輸入密碼繼續</p>
        </div>

        <!-- 錯誤提示 -->
        <div class="error-message" id="error-message">
            <i class="fas fa-exclamation-triangle"></i>
            <span id="error-text">密碼錯誤，請重新輸入</span>
        </div>

        <!-- 登錄表單 -->
        <form class="login-form" id="login-form">
            <div class="input-group">
                <i class="fas fa-lock input-icon"></i>
                <input 
                    type="password" 
                    class="input-field" 
                    id="password-input"
                    placeholder="請輸入訪問密碼"
                    autocomplete="current-password"
                    required
                >
                <button type="button" class="password-toggle" id="password-toggle">
                    <i class="fas fa-eye"></i>
                </button>
            </div>

            <button type="submit" class="login-btn" id="login-btn">
                <span class="btn-loading">
                    <i class="fas fa-spinner"></i>
                </span>
                <span class="btn-text">
                    <i class="fas fa-sign-in-alt"></i>
                    進入澄源閱讀
                </span>
            </button>
        </form>

        <!-- 幫助信息 -->
        <div class="help-info">
            <i class="fas fa-info-circle"></i>
            為保護內容安全，本站采用密碼驗證方式。請聯絡管理員獲取訪問密碼。
        </div>

        <!-- 底部信息 -->
        <div class="login-footer">
            <p>&copy; 2025 澄源閱讀. 傳遞智慧，淨化心靈.</p>
        </div>
    </div>

    <script>
        // 預設密碼 (實際使用時應該加密或從後端驗證)
        const VALID_PASSWORDS = [
            'chengyuan2025',
            'dharma123', 
            'healing2025',
            'buddha@wisdom',
            'mindfulness2025'
        ];
        
        // DOM 元素
        const loginForm = document.getElementById('login-form');
        const passwordInput = document.getElementById('password-input');
        const passwordToggle = document.getElementById('password-toggle');
        const loginBtn = document.getElementById('login-btn');
        const errorMessage = document.getElementById('error-message');
        const errorText = document.getElementById('error-text');

        // 密碼顯示/隱藏切換
        passwordToggle.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            const icon = this.querySelector('i');
            icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
        });

        // 表單提交
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const password = passwordInput.value.trim();
            
            if (!password) {
                showError('請輸入密碼');
                return;
            }

            // 顯示載入狀態
            showLoading(true);
            hideError();

            // 模擬網路延遲
            setTimeout(() => {
                if (VALID_PASSWORDS.includes(password)) {
                    // 密碼正確
                    showLoading(false);
                    
                    // 本地存儲登錄狀態
                    localStorage.setItem('chengyuan_auth', 'true');
                    
                    // 發送消息給父頁面
                    if (window.parent !== window) {
                        window.parent.postMessage({
                            type: 'login_success'
                        }, '*');
                    } else {
                        // 如果不在 iframe 中，直接跳轉
                        setTimeout(() => {
                            window.location.href = '../index.html';
                        }, 1000);
                    }
                    
                    // 顯示成功動畫
                    showSuccessAnimation();
                    
                } else {
                    // 密碼錯誤
                    showLoading(false);
                    showError('密碼錯誤，請重新輸入');
                    passwordInput.value = '';
                    passwordInput.focus();
                    
                    // 震動效果 (如果支持)
                    if (navigator.vibrate) {
                        navigator.vibrate(200);
                    }
                }
            }, 1500);
        });

        // 顯示載入狀態
        function showLoading(loading) {
            if (loading) {
                loginBtn.classList.add('loading');
                loginBtn.disabled = true;
            } else {
                loginBtn.classList.remove('loading');
                loginBtn.disabled = false;
            }
        }

        // 顯示錯誤
        function showError(message) {
            errorText.textContent = message;
            errorMessage.classList.add('show');
            
            // 3秒後自動隱藏
            setTimeout(hideError, 3000);
        }

        // 隱藏錯誤
        function hideError() {
            errorMessage.classList.remove('show');
        }

        // 成功動畫
        function showSuccessAnimation() {
            loginBtn.innerHTML = `
                <i class="fas fa-check" style="color: #4caf50; font-size: 1.2rem;"></i>
                登錄成功
            `;
            loginBtn.style.background = '#4caf50';
            
            setTimeout(() => {
                loginBtn.innerHTML = `
                    <i class="fas fa-spinner" style="animation: spin 1s linear infinite;"></i>
                    正在進入...
                `;
            }, 1000);
        }

        // 輸入框回車鍵支持
        passwordInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                loginForm.dispatchEvent(new Event('submit'));
            }
        });

        // 頁面載入時自動聚焦
        window.addEventListener('load', function() {
            passwordInput.focus();
        });

        // 清除錯誤當用戶開始輸入
        passwordInput.addEventListener('input', function() {
            if (errorMessage.classList.contains('show')) {
                hideError();
            }
        });

        // 開發模式：三次點擊logo快速登錄
        let logoClickCount = 0;
        document.querySelector('.logo').addEventListener('click', function() {
            logoClickCount++;
            
            if (logoClickCount === 3) {
                passwordInput.value = VALID_PASSWORDS[0];
                loginForm.dispatchEvent(new Event('submit'));
                logoClickCount = 0;
            }
            
            // 重置計數器
            setTimeout(() => {
                logoClickCount = 0;
            }, 2000);
        });

        // 支持密碼管理器
        passwordInput.addEventListener('change', function() {
            if (this.value) {
                // 輕微延遲以確保密碼管理器完成填充
                setTimeout(() => {
                    if (VALID_PASSWORDS.includes(this.value)) {
                        loginForm.dispatchEvent(new Event('submit'));
                    }
                }, 100);
            }
        });
    </script>
</body>
</html>