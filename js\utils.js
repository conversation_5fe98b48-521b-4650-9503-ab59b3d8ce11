/**
 * 澄源閱讀 - 工具函數庫
 * 提供通用的工具函數和實用程序
 */

// ===== 常量定義 =====
const CONSTANTS = {
    APP_NAME: '澄源閱讀',
    VERSION: '1.0.0',
    API_BASE_URL: '/api',
    STORAGE_PREFIX: 'chengyuan_',
    SUPPORTED_LANGUAGES: ['zh-TW', 'zh-CN', 'en'],
    THEME_OPTIONS: ['light', 'dark', 'auto'],
    TOAST_DURATION: 3000,
    DEBOUNCE_DELAY: 300,
    THROTTLE_DELAY: 100
};

// ===== DOM 操作工具 =====
const DOM = {
    /**
     * 安全地獲取DOM元素
     * @param {string} selector - CSS選擇器
     * @param {Element} context - 搜索上下文
     * @returns {Element|null}
     */
    get(selector, context = document) {
        try {
            return context.querySelector(selector);
        } catch (error) {
            console.warn(`Invalid selector: ${selector}`, error);
            return null;
        }
    },

    /**
     * 安全地獲取多個DOM元素
     * @param {string} selector - CSS選擇器
     * @param {Element} context - 搜索上下文
     * @returns {NodeList}
     */
    getAll(selector, context = document) {
        try {
            return context.querySelectorAll(selector);
        } catch (error) {
            console.warn(`Invalid selector: ${selector}`, error);
            return [];
        }
    },

    /**
     * 創建DOM元素
     * @param {string} tag - 標籤名
     * @param {Object} attributes - 屬性對象
     * @param {string} content - 內容
     * @returns {Element}
     */
    create(tag, attributes = {}, content = '') {
        const element = document.createElement(tag);
        
        Object.entries(attributes).forEach(([key, value]) => {
            if (key === 'className') {
                element.className = value;
            } else if (key === 'innerHTML') {
                element.innerHTML = value;
            } else if (key === 'textContent') {
                element.textContent = value;
            } else if (key.startsWith('data-')) {
                element.setAttribute(key, value);
            } else {
                element[key] = value;
            }
        });

        if (content) {
            element.textContent = content;
        }

        return element;
    },

    /**
     * 檢查元素是否在視窗中
     * @param {Element} element - 要檢查的元素
     * @returns {boolean}
     */
    isInViewport(element) {
        if (!element) return false;
        
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    },

    /**
     * 平滑滾動到元素
     * @param {Element} element - 目標元素
     * @param {Object} options - 滾動選項
     */
    scrollTo(element, options = {}) {
        if (!element) return;
        
        const defaultOptions = {
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest'
        };
        
        element.scrollIntoView({ ...defaultOptions, ...options });
    },

    /**
     * 添加事件監聽器
     * @param {Element|string} target - 目標元素或選擇器
     * @param {string} event - 事件名稱
     * @param {Function} handler - 事件處理器
     * @param {Object} options - 事件選項
     */
    on(target, event, handler, options = {}) {
        const element = typeof target === 'string' ? this.get(target) : target;
        if (element && typeof handler === 'function') {
            element.addEventListener(event, handler, options);
        }
    },

    /**
     * 移除事件監聽器
     * @param {Element|string} target - 目標元素或選擇器
     * @param {string} event - 事件名稱
     * @param {Function} handler - 事件處理器
     */
    off(target, event, handler) {
        const element = typeof target === 'string' ? this.get(target) : target;
        if (element && typeof handler === 'function') {
            element.removeEventListener(event, handler);
        }
    }
};

// ===== 本地存儲工具 =====
const Storage = {
    /**
     * 設置存儲項
     * @param {string} key - 鍵名
     * @param {*} value - 值
     * @param {boolean} session - 是否使用sessionStorage
     */
    set(key, value, session = false) {
        try {
            const prefixedKey = CONSTANTS.STORAGE_PREFIX + key;
            const storage = session ? sessionStorage : localStorage;
            const serializedValue = JSON.stringify(value);
            storage.setItem(prefixedKey, serializedValue);
        } catch (error) {
            console.error('Storage.set error:', error);
        }
    },

    /**
     * 獲取存儲項
     * @param {string} key - 鍵名
     * @param {*} defaultValue - 默認值
     * @param {boolean} session - 是否使用sessionStorage
     * @returns {*}
     */
    get(key, defaultValue = null, session = false) {
        try {
            const prefixedKey = CONSTANTS.STORAGE_PREFIX + key;
            const storage = session ? sessionStorage : localStorage;
            const item = storage.getItem(prefixedKey);
            
            if (item === null) return defaultValue;
            return JSON.parse(item);
        } catch (error) {
            console.error('Storage.get error:', error);
            return defaultValue;
        }
    },

    /**
     * 移除存儲項
     * @param {string} key - 鍵名
     * @param {boolean} session - 是否使用sessionStorage
     */
    remove(key, session = false) {
        try {
            const prefixedKey = CONSTANTS.STORAGE_PREFIX + key;
            const storage = session ? sessionStorage : localStorage;
            storage.removeItem(prefixedKey);
        } catch (error) {
            console.error('Storage.remove error:', error);
        }
    },

    /**
     * 清空所有應用存儲
     * @param {boolean} session - 是否使用sessionStorage
     */
    clear(session = false) {
        try {
            const storage = session ? sessionStorage : localStorage;
            const keys = Object.keys(storage);
            
            keys.forEach(key => {
                if (key.startsWith(CONSTANTS.STORAGE_PREFIX)) {
                    storage.removeItem(key);
                }
            });
        } catch (error) {
            console.error('Storage.clear error:', error);
        }
    }
};

// ===== 數據驗證工具 =====
const Validator = {
    /**
     * 驗證電子郵件格式
     * @param {string} email - 電子郵件地址
     * @returns {boolean}
     */
    isEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    /**
     * 驗證手機號碼格式（台灣）
     * @param {string} phone - 手機號碼
     * @returns {boolean}
     */
    isPhone(phone) {
        const phoneRegex = /^09\d{8}$/;
        return phoneRegex.test(phone);
    },

    /**
     * 驗證URL格式
     * @param {string} url - URL地址
     * @returns {boolean}
     */
    isURL(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    },

    /**
     * 驗證字符串長度
     * @param {string} str - 字符串
     * @param {number} min - 最小長度
     * @param {number} max - 最大長度
     * @returns {boolean}
     */
    isLength(str, min = 0, max = Infinity) {
        const length = str ? str.length : 0;
        return length >= min && length <= max;
    },

    /**
     * 驗證是否為空
     * @param {*} value - 要檢查的值
     * @returns {boolean}
     */
    isEmpty(value) {
        if (value == null) return true;
        if (typeof value === 'string') return value.trim().length === 0;
        if (Array.isArray(value)) return value.length === 0;
        if (typeof value === 'object') return Object.keys(value).length === 0;
        return false;
    },

    /**
     * 驗證密碼強度
     * @param {string} password - 密碼
     * @returns {Object} 包含強度級別和建議的對象
     */
    checkPasswordStrength(password) {
        if (!password) {
            return { level: 0, message: '請輸入密碼' };
        }

        let score = 0;
        const checks = {
            length: password.length >= 8,
            uppercase: /[A-Z]/.test(password),
            lowercase: /[a-z]/.test(password),
            numbers: /\d/.test(password),
            symbols: /[^A-Za-z0-9]/.test(password)
        };

        score = Object.values(checks).filter(Boolean).length;

        const levels = [
            { level: 0, message: '密碼太弱', color: '#f44336' },
            { level: 1, message: '密碼很弱', color: '#ff9800' },
            { level: 2, message: '密碼較弱', color: '#ff9800' },
            { level: 3, message: '密碼中等', color: '#2196f3' },
            { level: 4, message: '密碼較強', color: '#4caf50' },
            { level: 5, message: '密碼很強', color: '#4caf50' }
        ];

        return { ...levels[score], checks };
    }
};

// ===== 格式化工具 =====
const Format = {
    /**
     * 格式化日期
     * @param {Date|string|number} date - 日期
     * @param {string} format - 格式字符串
     * @returns {string}
     */
    date(date, format = 'YYYY-MM-DD') {
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';

        const formatMap = {
            YYYY: d.getFullYear(),
            MM: String(d.getMonth() + 1).padStart(2, '0'),
            DD: String(d.getDate()).padStart(2, '0'),
            HH: String(d.getHours()).padStart(2, '0'),
            mm: String(d.getMinutes()).padStart(2, '0'),
            ss: String(d.getSeconds()).padStart(2, '0')
        };

        return format.replace(/YYYY|MM|DD|HH|mm|ss/g, match => formatMap[match]);
    },

    /**
     * 格式化數字
     * @param {number} num - 數字
     * @param {number} decimals - 小數位數
     * @returns {string}
     */
    number(num, decimals = 0) {
        if (isNaN(num)) return '0';
        return Number(num).toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },

    /**
     * 格式化文件大小
     * @param {number} bytes - 字節數
     * @param {number} decimals - 小數位數
     * @returns {string}
     */
    fileSize(bytes, decimals = 2) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + ' ' + sizes[i];
    },

    /**
     * 格式化相對時間
     * @param {Date|string|number} date - 日期
     * @returns {string}
     */
    relativeTime(date) {
        const now = new Date();
        const target = new Date(date);
        const diffInSeconds = Math.floor((now - target) / 1000);

        const intervals = [
            { label: '年', seconds: 31536000 },
            { label: '個月', seconds: 2592000 },
            { label: '天', seconds: 86400 },
            { label: '小時', seconds: 3600 },
            { label: '分鐘', seconds: 60 },
            { label: '秒', seconds: 1 }
        ];

        for (let interval of intervals) {
            const count = Math.floor(diffInSeconds / interval.seconds);
            if (count > 0) {
                return `${count}${interval.label}前`;
            }
        }

        return '剛剛';
    },

    /**
     * 截斷文本
     * @param {string} text - 文本
     * @param {number} length - 最大長度
     * @param {string} suffix - 後綴
     * @returns {string}
     */
    truncate(text, length = 100, suffix = '...') {
        if (!text || text.length <= length) return text || '';
        return text.slice(0, length) + suffix;
    }
};

// ===== 防抖和節流工具 =====
const Performance = {
    /**
     * 防抖函數
     * @param {Function} func - 要防抖的函數
     * @param {number} delay - 延遲時間（毫秒）
     * @returns {Function}
     */
    debounce(func, delay = CONSTANTS.DEBOUNCE_DELAY) {
        let timeoutId;
        return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    },

    /**
     * 節流函數
     * @param {Function} func - 要節流的函數
     * @param {number} delay - 延遲時間（毫秒）
     * @returns {Function}
     */
    throttle(func, delay = CONSTANTS.THROTTLE_DELAY) {
        let lastCall = 0;
        return function (...args) {
            const now = Date.now();
            if (now - lastCall >= delay) {
                lastCall = now;
                return func.apply(this, args);
            }
        };
    },

    /**
     * 延遲執行
     * @param {number} ms - 延遲毫秒數
     * @returns {Promise}
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    },

    /**
     * 請求動畫幀節流
     * @param {Function} func - 要執行的函數
     * @returns {Function}
     */
    rafThrottle(func) {
        let requestId = null;
        return function (...args) {
            if (requestId === null) {
                requestId = requestAnimationFrame(() => {
                    func.apply(this, args);
                    requestId = null;
                });
            }
        };
    }
};

// ===== URL 和查詢字符串工具 =====
const URL_Utils = {
    /**
     * 解析查詢字符串
     * @param {string} query - 查詢字符串
     * @returns {Object}
     */
    parseQuery(query = window.location.search) {
        const params = new URLSearchParams(query);
        const result = {};
        for (let [key, value] of params) {
            result[key] = value;
        }
        return result;
    },

    /**
     * 構建查詢字符串
     * @param {Object} params - 參數對象
     * @returns {string}
     */
    buildQuery(params) {
        const searchParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                searchParams.append(key, value);
            }
        });
        return searchParams.toString();
    },

    /**
     * 更新URL參數
     * @param {Object} params - 新參數
     * @param {boolean} replace - 是否替換當前歷史記錄
     */
    updateParams(params, replace = false) {
        const url = new URL(window.location);
        Object.entries(params).forEach(([key, value]) => {
            if (value === null || value === undefined) {
                url.searchParams.delete(key);
            } else {
                url.searchParams.set(key, value);
            }
        });

        if (replace) {
            window.history.replaceState({}, '', url);
        } else {
            window.history.pushState({}, '', url);
        }
    }
};

// ===== 設備檢測工具 =====
const Device = {
    /**
     * 檢測是否為移動設備
     * @returns {boolean}
     */
    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },

    /**
     * 檢測是否為平板設備
     * @returns {boolean}
     */
    isTablet() {
        return /iPad|Android(?!.*Mobile)/i.test(navigator.userAgent);
    },

    /**
     * 檢測是否支持觸摸
     * @returns {boolean}
     */
    isTouchDevice() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    },

    /**
     * 獲取視窗大小
     * @returns {Object}
     */
    getViewportSize() {
        return {
            width: window.innerWidth || document.documentElement.clientWidth,
            height: window.innerHeight || document.documentElement.clientHeight
        };
    },

    /**
     * 檢測當前斷點
     * @returns {string}
     */
    getBreakpoint() {
        const width = this.getViewportSize().width;
        if (width < 576) return 'xs';
        if (width < 768) return 'sm';
        if (width < 992) return 'md';
        if (width < 1200) return 'lg';
        return 'xl';
    }
};

// ===== 圖片處理工具 =====
const Image = {
    /**
     * 預載入圖片
     * @param {string|string[]} src - 圖片URL或URL數組
     * @returns {Promise}
     */
    preload(src) {
        const sources = Array.isArray(src) ? src : [src];
        const promises = sources.map(url => {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => resolve(img);
                img.onerror = reject;
                img.src = url;
            });
        });
        return Promise.all(promises);
    },

    /**
     * 壓縮圖片
     * @param {File} file - 圖片文件
     * @param {Object} options - 壓縮選項
     * @returns {Promise<Blob>}
     */
    compress(file, options = {}) {
        const {
            maxWidth = 1920,
            maxHeight = 1080,
            quality = 0.8,
            type = 'image/jpeg'
        } = options;

        return new Promise((resolve, reject) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                const { width, height } = img;
                const ratio = Math.min(maxWidth / width, maxHeight / height);
                
                canvas.width = width * ratio;
                canvas.height = height * ratio;
                
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                
                canvas.toBlob(resolve, type, quality);
            };

            img.onerror = reject;
            img.src = URL.createObjectURL(file);
        });
    }
};

// ===== 錯誤處理工具 =====
const ErrorHandler = {
    /**
     * 處理錯誤
     * @param {Error} error - 錯誤對象
     * @param {string} context - 上下文信息
     */
    handle(error, context = '') {
        console.error(`[${CONSTANTS.APP_NAME}] Error in ${context}:`, error);
        
        // 可以在這裡添加錯誤上報邏輯
        if (window.gtag) {
            window.gtag('event', 'exception', {
                description: error.message,
                fatal: false
            });
        }
    },

    /**
     * 安全執行函數
     * @param {Function} func - 要執行的函數
     * @param {string} context - 上下文信息
     * @param {...any} args - 函數參數
     * @returns {*}
     */
    safe(func, context = '', ...args) {
        try {
            return func(...args);
        } catch (error) {
            this.handle(error, context);
            return null;
        }
    }
};

// ===== 導出工具對象 =====
window.Utils = {
    CONSTANTS,
    DOM,
    Storage,
    Validator,
    Format,
    Performance,
    URL_Utils,
    Device,
    Image,
    ErrorHandler
};

// 兼容性檢查和 Polyfills
(function initPolyfills() {
    // Promise polyfill for older browsers
    if (!window.Promise) {
        console.warn('Promise not supported, please include a polyfill');
    }

    // URLSearchParams polyfill for older browsers
    if (!window.URLSearchParams) {
        console.warn('URLSearchParams not supported, please include a polyfill');
    }

    // IntersectionObserver polyfill hint
    if (!window.IntersectionObserver) {
        console.warn('IntersectionObserver not supported, consider including a polyfill');
    }
})();

console.log(`${CONSTANTS.APP_NAME} Utils v${CONSTANTS.VERSION} loaded`);