-- 澄源閱讀 - Cloudflare D1 數據庫結構
-- 創建時間: 2025-01-12

-- 文章表
CREATE TABLE IF NOT EXISTS articles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    category TEXT NOT NULL DEFAULT 'dharma',
    author <PERSON><PERSON><PERSON>,
    author_avatar TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    published_at DATETIME,
    status TEXT DEFAULT 'published', -- published, draft, archived
    featured_image TEXT,
    featured_image_alt TEXT,
    tags TEXT, -- JSON array stored as text
    views INTEGER DEFAULT 0,
    likes INTEGER DEFAULT 0,
    reading_time INTEGER, -- 預估閱讀時間（分鐘）
    difficulty TEXT DEFAULT 'beginner', -- beginner, intermediate, advanced
    language TEXT DEFAULT 'zh-TW',
    seo_title TEXT,
    seo_description TEXT,
    slug TEXT UNIQUE
);

-- 分類表
CREATE TABLE IF NOT EXISTS categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    icon TEXT,
    color TEXT,
    parent_id INTEGER,
    sort_order INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories (id)
);

-- 標籤表
CREATE TABLE IF NOT EXISTS tags (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    slug TEXT NOT NULL UNIQUE,
    color TEXT,
    usage_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 文章標籤關聯表
CREATE TABLE IF NOT EXISTS article_tags (
    article_id INTEGER NOT NULL,
    tag_id INTEGER NOT NULL,
    PRIMARY KEY (article_id, tag_id),
    FOREIGN KEY (article_id) REFERENCES articles (id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags (id) ON DELETE CASCADE
);

-- 圖片信息表
CREATE TABLE IF NOT EXISTS images (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    filename TEXT NOT NULL,
    original_name TEXT NOT NULL,
    file_size INTEGER,
    mime_type TEXT,
    width INTEGER,
    height INTEGER,
    r2_url TEXT NOT NULL,
    r2_key TEXT NOT NULL,
    alt_text TEXT,
    caption TEXT,
    article_id INTEGER,
    uploaded_by TEXT,
    uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (article_id) REFERENCES articles (id) ON DELETE SET NULL
);

-- 用戶收藏表（本地存儲備份）
CREATE TABLE IF NOT EXISTS favorites (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,
    article_id INTEGER NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (article_id) REFERENCES articles (id) ON DELETE CASCADE
);

-- 搜索日誌表
CREATE TABLE IF NOT EXISTS search_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    query TEXT NOT NULL,
    results_count INTEGER,
    session_id TEXT,
    ip_address TEXT,
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 文章瀏覽記錄表
CREATE TABLE IF NOT EXISTS article_views (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    article_id INTEGER NOT NULL,
    session_id TEXT,
    ip_address TEXT,
    user_agent TEXT,
    referrer TEXT,
    view_duration INTEGER, -- 瀏覽時長（秒）
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (article_id) REFERENCES articles (id) ON DELETE CASCADE
);

-- 評論表
CREATE TABLE IF NOT EXISTS comments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    article_id INTEGER NOT NULL,
    author_name TEXT,
    author_email TEXT,
    content TEXT NOT NULL,
    status TEXT DEFAULT 'pending', -- pending, approved, rejected, spam
    ip_address TEXT,
    user_agent TEXT,
    parent_id INTEGER, -- 回覆評論
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (article_id) REFERENCES articles (id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES comments (id) ON DELETE CASCADE
);

-- 網站配置表
CREATE TABLE IF NOT EXISTS site_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key TEXT NOT NULL UNIQUE,
    config_value TEXT,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 研究論文表
CREATE TABLE IF NOT EXISTS research_papers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    abstract TEXT,
    authors TEXT, -- JSON array
    institution TEXT,
    journal TEXT,
    publication_date DATE,
    doi TEXT,
    pmid TEXT,
    keywords TEXT, -- JSON array
    category TEXT,
    impact_factor REAL,
    citation_count INTEGER DEFAULT 0,
    pdf_url TEXT,
    research_type TEXT, -- clinical_trial, meta_analysis, review, etc.
    study_participants INTEGER,
    study_duration TEXT,
    findings TEXT,
    conclusions TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 療癒內容表
CREATE TABLE IF NOT EXISTS healing_content (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    description TEXT,
    type TEXT NOT NULL, -- meditation, breathing, movement, sound, visualization, energy
    difficulty TEXT DEFAULT 'beginner',
    duration INTEGER, -- 持續時間（分鐘）
    instructions TEXT, -- 詳細指導
    audio_url TEXT,
    video_url TEXT,
    thumbnail_url TEXT,
    benefits TEXT, -- JSON array
    contraindications TEXT,
    tags TEXT, -- JSON array
    views INTEGER DEFAULT 0,
    likes INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 創建索引以提升查詢性能
CREATE INDEX IF NOT EXISTS idx_articles_category ON articles (category);
CREATE INDEX IF NOT EXISTS idx_articles_status ON articles (status);
CREATE INDEX IF NOT EXISTS idx_articles_published_at ON articles (published_at);
CREATE INDEX IF NOT EXISTS idx_articles_views ON articles (views);
CREATE INDEX IF NOT EXISTS idx_articles_likes ON articles (likes);
CREATE INDEX IF NOT EXISTS idx_articles_slug ON articles (slug);

CREATE INDEX IF NOT EXISTS idx_article_views_article_id ON article_views (article_id);
CREATE INDEX IF NOT EXISTS idx_article_views_created_at ON article_views (created_at);

CREATE INDEX IF NOT EXISTS idx_search_logs_query ON search_logs (query);
CREATE INDEX IF NOT EXISTS idx_search_logs_created_at ON search_logs (created_at);

CREATE INDEX IF NOT EXISTS idx_favorites_session_id ON favorites (session_id);
CREATE INDEX IF NOT EXISTS idx_favorites_article_id ON favorites (article_id);

CREATE INDEX IF NOT EXISTS idx_comments_article_id ON comments (article_id);
CREATE INDEX IF NOT EXISTS idx_comments_status ON comments (status);

CREATE INDEX IF NOT EXISTS idx_research_papers_category ON research_papers (category);
CREATE INDEX IF NOT EXISTS idx_research_papers_publication_date ON research_papers (publication_date);

CREATE INDEX IF NOT EXISTS idx_healing_content_type ON healing_content (type);
CREATE INDEX IF NOT EXISTS idx_healing_content_difficulty ON healing_content (difficulty);

-- 創建全文搜索虛擬表（如果 D1 支持 FTS）
-- CREATE VIRTUAL TABLE IF NOT EXISTS articles_fts USING fts5(
--     title, content, excerpt, tags, 
--     content='articles', 
--     content_rowid='id'
-- );

-- 插入初始數據

-- 插入分類數據
INSERT OR IGNORE INTO categories (name, slug, description, icon, color) VALUES
('佛學智慧', 'dharma', '探索佛教教法和修行智慧', 'fas fa-dharmachakra', '#ff9a56'),
('身心療癒', 'healing', '身心靈療癒方法和實踐', 'fas fa-spa', '#4ecdc4'),
('最新研究', 'research', '學術研究和科學發現', 'fas fa-microscope', '#6c5ce7'),
('禪修指導', 'meditation', '冥想和禪修實用指南', 'fas fa-lotus-position', '#a29bfe'),
('佛教哲學', 'philosophy', '佛教思想和哲學探討', 'fas fa-brain', '#fd79a8'),
('修行方法', 'practice', '日常修行和實踐技巧', 'fas fa-hands-praying', '#00b894');

-- 插入標籤數據
INSERT OR IGNORE INTO tags (name, slug, color) VALUES
('慈悲修持', 'compassion-practice', '#e17055'),
('正念冥想', 'mindfulness-meditation', '#00b894'),
('藏傳佛教', 'tibetan-buddhism', '#fdcb6e'),
('身心療癒', 'mind-body-healing', '#6c5ce7'),
('禪修方法', 'meditation-methods', '#fd79a8'),
('佛教哲學', 'buddhist-philosophy', '#a29bfe'),
('壓力管理', 'stress-management', '#00cec9'),
('神經科學', 'neuroscience', '#0984e3'),
('四無量心', 'four-immeasurables', '#e84393'),
('菩提心', 'bodhicitta', '#f39c12'),
('空性教法', 'emptiness-teaching', '#9b59b6'),
('因果業力', 'karma-causation', '#e67e22'),
('輪迴生死', 'rebirth-death', '#34495e'),
('正念覺察', 'mindful-awareness', '#16a085'),
('呼吸練習', 'breathing-exercise', '#27ae60'),
('瑜伽療癒', 'yoga-healing', '#e74c3c'),
('音頻療癒', 'sound-healing', '#8e44ad'),
('能量平衡', 'energy-balance', '#f39c12'),
('情緒療癒', 'emotional-healing', '#e91e63'),
('冥想研究', 'meditation-research', '#3f51b5');

-- 插入網站配置
INSERT OR IGNORE INTO site_config (config_key, config_value, description) VALUES
('site_title', '澄源閱讀', '網站標題'),
('site_description', '探索佛學智慧，體驗身心療癒，開啟內在覺醒之旅', '網站描述'),
('site_keywords', '佛學,身心療癒,正念冥想,藏傳佛教,修行,禪修', '網站關鍵詞'),
('site_author', '澄源閱讀團隊', '網站作者'),
('contact_email', '<EMAIL>', '聯絡郵箱'),
('articles_per_page', '12', '每頁文章數'),
('comments_enabled', 'true', '是否啟用評論'),
('search_enabled', 'true', '是否啟用搜索'),
('analytics_enabled', 'true', '是否啟用統計'),
('cache_duration', '3600', '緩存持續時間（秒）');

-- 插入示例文章數據
INSERT OR IGNORE INTO articles (
    title, content, excerpt, category, author, tags, 
    reading_time, difficulty, slug
) VALUES 
(
    '藏傳佛教中的慈悲修持法門',
    '慈悲心是藏傳佛教修行的核心，也是成就菩薩道的根本。在這篇文章中，我們將深入探討慈悲心的本質、培養方法以及在日常生活中的實踐...',
    '慈悲心是藏傳佛教修行的核心，也是成就菩薩道的根本。本文將深入探討如何在日常生活中培養真正的慈悲心...',
    'dharma',
    '釋智慧',
    '["慈悲修持", "藏傳佛教", "四無量心", "修行方法"]',
    8,
    'beginner',
    'tibetan-compassion-practice'
),
(
    '正念冥想對現代人壓力緩解的應用',
    '在現代快節奏的生活中，壓力已成為影響身心健康的主要因素。正念冥想作為一種古老的修行方法，在現代心理學和醫學研究中得到了廣泛驗證...',
    '結合科學研究與傳統修行，探討正念冥想如何有效緩解現代生活中的各種壓力...',
    'healing',
    'Dr. 陳明',
    '["正念冥想", "壓力管理", "身心療癒", "現代應用"]',
    6,
    'intermediate',
    'mindfulness-stress-relief'
),
(
    '冥想對大腦神經可塑性的影響研究',
    '近年來，神經科學研究為古老的冥想修行提供了科學依據。本文將介紹最新的腦部影像學研究成果，探討長期冥想練習如何改變大腦結構...',
    '最新神經科學研究顯示，長期冥想練習能夠改變大腦結構，提升認知能力...',
    'research',
    'Dr. 王研究',
    '["神經科學", "冥想研究", "大腦研究", "認知能力"]',
    10,
    'advanced',
    'meditation-neuroplasticity-research'
);

-- 插入示例研究論文數據
INSERT OR IGNORE INTO research_papers (
    title, abstract, authors, institution, journal, publication_date,
    category, impact_factor, research_type, keywords
) VALUES 
(
    'Mindfulness-Based Stress Reduction Effects on Neural Networks',
    'This study examines the neuroplastic changes induced by an 8-week mindfulness-based stress reduction program...',
    '["Dr. Sarah Chen", "Dr. Michael Rodriguez"]',
    'Johns Hopkins University',
    'Nature Neuroscience',
    '2025-01-08',
    'neuroscience',
    8.5,
    'clinical_trial',
    '["mindfulness", "neuroplasticity", "stress", "fMRI"]'
),
(
    'Compassion Meditation and Inflammatory Markers: A Randomized Trial',
    'A randomized controlled trial investigating the effects of compassion meditation on inflammatory biomarkers...',
    '["Dr. Lisa Wong", "Dr. James Patterson"]',
    'University of California, San Francisco',
    'Psychological Science',
    '2025-01-05',
    'healthcare',
    9.2,
    'randomized_trial',
    '["compassion", "inflammation", "immune_system", "meditation"]'
);

-- 插入示例療癒內容數據
INSERT OR IGNORE INTO healing_content (
    title, description, type, difficulty, duration, instructions, benefits, tags
) VALUES 
(
    '基礎正念呼吸法',
    '學習基本的正念呼吸技巧，適合初學者建立正念冥想基礎',
    'breathing',
    'beginner',
    15,
    '1. 找到舒適的坐姿...',
    '["減輕壓力", "提高專注力", "改善睡眠"]',
    '["正念", "呼吸", "基礎練習"]'
),
(
    '慈心冥想引導',
    '培養慈愛心的引導式冥想練習，從自己開始擴展到所有眾生',
    'meditation',
    'intermediate',
    25,
    '1. 調整坐姿，閉上眼睛...',
    '["增強同理心", "改善人際關係", "內心平靜"]',
    '["慈心", "冥想", "慈悲"]'
),
(
    '脈輪平衡引導',
    '通過觀想和能量工作平衡七個主要脈輪',
    'energy',
    'advanced',
    45,
    '1. 躺下放鬆全身...',
    '["能量平衡", "身心和諧", "靈性成長"]',
    '["脈輪", "能量", "平衡"]'
);

-- 觸發器：更新文章的 updated_at 時間戳
CREATE TRIGGER IF NOT EXISTS update_articles_timestamp 
    AFTER UPDATE ON articles
    FOR EACH ROW 
    WHEN NEW.updated_at = OLD.updated_at
BEGIN
    UPDATE articles SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 觸發器：更新評論的 updated_at 時間戳
CREATE TRIGGER IF NOT EXISTS update_comments_timestamp 
    AFTER UPDATE ON comments
    FOR EACH ROW 
    WHEN NEW.updated_at = OLD.updated_at
BEGIN
    UPDATE comments SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 觸發器：更新標籤使用計數
CREATE TRIGGER IF NOT EXISTS increment_tag_usage 
    AFTER INSERT ON article_tags
BEGIN
    UPDATE tags SET usage_count = usage_count + 1 WHERE id = NEW.tag_id;
END;

CREATE TRIGGER IF NOT EXISTS decrement_tag_usage 
    AFTER DELETE ON article_tags
BEGIN
    UPDATE tags SET usage_count = usage_count - 1 WHERE id = OLD.tag_id;
END;