#!/bin/bash

# 澄源閱讀 - 部署腳本
# 用於自動化部署流程

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 默認環境
ENVIRONMENT=${1:-staging}

echo -e "${BLUE}🚀 開始部署澄源閱讀到 ${ENVIRONMENT} 環境...${NC}"

# 檢查環境參數
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    echo -e "${RED}❌ 無效的環境參數。請使用 'staging' 或 'production'${NC}"
    exit 1
fi

# 檢查必要工具
check_tools() {
    echo -e "${YELLOW}🔍 檢查必要工具...${NC}"
    
    if ! command -v wrangler &> /dev/null; then
        echo -e "${RED}❌ Wrangler CLI 未安裝${NC}"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm 未安裝${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 工具檢查通過${NC}"
}

# 建置前端資源
build_frontend() {
    echo -e "${YELLOW}📦 建置前端資源...${NC}"
    
    # 檢查並優化圖片
    if [ -d "assets/images" ]; then
        echo "   - 檢查圖片資源..."
        find assets/images -name "*.jpg" -o -name "*.png" -o -name "*.webp" | wc -l | xargs echo "     找到圖片文件："
    fi
    
    # 檢查 CSS 文件
    if [ -d "css" ]; then
        echo "   - 檢查 CSS 文件..."
        find css -name "*.css" | wc -l | xargs echo "     找到 CSS 文件："
    fi
    
    # 檢查 JavaScript 文件
    if [ -d "js" ]; then
        echo "   - 檢查 JavaScript 文件..."
        find js -name "*.js" | wc -l | xargs echo "     找到 JS 文件："
    fi
    
    echo -e "${GREEN}✅ 前端資源檢查完成${NC}"
}

# 部署數據庫更新
deploy_database() {
    echo -e "${YELLOW}🗄️  部署數據庫更新...${NC}"
    
    DB_NAME="chengyuan-reading-db"
    if [[ "$ENVIRONMENT" == "production" ]]; then
        DB_NAME="chengyuan-reading-db-prod"
    fi
    
    # 檢查是否有新的遷移文件
    if [ -f "database/migrations.sql" ]; then
        echo "   - 執行數據庫遷移..."
        wrangler d1 execute $DB_NAME --file=database/migrations.sql --env=$ENVIRONMENT
    fi
    
    echo -e "${GREEN}✅ 數據庫更新完成${NC}"
}

# 部署 Workers API
deploy_workers() {
    echo -e "${YELLOW}⚡ 部署 Workers API...${NC}"
    
    # 部署文章 API
    echo "   - 部署文章 API..."
    wrangler deploy functions/articles.js --env=$ENVIRONMENT --name=chengyuan-articles-$ENVIRONMENT
    
    # 部署搜索 API
    echo "   - 部署搜索 API..."
    wrangler deploy functions/search.js --env=$ENVIRONMENT --name=chengyuan-search-$ENVIRONMENT
    
    # 部署上傳 API
    echo "   - 部署上傳 API..."
    wrangler deploy functions/upload.js --env=$ENVIRONMENT --name=chengyuan-upload-$ENVIRONMENT
    
    echo -e "${GREEN}✅ Workers API 部署完成${NC}"
}

# 部署靜態網站
deploy_pages() {
    echo -e "${YELLOW}🌐 部署靜態網站...${NC}"
    
    PROJECT_NAME="chengyuan-reading"
    if [[ "$ENVIRONMENT" == "production" ]]; then
        PROJECT_NAME="chengyuan-reading-prod"
    fi
    
    # 部署到 Cloudflare Pages
    wrangler pages deploy . --project-name=$PROJECT_NAME --env=$ENVIRONMENT
    
    echo -e "${GREEN}✅ 靜態網站部署完成${NC}"
}

# 執行部署後測試
run_tests() {
    echo -e "${YELLOW}🧪 執行部署後測試...${NC}"
    
    BASE_URL="https://chengyuan-reading.pages.dev"
    if [[ "$ENVIRONMENT" == "production" ]]; then
        BASE_URL="https://chengyuan-reading.com"
    fi
    
    # 測試主頁
    echo "   - 測試主頁載入..."
    if curl -s -o /dev/null -w "%{http_code}" "$BASE_URL" | grep -q "200"; then
        echo -e "${GREEN}     ✅ 主頁測試通過${NC}"
    else
        echo -e "${RED}     ❌ 主頁測試失敗${NC}"
    fi
    
    # 測試 API 端點
    echo "   - 測試 API 端點..."
    API_URL="$BASE_URL/api/articles"
    if curl -s -o /dev/null -w "%{http_code}" "$API_URL" | grep -q "200"; then
        echo -e "${GREEN}     ✅ API 測試通過${NC}"
    else
        echo -e "${RED}     ❌ API 測試失敗${NC}"
    fi
    
    echo -e "${GREEN}✅ 測試完成${NC}"
}

# 生成部署報告
generate_report() {
    echo -e "${YELLOW}📊 生成部署報告...${NC}"
    
    REPORT_FILE="deployment-report-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > $REPORT_FILE << EOF
澄源閱讀部署報告
================

部署時間: $(date)
環境: $ENVIRONMENT
部署版本: $(git rev-parse --short HEAD 2>/dev/null || echo "未知")

部署內容:
- 前端靜態網站
- Workers API (文章、搜索、上傳)
- 數據庫更新
- 配置文件

部署 URL:
EOF

    if [[ "$ENVIRONMENT" == "production" ]]; then
        cat >> $REPORT_FILE << EOF
- 網站: https://chengyuan-reading.com
- API: https://api.chengyuan-reading.com
EOF
    else
        cat >> $REPORT_FILE << EOF
- 網站: https://chengyuan-reading.pages.dev
- API: https://chengyuan-reading.your-subdomain.workers.dev
EOF
    fi
    
    cat >> $REPORT_FILE << EOF

後續檢查:
- [ ] 驗證網站功能
- [ ] 檢查 API 響應
- [ ] 測試圖片上傳
- [ ] 驗證搜索功能
- [ ] 檢查數據同步

部署日誌:
可使用以下命令查看實時日誌:
wrangler tail chengyuan-articles-$ENVIRONMENT
wrangler tail chengyuan-search-$ENVIRONMENT  
wrangler tail chengyuan-upload-$ENVIRONMENT
EOF
    
    echo -e "${GREEN}✅ 部署報告已生成: $REPORT_FILE${NC}"
}

# 清理函數
cleanup() {
    echo -e "${YELLOW}🧹 清理臨時文件...${NC}"
    # 清理任何臨時文件
    rm -f *.tmp
    echo -e "${GREEN}✅ 清理完成${NC}"
}

# 主執行流程
main() {
    echo -e "${BLUE}澄源閱讀自動部署腳本${NC}"
    echo "環境: $ENVIRONMENT"
    echo "時間: $(date)"
    echo ""
    
    check_tools
    build_frontend
    deploy_database
    deploy_workers
    deploy_pages
    run_tests
    generate_report
    cleanup
    
    echo ""
    echo -e "${GREEN}🎉 部署完成！${NC}"
    echo ""
    echo "🔗 快速鏈接："
    if [[ "$ENVIRONMENT" == "production" ]]; then
        echo "   網站: https://chengyuan-reading.com"
        echo "   管理: https://dash.cloudflare.com"
    else
        echo "   網站: https://chengyuan-reading.pages.dev"
        echo "   管理: https://dash.cloudflare.com"
    fi
    echo ""
    echo "📋 下一步："
    echo "   1. 驗證網站功能"
    echo "   2. 檢查分析數據"
    echo "   3. 監控性能指標"
    echo "   4. 備份數據"
}

# 處理中斷信號
trap cleanup EXIT

# 執行主流程
main "$@"