# Cloudflare Workers 配置文件
# 澄源閱讀項目

name = "chengyuan-reading"
main = "functions/index.js"
compatibility_date = "2024-12-01"

# 環境變量
[env.production]
name = "chengyuan-reading-prod"

[env.staging]
name = "chengyuan-reading-staging"

[env.development]
name = "chengyuan-reading-dev"

# D1 數據庫綁定
[[d1_databases]]
binding = "DB"
database_name = "chengyuan-reading-db"
database_id = "your-d1-database-id"

# R2 存儲桶綁定
[[r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "chengyuan-images"

# KV 存儲綁定（用於緩存）
[[kv_namespaces]]
binding = "CACHE"
id = "your-kv-namespace-id"
preview_id = "your-kv-preview-id"

# 環境變量
[vars]
ENVIRONMENT = "development"
API_VERSION = "v1"
R2_PUBLIC_DOMAIN = "images.chengyuan-reading.com"
SITE_URL = "https://chengyuan-reading.pages.dev"
CONTACT_EMAIL = "<EMAIL>"
MAX_UPLOAD_SIZE = "5242880"  # 5MB in bytes
CACHE_TTL = "3600"  # 1 hour
SEARCH_RESULTS_LIMIT = "50"
ARTICLES_PER_PAGE = "12"

# 生產環境變量
[env.production.vars]
ENVIRONMENT = "production"
SITE_URL = "https://chengyuan-reading.com"
R2_PUBLIC_DOMAIN = "images.chengyuan-reading.com"
CACHE_TTL = "7200"  # 2 hours

# 測試環境變量
[env.staging.vars]
ENVIRONMENT = "staging"
SITE_URL = "https://staging.chengyuan-reading.com"
R2_PUBLIC_DOMAIN = "staging-images.chengyuan-reading.com"

# Workers 設置
[build]
command = ""

# 自定義域名（生產環境）
[[env.production.routes]]
pattern = "api.chengyuan-reading.com/*"
zone_name = "chengyuan-reading.com"

# 自定義域名（測試環境）
[[env.staging.routes]]
pattern = "api-staging.chengyuan-reading.com/*"
zone_name = "chengyuan-reading.com"

# 觸發器設置
[[triggers]]
crons = ["0 2 * * *"]  # 每天凌晨2點執行清理任務