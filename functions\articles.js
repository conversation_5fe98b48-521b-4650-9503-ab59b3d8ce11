/**
 * 澄源閱讀 - 文章管理 API
 * Cloudflare Workers 函數
 * 
 * 功能：
 * - 獲取文章列表（分頁、篩選、排序）
 * - 獲取文章詳情
 * - 按分類獲取文章
 * - 獲取熱門文章
 * - 文章點讚/收藏
 * - 記錄文章瀏覽
 */

// CORS 處理
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Max-Age': '86400',
};

// 處理 CORS 預檢請求
function handleCORS(request) {
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }
}

// 錯誤處理
function createErrorResponse(message, status = 400) {
  return new Response(JSON.stringify({
    success: false,
    error: message,
    timestamp: new Date().toISOString()
  }), {
    status,
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders
    }
  });
}

// 成功響應
function createSuccessResponse(data, meta = {}) {
  return new Response(JSON.stringify({
    success: true,
    data,
    meta,
    timestamp: new Date().toISOString()
  }), {
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders
    }
  });
}

// 獲取客戶端IP
function getClientIP(request) {
  return request.headers.get('CF-Connecting-IP') || 
         request.headers.get('X-Forwarded-For') || 
         '127.0.0.1';
}

// 獲取會話ID（從請求頭或生成新的）
function getSessionId(request) {
  return request.headers.get('X-Session-ID') || 
         crypto.randomUUID();
}

// 主處理函數
export default {
  async fetch(request, env, ctx) {
    // 處理 CORS
    const corsResponse = handleCORS(request);
    if (corsResponse) return corsResponse;

    try {
      const url = new URL(request.url);
      const path = url.pathname;
      const method = request.method;

      // 路由處理
      if (path === '/api/articles' && method === 'GET') {
        return await getArticles(request, env);
      }
      
      if (path.match(/^\/api\/articles\/\d+$/) && method === 'GET') {
        const articleId = path.split('/').pop();
        return await getArticleById(request, env, articleId);
      }
      
      if (path.match(/^\/api\/articles\/category\/\w+$/) && method === 'GET') {
        const category = path.split('/').pop();
        return await getArticlesByCategory(request, env, category);
      }
      
      if (path === '/api/articles/featured' && method === 'GET') {
        return await getFeaturedArticles(request, env);
      }
      
      if (path === '/api/articles/popular' && method === 'GET') {
        return await getPopularArticles(request, env);
      }
      
      if (path.match(/^\/api\/articles\/\d+\/like$/) && method === 'POST') {
        const articleId = path.split('/')[3];
        return await likeArticle(request, env, articleId);
      }
      
      if (path.match(/^\/api\/articles\/\d+\/view$/) && method === 'POST') {
        const articleId = path.split('/')[3];
        return await recordArticleView(request, env, articleId);
      }
      
      if (path === '/api/categories' && method === 'GET') {
        return await getCategories(request, env);
      }
      
      if (path === '/api/tags' && method === 'GET') {
        return await getTags(request, env);
      }

      return createErrorResponse('API endpoint not found', 404);

    } catch (error) {
      console.error('API Error:', error);
      return createErrorResponse('Internal server error', 500);
    }
  }
};

// 獲取文章列表
async function getArticles(request, env) {
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page')) || 1;
  const limit = Math.min(parseInt(url.searchParams.get('limit')) || 12, 50);
  const category = url.searchParams.get('category');
  const tag = url.searchParams.get('tag');
  const sort = url.searchParams.get('sort') || 'published_at';
  const order = url.searchParams.get('order') || 'DESC';
  const status = url.searchParams.get('status') || 'published';
  
  const offset = (page - 1) * limit;

  try {
    // 構建查詢條件
    let whereConditions = ['status = ?'];
    let params = [status];

    if (category) {
      whereConditions.push('category = ?');
      params.push(category);
    }

    if (tag) {
      whereConditions.push('id IN (SELECT article_id FROM article_tags at JOIN tags t ON at.tag_id = t.id WHERE t.slug = ?)');
      params.push(tag);
    }

    const whereClause = whereConditions.join(' AND ');

    // 獲取總數
    const countQuery = `SELECT COUNT(*) as total FROM articles WHERE ${whereClause}`;
    const countResult = await env.DB.prepare(countQuery).bind(...params).first();
    const total = countResult.total;

    // 獲取文章列表
    const articlesQuery = `
      SELECT 
        id, title, excerpt, category, author, author_avatar,
        published_at, views, likes, reading_time, difficulty,
        featured_image, featured_image_alt, tags, slug
      FROM articles 
      WHERE ${whereClause}
      ORDER BY ${sort} ${order}
      LIMIT ? OFFSET ?
    `;

    const articles = await env.DB.prepare(articlesQuery)
      .bind(...params, limit, offset)
      .all();

    // 處理標籤JSON
    const processedArticles = articles.results.map(article => ({
      ...article,
      tags: article.tags ? JSON.parse(article.tags) : [],
      published_at: new Date(article.published_at).toISOString(),
      category_name: getCategoryName(article.category)
    }));

    const totalPages = Math.ceil(total / limit);

    return createSuccessResponse(processedArticles, {
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Get articles error:', error);
    return createErrorResponse('Failed to fetch articles');
  }
}

// 獲取文章詳情
async function getArticleById(request, env, articleId) {
  try {
    const article = await env.DB.prepare(`
      SELECT 
        id, title, content, excerpt, category, author, author_avatar,
        created_at, published_at, updated_at, views, likes, reading_time,
        difficulty, featured_image, featured_image_alt, tags, slug,
        seo_title, seo_description
      FROM articles 
      WHERE id = ? AND status = 'published'
    `).bind(articleId).first();

    if (!article) {
      return createErrorResponse('Article not found', 404);
    }

    // 獲取相關文章
    const relatedArticles = await env.DB.prepare(`
      SELECT id, title, excerpt, category, author, published_at, featured_image
      FROM articles 
      WHERE category = ? AND id != ? AND status = 'published'
      ORDER BY published_at DESC
      LIMIT 3
    `).bind(article.category, articleId).all();

    // 處理數據
    const processedArticle = {
      ...article,
      tags: article.tags ? JSON.parse(article.tags) : [],
      created_at: new Date(article.created_at).toISOString(),
      published_at: new Date(article.published_at).toISOString(),
      updated_at: new Date(article.updated_at).toISOString(),
      category_name: getCategoryName(article.category),
      related_articles: relatedArticles.results.map(related => ({
        ...related,
        published_at: new Date(related.published_at).toISOString(),
        category_name: getCategoryName(related.category)
      }))
    };

    return createSuccessResponse(processedArticle);

  } catch (error) {
    console.error('Get article by ID error:', error);
    return createErrorResponse('Failed to fetch article');
  }
}

// 按分類獲取文章
async function getArticlesByCategory(request, env, category) {
  const url = new URL(request.url);
  const limit = Math.min(parseInt(url.searchParams.get('limit')) || 6, 20);
  
  try {
    const articles = await env.DB.prepare(`
      SELECT 
        id, title, excerpt, author, published_at, views, likes,
        reading_time, featured_image, tags, slug
      FROM articles 
      WHERE category = ? AND status = 'published'
      ORDER BY published_at DESC
      LIMIT ?
    `).bind(category, limit).all();

    const processedArticles = articles.results.map(article => ({
      ...article,
      tags: article.tags ? JSON.parse(article.tags) : [],
      published_at: new Date(article.published_at).toISOString(),
      category_name: getCategoryName(category)
    }));

    return createSuccessResponse(processedArticles);

  } catch (error) {
    console.error('Get articles by category error:', error);
    return createErrorResponse('Failed to fetch articles by category');
  }
}

// 獲取精選文章
async function getFeaturedArticles(request, env) {
  const url = new URL(request.url);
  const limit = Math.min(parseInt(url.searchParams.get('limit')) || 6, 12);
  
  try {
    const articles = await env.DB.prepare(`
      SELECT 
        id, title, excerpt, category, author, published_at, 
        views, likes, reading_time, featured_image, tags, slug
      FROM articles 
      WHERE status = 'published'
      ORDER BY (likes * 0.7 + views * 0.3) DESC, published_at DESC
      LIMIT ?
    `).bind(limit).all();

    const processedArticles = articles.results.map(article => ({
      ...article,
      tags: article.tags ? JSON.parse(article.tags) : [],
      published_at: new Date(article.published_at).toISOString(),
      category_name: getCategoryName(article.category)
    }));

    return createSuccessResponse(processedArticles);

  } catch (error) {
    console.error('Get featured articles error:', error);
    return createErrorResponse('Failed to fetch featured articles');
  }
}

// 獲取熱門文章
async function getPopularArticles(request, env) {
  const url = new URL(request.url);
  const limit = Math.min(parseInt(url.searchParams.get('limit')) || 10, 20);
  const timeRange = url.searchParams.get('timeRange') || '7'; // 天數
  
  try {
    const articles = await env.DB.prepare(`
      SELECT 
        a.id, a.title, a.excerpt, a.category, a.author, a.published_at,
        a.views, a.likes, a.reading_time, a.featured_image, a.tags, a.slug,
        COUNT(av.id) as recent_views
      FROM articles a
      LEFT JOIN article_views av ON a.id = av.article_id 
        AND av.created_at > datetime('now', '-${timeRange} days')
      WHERE a.status = 'published'
      GROUP BY a.id
      ORDER BY recent_views DESC, a.views DESC
      LIMIT ?
    `).bind(limit).all();

    const processedArticles = articles.results.map(article => ({
      ...article,
      tags: article.tags ? JSON.parse(article.tags) : [],
      published_at: new Date(article.published_at).toISOString(),
      category_name: getCategoryName(article.category)
    }));

    return createSuccessResponse(processedArticles);

  } catch (error) {
    console.error('Get popular articles error:', error);
    return createErrorResponse('Failed to fetch popular articles');
  }
}

// 文章點讚
async function likeArticle(request, env, articleId) {
  const sessionId = getSessionId(request);
  const clientIP = getClientIP(request);
  
  try {
    // 檢查是否已經點讚
    const existingLike = await env.DB.prepare(`
      SELECT id FROM favorites 
      WHERE article_id = ? AND session_id = ?
    `).bind(articleId, sessionId).first();

    if (existingLike) {
      // 取消點讚
      await env.DB.prepare(`
        DELETE FROM favorites 
        WHERE article_id = ? AND session_id = ?
      `).bind(articleId, sessionId).run();

      await env.DB.prepare(`
        UPDATE articles 
        SET likes = likes - 1 
        WHERE id = ?
      `).bind(articleId).run();

      return createSuccessResponse({ liked: false, action: 'unliked' });
    } else {
      // 添加點讚
      await env.DB.prepare(`
        INSERT INTO favorites (article_id, session_id, ip_address, user_agent)
        VALUES (?, ?, ?, ?)
      `).bind(
        articleId, 
        sessionId, 
        clientIP, 
        request.headers.get('User-Agent') || ''
      ).run();

      await env.DB.prepare(`
        UPDATE articles 
        SET likes = likes + 1 
        WHERE id = ?
      `).bind(articleId).run();

      return createSuccessResponse({ liked: true, action: 'liked' });
    }

  } catch (error) {
    console.error('Like article error:', error);
    return createErrorResponse('Failed to process like');
  }
}

// 記錄文章瀏覽
async function recordArticleView(request, env, articleId) {
  const sessionId = getSessionId(request);
  const clientIP = getClientIP(request);
  
  try {
    const body = await request.json().catch(() => ({}));
    const viewDuration = body.duration || 0;
    const referrer = body.referrer || request.headers.get('Referer') || '';

    // 檢查是否在短時間內重複瀏覽（防止刷量）
    const recentView = await env.DB.prepare(`
      SELECT id FROM article_views 
      WHERE article_id = ? AND session_id = ? 
        AND created_at > datetime('now', '-1 hour')
    `).bind(articleId, sessionId).first();

    if (!recentView) {
      // 記錄瀏覽
      await env.DB.prepare(`
        INSERT INTO article_views 
        (article_id, session_id, ip_address, user_agent, referrer, view_duration)
        VALUES (?, ?, ?, ?, ?, ?)
      `).bind(
        articleId,
        sessionId,
        clientIP,
        request.headers.get('User-Agent') || '',
        referrer,
        viewDuration
      ).run();

      // 更新文章瀏覽數
      await env.DB.prepare(`
        UPDATE articles 
        SET views = views + 1 
        WHERE id = ?
      `).bind(articleId).run();
    }

    return createSuccessResponse({ recorded: !recentView });

  } catch (error) {
    console.error('Record article view error:', error);
    return createErrorResponse('Failed to record view');
  }
}

// 獲取分類列表
async function getCategories(request, env) {
  try {
    const categories = await env.DB.prepare(`
      SELECT 
        c.id, c.name, c.slug, c.description, c.icon, c.color,
        COUNT(a.id) as article_count
      FROM categories c
      LEFT JOIN articles a ON c.slug = a.category AND a.status = 'published'
      GROUP BY c.id
      ORDER BY c.sort_order, c.name
    `).all();

    return createSuccessResponse(categories.results);

  } catch (error) {
    console.error('Get categories error:', error);
    return createErrorResponse('Failed to fetch categories');
  }
}

// 獲取標籤列表
async function getTags(request, env) {
  const url = new URL(request.url);
  const limit = Math.min(parseInt(url.searchParams.get('limit')) || 20, 50);
  
  try {
    const tags = await env.DB.prepare(`
      SELECT id, name, slug, color, usage_count
      FROM tags
      WHERE usage_count > 0
      ORDER BY usage_count DESC
      LIMIT ?
    `).bind(limit).all();

    return createSuccessResponse(tags.results);

  } catch (error) {
    console.error('Get tags error:', error);
    return createErrorResponse('Failed to fetch tags');
  }
}

// 輔助函數：獲取分類名稱
function getCategoryName(categorySlug) {
  const categoryNames = {
    'dharma': '佛學智慧',
    'healing': '身心療癒',
    'research': '最新研究',
    'meditation': '禪修指導',
    'philosophy': '佛教哲學',
    'practice': '修行方法'
  };
  return categoryNames[categorySlug] || categorySlug;
}