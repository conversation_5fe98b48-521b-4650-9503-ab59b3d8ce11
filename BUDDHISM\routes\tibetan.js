const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const authenticate = require('../middleware/auth');

// 驗證錯誤處理中間件
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// 模擬藏傳佛教修持數據
const tibetanPractices = {
  teachings: [
    {
      id: '1',
      title: '四聖諦教學',
      teacher: '洛桑仁波切',
      category: 'fundamental',
      difficulty: 'beginner',
      duration: 45,
      description: '深入講解佛教四聖諦的核心教義',
      audioUrl: '/audio/four-noble-truths.mp3',
      videoUrl: '/video/four-noble-truths.mp4',
      transcript: '四聖諦是佛教的根本教義...',
      keywords: ['四聖諦', '苦集滅道', '基礎教學'],
      prerequisite: null,
      views: 1250,
      rating: 4.8
    },
    {
      id: '2',
      title: '大手印禪修指導',
      teacher: '竹巴法王',
      category: 'meditation',
      difficulty: 'advanced',
      duration: 90,
      description: '傳統大手印禪修的完整指導',
      audioUrl: '/audio/mahamudra.mp3',
      videoUrl: '/video/mahamudra.mp4',
      transcript: '大手印是藏傳佛教最高深的禪修法門...',
      keywords: ['大手印', '禪修', '高深修持'],
      prerequisite: 'basic_meditation',
      views: 580,
      rating: 4.9
    }
  ],
  practices: [
    {
      id: '1',
      name: '綠度母修持法',
      category: 'deity_yoga',
      difficulty: 'intermediate',
      steps: [
        '調身調息',
        '觀想綠度母聖像',
        '持誦度母心咒',
        '迴向功德'
      ],
      duration: 30,
      benefits: ['消除障礙', '增長智慧', '獲得加持'],
      mantra: 'ཨོཾ་ཏཱ་རེ་ཏུཏྟཱ་རེ་ཏུ་རེ་སྭཱ་ཧཱ།',
      mantraTransliteration: 'Om Tare Tuttare Ture Soha',
      instructions: '首先端坐，調整呼吸，然後開始觀想...',
      mudra: '蓮花手印',
      visualization: '觀想綠度母慈悲莊嚴之相...'
    }
  ],
  mantras: [
    {
      id: '1',
      name: '六字大明咒',
      tibetan: 'ཨོཾ་མ་ཎི་པདྨེ་ཧཱུྃ།',
      transliteration: 'Om Mani Padme Hum',
      meaning: '蓮花中的摩尼寶',
      benefits: ['淨化業障', '增長慈悲', '開發智慧'],
      audio: '/audio/om-mani-padme-hum.mp3',
      recitationCount: 108,
      category: 'compassion'
    }
  ]
};

// 獲取藏傳佛教教學列表
router.get('/teachings', async (req, res) => {
  try {
    const {
      category,
      difficulty,
      teacher,
      page = 1,
      limit = 20,
      sort = 'newest'
    } = req.query;

    let teachings = [...tibetanPractices.teachings];

    // 應用篩選
    if (category) teachings = teachings.filter(t => t.category === category);
    if (difficulty) teachings = teachings.filter(t => t.difficulty === difficulty);
    if (teacher) teachings = teachings.filter(t => t.teacher.includes(teacher));

    // 排序
    const sortOptions = {
      'newest': (a, b) => parseInt(b.id) - parseInt(a.id),
      'popular': (a, b) => b.views - a.views,
      'rating': (a, b) => b.rating - a.rating,
      'duration': (a, b) => a.duration - b.duration
    };

    if (sortOptions[sort]) {
      teachings.sort(sortOptions[sort]);
    }

    // 分頁
    const skip = (page - 1) * limit;
    const paginatedTeachings = teachings.slice(skip, skip + parseInt(limit));

    res.json({
      message: 'Tibetan teachings retrieved successfully',
      teachings: paginatedTeachings,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: teachings.length,
        pages: Math.ceil(teachings.length / limit)
      },
      categories: ['fundamental', 'meditation', 'philosophy', 'ritual'],
      difficulties: ['beginner', 'intermediate', 'advanced'],
      teachers: [...new Set(tibetanPractices.teachings.map(t => t.teacher))]
    });
  } catch (error) {
    console.error('Get teachings error:', error);
    res.status(500).json({ error: 'Failed to fetch teachings' });
  }
});

// 獲取特定教學詳情
router.get('/teachings/:id', async (req, res) => {
  try {
    const teachingId = req.params.id;
    const teaching = tibetanPractices.teachings.find(t => t.id === teachingId);

    if (!teaching) {
      return res.status(404).json({ error: 'Teaching not found' });
    }

    // 增加觀看次數
    teaching.views += 1;

    // 獲取相關教學
    const relatedTeachings = tibetanPractices.teachings
      .filter(t => t.id !== teachingId)
      .filter(t => t.category === teaching.category || t.teacher === teaching.teacher)
      .slice(0, 3);

    res.json({
      message: 'Teaching details retrieved successfully',
      teaching,
      relatedTeachings
    });
  } catch (error) {
    console.error('Get teaching details error:', error);
    res.status(500).json({ error: 'Failed to fetch teaching details' });
  }
});

// 獲取修持法門列表
router.get('/practices', async (req, res) => {
  try {
    const { category, difficulty } = req.query;
    let practices = [...tibetanPractices.practices];

    if (category) practices = practices.filter(p => p.category === category);
    if (difficulty) practices = practices.filter(p => p.difficulty === difficulty);

    res.json({
      message: 'Tibetan practices retrieved successfully',
      practices,
      categories: ['deity_yoga', 'breathing', 'visualization', 'mantra'],
      difficulties: ['beginner', 'intermediate', 'advanced']
    });
  } catch (error) {
    console.error('Get practices error:', error);
    res.status(500).json({ error: 'Failed to fetch practices' });
  }
});

// 獲取咒語列表
router.get('/mantras', async (req, res) => {
  try {
    const { category } = req.query;
    let mantras = [...tibetanPractices.mantras];

    if (category) mantras = mantras.filter(m => m.category === category);

    res.json({
      message: 'Mantras retrieved successfully',
      mantras,
      categories: ['compassion', 'wisdom', 'protection', 'healing']
    });
  } catch (error) {
    console.error('Get mantras error:', error);
    res.status(500).json({ error: 'Failed to fetch mantras' });
  }
});

// 記錄修持進度
router.post('/practice-log', authenticate, [
  body('practiceId').notEmpty().withMessage('Practice ID is required'),
  body('duration').isInt({ min: 1 }).withMessage('Duration must be positive'),
  body('date').isISO8601().withMessage('Valid date required'),
  body('notes').optional().isLength({ max: 500 }).withMessage('Notes too long')
], handleValidationErrors, async (req, res) => {
  try {
    const { practiceId, duration, date, notes, difficulty } = req.body;
    const userId = req.user._id;

    // 記錄修持（實際應存到數據庫）
    const logEntry = {
      id: Date.now().toString(),
      userId,
      practiceId,
      duration,
      date: new Date(date),
      notes,
      difficulty,
      createdAt: new Date()
    };

    console.log('Practice log entry:', logEntry);

    res.json({
      message: 'Practice logged successfully',
      logEntry: {
        id: logEntry.id,
        practiceId: logEntry.practiceId,
        duration: logEntry.duration,
        date: logEntry.date,
        notes: logEntry.notes
      }
    });
  } catch (error) {
    console.error('Log practice error:', error);
    res.status(500).json({ error: 'Failed to log practice' });
  }
});

// 獲取修持統計
router.get('/practice-stats', authenticate, async (req, res) => {
  try {
    const userId = req.user._id;
    
    // 模擬統計數據
    const stats = {
      totalSessions: 45,
      totalDuration: 1350, // 分鐘
      streak: 7, // 連續天數
      favoriteCategory: 'meditation',
      monthlyProgress: [
        { month: '2023-10', sessions: 12, duration: 360 },
        { month: '2023-11', sessions: 18, duration: 540 },
        { month: '2023-12', sessions: 15, duration: 450 }
      ],
      achievements: [
        { name: '初心者', description: '完成首次修持', earnedAt: '2023-10-01' },
        { name: '持之以恆', description: '連續修持7天', earnedAt: '2023-12-15' }
      ]
    };

    res.json({
      message: 'Practice statistics retrieved successfully',
      statistics: stats
    });
  } catch (error) {
    console.error('Get practice stats error:', error);
    res.status(500).json({ error: 'Failed to fetch practice statistics' });
  }
});

module.exports = router;