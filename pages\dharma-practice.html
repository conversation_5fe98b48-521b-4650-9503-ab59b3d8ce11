<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修行方法 - 佛學智慧</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.6;
            color: #333;
            background: transparent;
            padding: 20px;
        }

        /* 分類介紹區 */
        .category-intro {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
            padding: 40px 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .category-intro::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 30px 30px;
            animation: float 20s linear infinite;
        }

        @keyframes float {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .intro-content {
            position: relative;
            z-index: 2;
        }

        .intro-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            animation: breathe 3s infinite;
        }

        @keyframes breathe {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .intro-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .intro-description {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.8;
        }

        /* 修行方法類別 */
        .practice-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .category-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border-left: 4px solid transparent;
        }

        .category-card.sutra-study {
            border-left-color: #ff6b6b;
        }

        .category-card.pure-land {
            border-left-color: #4ecdc4;
        }

        .category-card.eightfold-path {
            border-left-color: #45b7d1;
        }

        .category-card.loving-kindness {
            border-left-color: #f9ca24;
        }

        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .category-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .category-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .category-card.sutra-study .category-icon {
            background: #ff6b6b;
        }

        .category-card.pure-land .category-icon {
            background: #4ecdc4;
        }

        .category-card.eightfold-path .category-icon {
            background: #45b7d1;
        }

        .category-card.loving-kindness .category-icon {
            background: #f9ca24;
        }

        .category-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
        }

        .category-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .category-count {
            font-size: 0.9rem;
            color: #999;
        }

        /* 精選文章 */
        .featured-articles {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .articles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .article-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid #e9ecef;
        }

        .article-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-color: #f093fb;
        }

        .article-icon {
            color: #f093fb;
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .article-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .article-excerpt {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .article-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: #999;
        }

        /* 修行階梯 */
        .practice-pathway {
            background: linear-gradient(135deg, #ffeaa7, #fab1a0);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid #fdcb6e;
        }

        .pathway-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .pathway-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
        }

        .pathway-number {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .pathway-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin: 15px 0 10px;
        }

        .pathway-description {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            .category-intro {
                padding: 30px 20px;
            }

            .intro-title {
                font-size: 1.5rem;
            }

            .practice-categories,
            .articles-grid,
            .pathway-steps {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 分類介紹 -->
    <div class="category-intro">
        <div class="intro-content">
            <div class="intro-icon">
                <i class="fas fa-hands-praying"></i>
            </div>
            <h1 class="intro-title">修行方法</h1>
            <p class="intro-description">
                佛教修行有多種法門，各適合不同根器的修行者。本頁面涵蓋各種修行方法，
                從念佛法門到八正道修持，幫助您找到適合自己的修行道路。
            </p>
        </div>
    </div>

    <!-- 修行方法類別 -->
    <div class="practice-categories">
        <div class="category-card sutra-study" data-category="sutra-study">
            <div class="category-header">
                <div class="category-icon">
                    <i class="fas fa-book-open"></i>
                </div>
                <h3 class="category-title">經典研習</h3>
            </div>
            <p class="category-description">
                深入研讀佛教經典，透過經文的學習和理解，培養智慧和正見。
            </p>
            <div class="category-count">2 篇相關文章</div>
        </div>

        <div class="category-card pure-land" data-category="pure-land">
            <div class="category-header">
                <div class="category-icon">
                    <i class="fas fa-pray"></i>
                </div>
                <h3 class="category-title">念佛法門</h3>
            </div>
            <p class="category-description">
                專注於阿彌陀佛的修持方法，透過念佛培養專注力和信心。
            </p>
            <div class="category-count">1 篇相關文章</div>
        </div>

        <div class="category-card eightfold-path" data-category="eightfold-path">
            <div class="category-header">
                <div class="category-icon">
                    <i class="fas fa-dharmachakra"></i>
                </div>
                <h3 class="category-title">八正道</h3>
            </div>
            <p class="category-description">
                佛陀教導的解脫之路，包含正見、正思惟等八個修行要素。
            </p>
            <div class="category-count">1 篇相關文章</div>
        </div>

        <div class="category-card loving-kindness" data-category="loving-kindness">
            <div class="category-header">
                <div class="category-icon">
                    <i class="fas fa-heart"></i>
                </div>
                <h3 class="category-title">慈悲修持</h3>
            </div>
            <p class="category-description">
                培養慈悲心的修行方法，包括四無量心的觀修練習。
            </p>
            <div class="category-count">1 篇相關文章</div>
        </div>
    </div>

    <!-- 精選文章 -->
    <div class="featured-articles">
        <h2 class="section-title">
            <i class="fas fa-star"></i>
            修行方法精選
        </h2>
        <div class="articles-grid" id="practice-articles">
            <!-- 文章將通過 JavaScript 動態生成 -->
        </div>
    </div>

    <!-- 修行階梯 -->
    <div class="practice-pathway">
        <h2 class="section-title">
            <i class="fas fa-route"></i>
            修行進階之路
        </h2>
        <div class="pathway-steps">
            <div class="pathway-card">
                <div class="pathway-number">1</div>
                <h3 class="pathway-title">建立正見</h3>
                <p class="pathway-description">
                    透過學習佛教基本教義，建立正確的世界觀和人生觀。
                </p>
            </div>
            <div class="pathway-card">
                <div class="pathway-number">2</div>
                <h3 class="pathway-title">持戒修行</h3>
                <p class="pathway-description">
                    遵守佛教戒律，培養良好的道德品格和行為習慣。
                </p>
            </div>
            <div class="pathway-card">
                <div class="pathway-number">3</div>
                <h3 class="pathway-title">禪定修持</h3>
                <p class="pathway-description">
                    透過各種禪修方法，培養專注力和內心的平靜。
                </p>
            </div>
            <div class="pathway-card">
                <div class="pathway-number">4</div>
                <h3 class="pathway-title">智慧開展</h3>
                <p class="pathway-description">
                    在定力基礎上，觀察諸法實相，開發解脫的智慧。
                </p>
            </div>
        </div>
    </div>

    <script>
        // 修行方法相關文章數據
        const practiceArticles = [
            {
                id: 16,
                title: "念佛法門：淨土修行指南",
                excerpt: "念佛法門是專注於阿彌陀佛的修持方法，適合各種根器的修行者。本文詳細介紹了念佛的方法、心態調整，以及如何在日常生活中持續念佛修行。",
                difficulty: "beginner",
                date: "2024-12-18",
                views: 1923,
                likes: 156,
                icon: "fas fa-pray"
            },
            {
                id: 17,
                title: "八正道：解脫的完整之路",
                excerpt: "八正道是佛陀教導的完整修行體系，包含正見、正思惟、正語、正業、正命、正精進、正念、正定。本文系統性地介紹每一道的修持要點。",
                difficulty: "intermediate",
                date: "2024-12-15",
                views: 2187,
                likes: 203,
                icon: "fas fa-dharmachakra"
            },
            {
                id: 20,
                title: "慈悲喜捨四無量心修持",
                excerpt: "四無量心是大乘佛教的重要修行法門，透過觀修慈悲喜捨，培養廣大的菩提心。文章詳細指導如何逐步修持四無量心的觀想方法。",
                difficulty: "intermediate",
                date: "2024-12-10",
                views: 1654,
                likes: 134,
                icon: "fas fa-heart"
            },
            {
                id: 8,
                title: "《心經》的般若智慧解讀",
                excerpt: "《心經》僅260字，卻濃縮了般若波羅蜜多的精華。本文逐句解讀心經的深層含義，探討色即是空、空即是色的般若智慧，以及如何在修行中體悟空性。",
                difficulty: "advanced",
                date: "2024-12-28",
                views: 3021,
                likes: 287,
                icon: "fas fa-scroll"
            },
            {
                id: 15,
                title: "《金剛經》的無住生心智慧",
                excerpt: "《金剛經》教導「應無所住而生其心」的甚深智慧，是般若經典的代表作。文章深入解析金剛經的核心思想，指導如何在修行中實踐無住生心。",
                difficulty: "advanced",
                date: "2024-12-20",
                views: 2543,
                likes: 234,
                icon: "fas fa-gem"
            }
        ];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderArticles();
            initializeNavigation();
        });

        // 渲染文章
        function renderArticles() {
            const container = document.getElementById('practice-articles');
            
            container.innerHTML = practiceArticles.map(article => `
                <div class="article-card" data-article-id="${article.id}">
                    <div class="article-icon">
                        <i class="${article.icon}"></i>
                    </div>
                    <h3 class="article-title">${article.title}</h3>
                    <p class="article-excerpt">${article.excerpt}</p>
                    <div class="article-meta">
                        <span>${article.difficulty === 'beginner' ? '入門' : article.difficulty === 'intermediate' ? '進階' : '高級'}</span>
                        <span>${article.date}</span>
                    </div>
                </div>
            `).join('');
        }

        // 初始化導航
        function initializeNavigation() {
            // 類別卡片點擊
            document.querySelectorAll('.category-card').forEach(card => {
                card.addEventListener('click', function() {
                    const category = this.getAttribute('data-category');
                    
                    if (window.parent !== window) {
                        window.parent.postMessage({
                            type: 'filter',
                            filterType: 'practice-method',
                            value: category
                        }, '*');
                    }
                });
            });

            // 文章點擊
            document.addEventListener('click', function(e) {
                const articleElement = e.target.closest('[data-article-id]');
                if (articleElement) {
                    const articleId = articleElement.getAttribute('data-article-id');
                    
                    if (window.parent !== window) {
                        window.parent.postMessage({
                            type: 'article_view',
                            articleId: articleId
                        }, '*');
                    }
                }
            });
        }
    </script>
</body>
</html>