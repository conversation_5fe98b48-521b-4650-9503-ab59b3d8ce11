<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>關於我們 - 佛教身心療癒網站</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2C3E50;
            --secondary-color: #F39C12;
            --accent-color: #E8F4F8;
            --text-color: #2C3E50;
            --light-bg: #F8F9FA;
        }

        body {
            font-family: 'Noto Sans CJK TC', 'Microsoft JhengHei', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            padding: 120px 0 80px;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            opacity: 0.3;
        }

        .page-header-content {
            position: relative;
            z-index: 2;
        }

        .navbar-custom {
            background: rgba(44, 62, 80, 0.95);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .navbar-custom.scrolled {
            background: var(--primary-color);
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
        }

        .nav-link {
            color: white !important;
            font-weight: 500;
            margin: 0 10px;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--secondary-color) !important;
        }

        .section-title {
            position: relative;
            text-align: center;
            margin-bottom: 50px;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: var(--secondary-color);
        }

        .mission-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            border-left: 5px solid var(--secondary-color);
        }

        .mission-card:hover {
            transform: translateY(-5px);
        }

        .mission-icon {
            font-size: 3rem;
            color: var(--secondary-color);
            margin-bottom: 20px;
        }

        .team-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            margin-bottom: 30px;
        }

        .team-card:hover {
            transform: translateY(-5px);
        }

        .team-avatar {
            width: 100%;
            height: 250px;
            object-fit: cover;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        }

        .team-info {
            padding: 25px;
        }

        .team-name {
            color: var(--primary-color);
            font-weight: bold;
            margin-bottom: 5px;
        }

        .team-title {
            color: var(--secondary-color);
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .values-section {
            background: var(--light-bg);
            padding: 80px 0;
        }

        .value-item {
            text-align: center;
            padding: 30px 20px;
        }

        .value-icon {
            font-size: 2.5rem;
            color: var(--secondary-color);
            margin-bottom: 20px;
        }

        .timeline {
            position: relative;
            padding: 20px 0;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--secondary-color);
            transform: translateX(-50%);
        }

        .timeline-item {
            position: relative;
            margin: 50px 0;
        }

        .timeline-content {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            width: 45%;
        }

        .timeline-item:nth-child(odd) .timeline-content {
            margin-left: 55%;
        }

        .timeline-item:nth-child(even) .timeline-content {
            margin-right: 55%;
        }

        .timeline-date {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background: var(--secondary-color);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            z-index: 2;
        }

        .contact-info {
            background: var(--primary-color);
            color: white;
            padding: 60px 0;
        }

        .contact-item {
            text-align: center;
            margin-bottom: 30px;
        }

        .contact-icon {
            font-size: 2rem;
            color: var(--secondary-color);
            margin-bottom: 15px;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            border-radius: 10px;
        }

        .dropdown-item:hover {
            background: var(--accent-color);
            color: var(--primary-color);
        }

        @media (max-width: 768px) {
            .page-header {
                padding: 100px 0 60px;
            }
            
            .timeline::before {
                left: 20px;
            }
            
            .timeline-content {
                width: 100%;
                margin-left: 60px !important;
                margin-right: 0 !important;
            }
            
            .timeline-date {
                left: 20px;
                transform: translateY(-50%);
            }
            
            .mission-card {
                padding: 25px;
            }
        }
    </style>
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html" style="color: var(--secondary-color);">
                <i class="fas fa-lotus me-2"></i>佛教身心療癒
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">主頁</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="about.html">關於我們</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            課程
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses-goals.html">課程目標</a></li>
                            <li><a class="dropdown-item" href="courses-weekly.html">每週主題</a></li>
                            <li><a class="dropdown-item" href="courses-methods.html">進行方式</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            經文選讀
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="scriptures-search.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="scriptures-methods.html">療癒方法</a></li>
                            <li><a class="dropdown-item" href="scriptures-theory.html">療癒理論</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            身心療癒研究
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="research-search.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="research-theory.html">療癒理論</a></li>
                            <li><a class="dropdown-item" href="research-methods.html">療癒方法</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            藏傳佛教
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="tibetan-theory.html">藏醫理論</a></li>
                            <li><a class="dropdown-item" href="tibetan-practice.html">藏醫實習</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            海外實習
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="overseas-members.html">成員</a></li>
                            <li><a class="dropdown-item" href="overseas-research.html">研究成果</a></li>
                            <li><a class="dropdown-item" href="overseas-future.html">未來計畫</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="workshop.html">工作坊</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="membership.html">加入會員</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 頁面標題 -->
    <section class="page-header">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8 page-header-content">
                    <h1 class="display-4 fw-bold mb-4">關於我們</h1>
                    <p class="lead">致力於融合古老佛教智慧與現代療癒科學</p>
                </div>
            </div>
        </div>
    </section>

    <main>
        <!-- 使命願景 -->
        <section class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 class="section-title h1 fw-bold">我們的使命</h2>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-4">
                        <div class="mission-card text-center">
                            <div class="mission-icon">
                                <i class="fas fa-heart"></i>
                            </div>
                            <h4 class="fw-bold mb-3">慈悲關懷</h4>
                            <p>以佛教的慈悲精神為核心，關懷每一位尋求身心療癒的人，提供溫暖與支持，讓愛與智慧照亮每個心靈。</p>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="mission-card text-center">
                            <div class="mission-icon">
                                <i class="fas fa-brain"></i>
                            </div>
                            <h4 class="fw-bold mb-3">智慧融合</h4>
                            <p>結合千年佛教智慧與現代科學研究，創造全新的身心療癒方法，讓古老教義在現代生活中發揮最大效用。</p>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="mission-card text-center">
                            <div class="mission-icon">
                                <i class="fas fa-hands-helping"></i>
                            </div>
                            <h4 class="fw-bold mb-3">實踐服務</h4>
                            <p>提供實用的療癒工具與方法，幫助現代人在快節奏生活中找到內在平靜，重建身心和諧的生活方式。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 核心價值 -->
        <section class="values-section">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 class="section-title h1 fw-bold">核心價值</h2>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-3 col-md-6">
                        <div class="value-item">
                            <div class="value-icon">
                                <i class="fas fa-balance-scale"></i>
                            </div>
                            <h5 class="fw-bold">正念平衡</h5>
                            <p>培養正念覺察，在忙碌生活中保持身心平衡，建立健康的生活態度。</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="value-item">
                            <div class="value-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <h5 class="fw-bold">智慧啟發</h5>
                            <p>透過佛教教義啟發內在智慧，幫助個人認識自我，找到人生方向。</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="value-item">
                            <div class="value-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <h5 class="fw-bold">共修成長</h5>
                            <p>建立學習社群，透過共同修習與分享，促進彼此的心靈成長。</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="value-item">
                            <div class="value-icon">
                                <i class="fas fa-globe"></i>
                            </div>
                            <h5 class="fw-bold">普世關懷</h5>
                            <p>超越文化與宗教界限，將療癒智慧傳播給需要的每一個人。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 團隊介紹 -->
        <section class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 class="section-title h1 fw-bold">專業團隊</h2>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-4 col-md-6">
                        <div class="team-card">
                            <div class="team-avatar"></div>
                            <div class="team-info">
                                <h5 class="team-name">釋慧覺法師</h5>
                                <p class="team-title">創辦人 / 首席指導師</p>
                                <p>畢業於佛光大學佛教學院，具有20年佛教修行與教學經驗，專精於禪修療癒與正念減壓技法。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <div class="team-card">
                            <div class="team-avatar"></div>
                            <div class="team-info">
                                <h5 class="team-name">陳雅雯博士</h5>
                                <p class="team-title">研究總監</p>
                                <p>心理學博士，專研正念認知療法與佛教心理學，發表多篇身心療癒相關國際期刊論文。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <div class="team-card">
                            <div class="team-avatar"></div>
                            <div class="team-info">
                                <h5 class="team-name">洛桑丹增仁波切</h5>
                                <p class="team-title">藏醫指導師</p>
                                <p>來自西藏的資深藏醫師，具有30年藏醫臨床經驗，專精於藏醫診斷與傳統療法。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <div class="team-card">
                            <div class="team-avatar"></div>
                            <div class="team-info">
                                <h5 class="team-name">林志明教授</h5>
                                <p class="team-title">學術顧問</p>
                                <p>台灣大學宗教學研究所教授，佛教文獻研究專家，致力於佛教現代化應用研究。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <div class="team-card">
                            <div class="team-avatar"></div>
                            <div class="team-info">
                                <h5 class="team-name">張美玲老師</h5>
                                <p class="team-title">課程設計師</p>
                                <p>具有教育心理學背景，專門設計適合現代人的佛教身心療癒課程與教材。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <div class="team-card">
                            <div class="team-avatar"></div>
                            <div class="team-info">
                                <h5 class="team-name">王建宏醫師</h5>
                                <p class="team-title">醫學顧問</p>
                                <p>精神科專科醫師，結合西醫與佛教療法，為學員提供全方位的身心健康指導。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 發展歷程 -->
        <section class="py-5" style="background: var(--light-bg);">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 class="section-title h1 fw-bold">發展歷程</h2>
                    </div>
                </div>
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-date">2020</div>
                        <div class="timeline-content">
                            <h5 class="fw-bold">網站創立</h5>
                            <p>由釋慧覺法師創立，開始整合佛教智慧與現代療癒科學，建立線上學習平台。</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-date">2021</div>
                        <div class="timeline-content">
                            <h5 class="fw-bold">專業團隊成立</h5>
                            <p>邀請多位專家學者加入，成立跨領域專業團隊，開發系統性課程內容。</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-date">2022</div>
                        <div class="timeline-content">
                            <h5 class="fw-bold">國際合作開始</h5>
                            <p>與尼泊爾、印度等地的佛教機構建立合作關係，開始海外實習計畫。</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-date">2023</div>
                        <div class="timeline-content">
                            <h5 class="fw-bold">研究成果發表</h5>
                            <p>發表多項身心療癒研究成果，獲得學術界與醫療界的廣泛認可。</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-date">2024</div>
                        <div class="timeline-content">
                            <h5 class="fw-bold">全面數位化</h5>
                            <p>完成平台全面升級，提供更豐富的數位學習資源與互動功能。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 聯絡資訊 -->
        <section class="contact-info">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-5">
                        <h2 class="fw-bold" style="color: var(--secondary-color);">聯絡我們</h2>
                        <p class="lead">歡迎與我們聯繫，一同踏上身心療癒的智慧之路</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-3 col-md-6">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <h6 class="fw-bold">地址</h6>
                            <p>台北市中正區重慶南路一段122號8樓</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <h6 class="fw-bold">電話</h6>
                            <p>(02) 2345-6789</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <h6 class="fw-bold">電子信箱</h6>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <h6 class="fw-bold">服務時間</h6>
                            <p>週一至週五 9:00-18:00</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // 導航欄滾動效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar-custom');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // 動畫效果
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // 為各種元素添加動畫
        document.addEventListener('DOMContentLoaded', function() {
            const animatedElements = document.querySelectorAll('.mission-card, .team-card, .timeline-item, .value-item');
            
            animatedElements.forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = 'all 0.6s ease';
                observer.observe(el);
            });
        });
    </script>
</body>
</html>
            