/**
 * 澄源閱讀 - 收藏管理 API
 * Cloudflare Workers 函數
 * 
 * 功能：
 * - 管理用戶收藏文章
 * - 收藏統計
 * - 收藏同步
 * - 導出收藏
 */

// CORS 處理
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Session-ID',
  'Access-Control-Max-Age': '86400',
};

// 處理 CORS 預檢請求
function handleCORS(request) {
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }
}

// 錯誤處理
function createErrorResponse(message, status = 400) {
  return new Response(JSON.stringify({
    success: false,
    error: message,
    timestamp: new Date().toISOString()
  }), {
    status,
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders
    }
  });
}

// 成功響應
function createSuccessResponse(data, meta = {}) {
  return new Response(JSON.stringify({
    success: true,
    data,
    meta,
    timestamp: new Date().toISOString()
  }), {
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders
    }
  });
}

// 獲取客戶端IP
function getClientIP(request) {
  return request.headers.get('CF-Connecting-IP') || 
         request.headers.get('X-Forwarded-For') || 
         '127.0.0.1';
}

// 獲取會話ID
function getSessionId(request) {
  return request.headers.get('X-Session-ID') || 
         crypto.randomUUID();
}

// 主處理函數
export default {
  async fetch(request, env, ctx) {
    // 處理 CORS
    const corsResponse = handleCORS(request);
    if (corsResponse) return corsResponse;

    try {
      const url = new URL(request.url);
      const path = url.pathname;
      const method = request.method;

      // 路由處理
      if (path === '/api/favorites' && method === 'GET') {
        return await getFavorites(request, env);
      }
      
      if (path === '/api/favorites' && method === 'POST') {
        return await addToFavorites(request, env);
      }
      
      if (path.match(/^\/api\/favorites\/\d+$/) && method === 'DELETE') {
        const articleId = path.split('/').pop();
        return await removeFromFavorites(request, env, articleId);
      }
      
      if (path === '/api/favorites/sync' && method === 'POST') {
        return await syncFavorites(request, env);
      }
      
      if (path === '/api/favorites/export' && method === 'GET') {
        return await exportFavorites(request, env);
      }
      
      if (path === '/api/favorites/stats' && method === 'GET') {
        return await getFavoritesStats(request, env);
      }

      return createErrorResponse('Favorites API endpoint not found', 404);

    } catch (error) {
      console.error('Favorites API Error:', error);
      return createErrorResponse('Internal server error', 500);
    }
  }
};

// 獲取用戶收藏列表
async function getFavorites(request, env) {
  const sessionId = getSessionId(request);
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page')) || 1;
  const limit = Math.min(parseInt(url.searchParams.get('limit')) || 12, 50);
  const category = url.searchParams.get('category');
  const sort = url.searchParams.get('sort') || 'created_at';
  const order = url.searchParams.get('order') || 'DESC';
  
  const offset = (page - 1) * limit;

  try {
    // 構建查詢條件
    let whereConditions = ['f.session_id = ?', 'a.status = ?'];
    let params = [sessionId, 'published'];

    if (category) {
      whereConditions.push('a.category = ?');
      params.push(category);
    }

    const whereClause = whereConditions.join(' AND ');

    // 獲取總數
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM favorites f
      JOIN articles a ON f.article_id = a.id
      WHERE ${whereClause}
    `;
    const countResult = await env.DB.prepare(countQuery).bind(...params).first();
    const total = countResult.total;

    // 獲取收藏文章
    const favoritesQuery = `
      SELECT 
        f.id as favorite_id,
        f.created_at as favorited_at,
        a.id, a.title, a.excerpt, a.category, a.author, a.author_avatar,
        a.published_at, a.views, a.likes, a.reading_time, a.difficulty,
        a.featured_image, a.featured_image_alt, a.tags, a.slug
      FROM favorites f
      JOIN articles a ON f.article_id = a.id
      WHERE ${whereClause}
      ORDER BY f.${sort} ${order}
      LIMIT ? OFFSET ?
    `;

    const favorites = await env.DB.prepare(favoritesQuery)
      .bind(...params, limit, offset)
      .all();

    // 處理數據
    const processedFavorites = favorites.results.map(favorite => ({
      favoriteId: favorite.favorite_id,
      favoritedAt: new Date(favorite.favorited_at).toISOString(),
      article: {
        id: favorite.id,
        title: favorite.title,
        excerpt: favorite.excerpt,
        category: favorite.category,
        categoryName: getCategoryName(favorite.category),
        author: favorite.author,
        authorAvatar: favorite.author_avatar,
        publishedAt: new Date(favorite.published_at).toISOString(),
        views: favorite.views,
        likes: favorite.likes,
        readingTime: favorite.reading_time,
        difficulty: favorite.difficulty,
        featuredImage: favorite.featured_image,
        featuredImageAlt: favorite.featured_image_alt,
        tags: favorite.tags ? JSON.parse(favorite.tags) : [],
        slug: favorite.slug
      }
    }));

    const totalPages = Math.ceil(total / limit);

    return createSuccessResponse(processedFavorites, {
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      sessionId
    });

  } catch (error) {
    console.error('Get favorites error:', error);
    return createErrorResponse('Failed to fetch favorites');
  }
}

// 添加到收藏
async function addToFavorites(request, env) {
  const sessionId = getSessionId(request);
  const clientIP = getClientIP(request);
  
  try {
    const body = await request.json();
    const { articleId } = body;

    if (!articleId) {
      return createErrorResponse('文章ID不能為空');
    }

    // 檢查文章是否存在
    const article = await env.DB.prepare(`
      SELECT id, title FROM articles 
      WHERE id = ? AND status = 'published'
    `).bind(articleId).first();

    if (!article) {
      return createErrorResponse('文章不存在', 404);
    }

    // 檢查是否已經收藏
    const existingFavorite = await env.DB.prepare(`
      SELECT id FROM favorites 
      WHERE article_id = ? AND session_id = ?
    `).bind(articleId, sessionId).first();

    if (existingFavorite) {
      return createErrorResponse('文章已在收藏列表中');
    }

    // 添加到收藏
    const result = await env.DB.prepare(`
      INSERT INTO favorites (article_id, session_id, ip_address, user_agent)
      VALUES (?, ?, ?, ?)
    `).bind(
      articleId,
      sessionId,
      clientIP,
      request.headers.get('User-Agent') || ''
    ).run();

    // 更新文章點讚數
    await env.DB.prepare(`
      UPDATE articles 
      SET likes = likes + 1 
      WHERE id = ?
    `).bind(articleId).run();

    return createSuccessResponse({
      favoriteId: result.meta.last_row_id,
      articleId: parseInt(articleId),
      articleTitle: article.title,
      favoritedAt: new Date().toISOString(),
      message: '已添加到收藏'
    });

  } catch (error) {
    console.error('Add to favorites error:', error);
    return createErrorResponse('添加收藏失敗');
  }
}

// 從收藏中移除
async function removeFromFavorites(request, env, articleId) {
  const sessionId = getSessionId(request);
  
  try {
    // 檢查收藏是否存在
    const favorite = await env.DB.prepare(`
      SELECT f.id, a.title 
      FROM favorites f
      JOIN articles a ON f.article_id = a.id
      WHERE f.article_id = ? AND f.session_id = ?
    `).bind(articleId, sessionId).first();

    if (!favorite) {
      return createErrorResponse('收藏記錄不存在', 404);
    }

    // 從收藏中移除
    await env.DB.prepare(`
      DELETE FROM favorites 
      WHERE article_id = ? AND session_id = ?
    `).bind(articleId, sessionId).run();

    // 更新文章點讚數
    await env.DB.prepare(`
      UPDATE articles 
      SET likes = likes - 1 
      WHERE id = ? AND likes > 0
    `).bind(articleId).run();

    return createSuccessResponse({
      articleId: parseInt(articleId),
      articleTitle: favorite.title,
      message: '已從收藏中移除'
    });

  } catch (error) {
    console.error('Remove from favorites error:', error);
    return createErrorResponse('移除收藏失敗');
  }
}

// 同步收藏（批量操作）
async function syncFavorites(request, env) {
  const sessionId = getSessionId(request);
  const clientIP = getClientIP(request);
  
  try {
    const body = await request.json();
    const { favorites = [], action = 'sync' } = body;

    if (!Array.isArray(favorites)) {
      return createErrorResponse('收藏列表格式錯誤');
    }

    let added = 0;
    let updated = 0;
    let errors = [];

    if (action === 'sync') {
      // 全量同步：先清除現有收藏，再添加新的
      await env.DB.prepare(`
        DELETE FROM favorites WHERE session_id = ?
      `).bind(sessionId).run();

      for (const favorite of favorites) {
        try {
          await env.DB.prepare(`
            INSERT INTO favorites (article_id, session_id, ip_address, user_agent)
            VALUES (?, ?, ?, ?)
          `).bind(
            favorite.articleId,
            sessionId,
            clientIP,
            request.headers.get('User-Agent') || ''
          ).run();
          added++;
        } catch (error) {
          errors.push(`文章 ${favorite.articleId}: ${error.message}`);
        }
      }
    } else if (action === 'merge') {
      // 增量同步：只添加不存在的收藏
      for (const favorite of favorites) {
        try {
          const existing = await env.DB.prepare(`
            SELECT id FROM favorites 
            WHERE article_id = ? AND session_id = ?
          `).bind(favorite.articleId, sessionId).first();

          if (!existing) {
            await env.DB.prepare(`
              INSERT INTO favorites (article_id, session_id, ip_address, user_agent)
              VALUES (?, ?, ?, ?)
            `).bind(
              favorite.articleId,
              sessionId,
              clientIP,
              request.headers.get('User-Agent') || ''
            ).run();
            added++;
          } else {
            updated++;
          }
        } catch (error) {
          errors.push(`文章 ${favorite.articleId}: ${error.message}`);
        }
      }
    }

    return createSuccessResponse({
      action,
      added,
      updated,
      errors,
      total: favorites.length,
      message: `同步完成：新增 ${added} 個，更新 ${updated} 個`
    });

  } catch (error) {
    console.error('Sync favorites error:', error);
    return createErrorResponse('同步收藏失敗');
  }
}

// 導出收藏
async function exportFavorites(request, env) {
  const sessionId = getSessionId(request);
  const url = new URL(request.url);
  const format = url.searchParams.get('format') || 'json';

  try {
    // 獲取所有收藏文章
    const favorites = await env.DB.prepare(`
      SELECT 
        f.created_at as favorited_at,
        a.id, a.title, a.excerpt, a.category, a.author,
        a.published_at, a.slug, a.tags
      FROM favorites f
      JOIN articles a ON f.article_id = a.id
      WHERE f.session_id = ? AND a.status = 'published'
      ORDER BY f.created_at DESC
    `).bind(sessionId).all();

    const processedFavorites = favorites.results.map(favorite => ({
      id: favorite.id,
      title: favorite.title,
      excerpt: favorite.excerpt,
      category: favorite.category,
      categoryName: getCategoryName(favorite.category),
      author: favorite.author,
      publishedAt: new Date(favorite.published_at).toISOString(),
      favoritedAt: new Date(favorite.favorited_at).toISOString(),
      slug: favorite.slug,
      tags: favorite.tags ? JSON.parse(favorite.tags) : [],
      url: `${env.SITE_URL || 'https://chengyuan-reading.pages.dev'}/article/${favorite.id}`
    }));

    if (format === 'csv') {
      // CSV 格式導出
      const csvHeader = 'ID,標題,分類,作者,發布時間,收藏時間,URL\n';
      const csvRows = processedFavorites.map(fav => 
        `${fav.id},"${fav.title}","${fav.categoryName}","${fav.author}","${fav.publishedAt}","${fav.favoritedAt}","${fav.url}"`
      ).join('\n');
      
      return new Response(csvHeader + csvRows, {
        headers: {
          'Content-Type': 'text/csv; charset=utf-8',
          'Content-Disposition': `attachment; filename="favorites_${new Date().toISOString().split('T')[0]}.csv"`,
          ...corsHeaders
        }
      });
    } else {
      // JSON 格式導出
      return createSuccessResponse({
        favorites: processedFavorites,
        exportedAt: new Date().toISOString(),
        total: processedFavorites.length,
        sessionId
      });
    }

  } catch (error) {
    console.error('Export favorites error:', error);
    return createErrorResponse('導出收藏失敗');
  }
}

// 獲取收藏統計
async function getFavoritesStats(request, env) {
  const sessionId = getSessionId(request);

  try {
    // 用戶收藏統計
    const userStats = await env.DB.prepare(`
      SELECT 
        COUNT(*) as total_favorites,
        COUNT(CASE WHEN f.created_at > datetime('now', '-7 days') THEN 1 END) as favorites_this_week,
        COUNT(CASE WHEN f.created_at > datetime('now', '-30 days') THEN 1 END) as favorites_this_month
      FROM favorites f
      JOIN articles a ON f.article_id = a.id
      WHERE f.session_id = ? AND a.status = 'published'
    `).bind(sessionId).first();

    // 按分類統計
    const categoryStats = await env.DB.prepare(`
      SELECT 
        a.category,
        COUNT(*) as favorite_count
      FROM favorites f
      JOIN articles a ON f.article_id = a.id
      WHERE f.session_id = ? AND a.status = 'published'
      GROUP BY a.category
      ORDER BY favorite_count DESC
    `).bind(sessionId).all();

    // 最近收藏的文章
    const recentFavorites = await env.DB.prepare(`
      SELECT 
        a.id, a.title, a.category, f.created_at as favorited_at
      FROM favorites f
      JOIN articles a ON f.article_id = a.id
      WHERE f.session_id = ? AND a.status = 'published'
      ORDER BY f.created_at DESC
      LIMIT 5
    `).bind(sessionId).all();

    return createSuccessResponse({
      user: {
        totalFavorites: userStats.total_favorites,
        favoritesThisWeek: userStats.favorites_this_week,
        favoritesThisMonth: userStats.favorites_this_month
      },
      categories: categoryStats.results.map(cat => ({
        category: cat.category,
        categoryName: getCategoryName(cat.category),
        favoriteCount: cat.favorite_count
      })),
      recent: recentFavorites.results.map(fav => ({
        id: fav.id,
        title: fav.title,
        category: fav.category,
        categoryName: getCategoryName(fav.category),
        favoritedAt: new Date(fav.favorited_at).toISOString()
      })),
      sessionId
    });

  } catch (error) {
    console.error('Get favorites stats error:', error);
    return createErrorResponse('獲取收藏統計失敗');
  }
}

// 輔助函數：獲取分類名稱
function getCategoryName(categorySlug) {
  const categoryNames = {
    'dharma': '佛學智慧',
    'healing': '身心療癒', 
    'research': '最新研究',
    'meditation': '禪修指導',
    'philosophy': '佛教哲學',
    'practice': '修行方法',
    'sutras': '經典解讀',
    'masters': '大師教導',
    'stories': '佛教故事'
  };
  return categoryNames[categorySlug] || categorySlug;
}