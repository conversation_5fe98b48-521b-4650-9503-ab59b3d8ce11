<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>未來計畫 - 佛教身心療癒網站</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2C3E50;
            --secondary-color: #F39C12;
            --accent-color: #E8F4F8;
            --text-color: #2C3E50;
            --light-bg: #F8F9FA;
            --success-color: #27AE60;
            --info-color: #3498DB;
            --future-purple: #9B59B6;
            --future-teal: #1ABC9C;
        }

        body {
            font-family: 'Noto Sans CJK TC', 'Microsoft JhengHei', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
        }

        .page-header {
            background: linear-gradient(135deg, var(--future-purple) 0%, var(--primary-color) 100%);
            color: white;
            padding: 120px 0 80px;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/></svg>') repeat;
        }

        .navbar-custom {
            background: rgba(44, 62, 80, 0.95);
            backdrop-filter: blur(10px);
        }

        .nav-link {
            color: white !important;
            font-weight: 500;
            margin: 0 10px;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--secondary-color) !important;
        }

        .future-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-top: 4px solid var(--future-purple);
            position: relative;
            overflow: hidden;
        }

        .future-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(155, 89, 182, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .future-card:hover::before {
            left: 100%;
        }

        .future-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .future-icon {
            font-size: 3rem;
            color: var(--future-purple);
            margin-bottom: 20px;
            text-align: center;
        }

        .section-title {
            position: relative;
            text-align: center;
            margin-bottom: 50px;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: var(--secondary-color);
        }

        .roadmap {
            position: relative;
            padding: 40px 0;
        }

        .roadmap::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(to bottom, var(--future-purple), var(--future-teal));
            transform: translateX(-50%);
        }

        .roadmap-item {
            position: relative;
            margin: 50px 0;
        }

        .roadmap-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            width: 45%;
            position: relative;
        }

        .roadmap-item:nth-child(odd) .roadmap-content {
            margin-left: 55%;
        }

        .roadmap-item:nth-child(even) .roadmap-content {
            margin-right: 55%;
        }

        .roadmap-year {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background: var(--future-purple);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.1rem;
            z-index: 2;
            box-shadow: 0 5px 15px rgba(155, 89, 182, 0.3);
        }

        .goal-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .goal-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .goal-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }

        .goal-card.short-term::before { background: var(--success-color); }
        .goal-card.medium-term::before { background: var(--secondary-color); }
        .goal-card.long-term::before { background: var(--future-purple); }

        .goal-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .goal-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }

        .goal-card.short-term .goal-icon { color: var(--success-color); }
        .goal-card.medium-term .goal-icon { color: var(--secondary-color); }
        .goal-card.long-term .goal-icon { color: var(--future-purple); }

        .innovation-section {
            background: linear-gradient(135deg, var(--light-bg), #f0f8ff);
            padding: 80px 0;
        }

        .innovation-item {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 4px solid var(--future-teal);
            transition: transform 0.3s ease;
        }

        .innovation-item:hover {
            transform: translateX(10px);
        }

        .innovation-icon {
            color: var(--future-teal);
            font-size: 1.5rem;
            margin-right: 15px;
        }

        .expansion-map {
            background: white;
            border-radius: 15px;
            padding: 40px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .region-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }

        .region-item:last-child {
            border-bottom: none;
        }

        .region-info {
            display: flex;
            align-items: center;
        }

        .region-flag {
            font-size: 2rem;
            margin-right: 20px;
        }

        .region-details h6 {
            margin: 0;
            color: var(--primary-color);
        }

        .region-details small {
            color: #666;
        }

        .region-status {
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-planning {
            background: rgba(243, 156, 18, 0.1);
            color: var(--secondary-color);
        }

        .status-developing {
            background: rgba(52, 152, 219, 0.1);
            color: var(--info-color);
        }

        .status-launching {
            background: rgba(155, 89, 182, 0.1);
            color: var(--future-purple);
        }

        .collaboration-call {
            background: var(--future-purple);
            color: white;
            border-radius: 15px;
            padding: 50px;
            text-align: center;
            margin: 50px 0;
        }

        .btn-future {
            background: var(--future-teal);
            border: none;
            color: white;
            padding: 15px 40px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-future:hover {
            background: #16A085;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(26, 188, 156, 0.4);
            color: white;
        }

        @media (max-width: 768px) {
            .page-header {
                padding: 100px 0 60px;
            }
            .future-card {
                padding: 20px;
            }
            .roadmap::before {
                left: 20px;
            }
            .roadmap-content {
                width: 100%;
                margin-left: 60px !important;
                margin-right: 0 !important;
            }
            .roadmap-year {
                left: 20px;
                transform: translateY(-50%);
            }
            .collaboration-call {
                padding: 30px;
            }
        }
    </style>
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html" style="color: var(--secondary-color);">
                <i class="fas fa-lotus me-2"></i>佛教身心療癒
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">主頁</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">關於我們</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            課程
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses-goals.html">課程目標</a></li>
                            <li><a class="dropdown-item" href="courses-weekly.html">每週主題</a></li>
                            <li><a class="dropdown-item" href="courses-methods.html">進行方式</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            經文選讀
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="scriptures-search.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="scriptures-methods.html">療癒方法</a></li>
                            <li><a class="dropdown-item" href="scriptures-theory.html">療癒理論</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            身心療癒研究
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="research-search.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="research-theory.html">療癒理論</a></li>
                            <li><a class="dropdown-item" href="research-methods.html">療癒方法</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            藏傳佛教
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="tibetan-theory.html">藏醫理論</a></li>
                            <li><a class="dropdown-item" href="tibetan-practice.html">藏醫實習</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown">
                            海外實習
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="overseas-members.html">成員</a></li>
                            <li><a class="dropdown-item" href="overseas-research.html">研究成果</a></li>
                            <li><a class="dropdown-item active" href="overseas-future.html">未來計畫</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="workshop.html">工作坊</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="membership.html">加入會員</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 頁面標題 -->
    <section class="page-header">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-4">未來計畫</h1>
                    <p class="lead">展望佛教身心療癒的未來發展藍圖</p>
                </div>
            </div>
        </div>
    </section>

    <main>
        <!-- 發展目標 -->
        <section class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 class="section-title h1 fw-bold">發展目標</h2>
                    </div>
                </div>

                <div class="goal-grid">
                    <div class="goal-card short-term">
                        <div class="goal-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h4 class="fw-bold mb-3">短期目標 (2025)</h4>
                        <ul class="text-start">
                            <li>擴展實習計畫至10個國家</li>
                            <li>建立線上VR療癒體驗平台</li>
                            <li>完成500名學員培訓</li>
                            <li>發表20篇國際期刊論文</li>
                            <li>獲得WHO傳統醫學認證</li>
                        </ul>
                        <div class="mt-3">
                            <span class="badge bg-success">進行中</span>
                        </div>
                    </div>

                    <div class="goal-card medium-term">
                        <div class="goal-icon">
                            <i class="fas fa-globe-asia"></i>
                        </div>
                        <h4 class="fw-bold mb-3">中期目標 (2026-2027)</h4>
                        <ul class="text-start">
                            <li>成立國際佛教療癒研究中心</li>
                            <li>建立全球認證體系</li>
                            <li>推出多語言學習平台</li>
                            <li>培訓1000名認證導師</li>
                            <li>與聯合國教科文組織合作</li>
                        </ul>
                        <div class="mt-3">
                            <span class="badge" style="background: var(--secondary-color);">規劃中</span>
                        </div>
                    </div>

                    <div class="goal-card long-term">
                        <div class="goal-icon">
                            <i class="fas fa-infinity"></i>
                        </div>
                        <h4 class="fw-bold mb-3">長期願景 (2028-2030)</h4>
                        <ul class="text-start">
                            <li>建立全球佛教療癒網絡</li>
                            <li>整合AI智能診療系統</li>
                            <li>影響全球健康政策制定</li>
                            <li>培育10000名療癒師</li>
                            <li>成為聯合國可持續發展夥伴</li>
                        </ul>
                        <div class="mt-3">
                            <span class="badge" style="background: var(--future-purple);">願景規劃</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 發展路線圖 -->
        <section class="py-5" style="background: var(--light-bg);">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 class="section-title h1 fw-bold">發展路線圖</h2>
                    </div>
                </div>
                
                <div class="roadmap">
                    <div class="roadmap-item">
                        <div class="roadmap-year">2025</div>
                        <div class="roadmap-content">
                            <h5 class="fw-bold">數位化轉型年</h5>
                            <p class="mb-3">全面數位化升級，建立智能學習平台</p>
                            <ul class="mb-0">
                                <li>推出VR/AR療癒體驗</li>
                                <li>建立AI輔助診斷系統</li>
                                <li>開發移動端應用程式</li>
                                <li>整合物聯網健康監測</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="roadmap-item">
                        <div class="roadmap-year">2026</div>
                        <div class="roadmap-content">
                            <h5 class="fw-bold">國際擴展年</h5>
                            <p class="mb-3">大幅擴展國際合作與交流</p>
                            <ul class="mb-0">
                                <li>成立歐洲、美洲分部</li>
                                <li>建立多語言服務體系</li>
                                <li>簽署國際合作協議</li>
                                <li>舉辦全球佛教療癒大會</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="roadmap-item">
                        <div class="roadmap-year">2027</div>
                        <div class="roadmap-content">
                            <h5 class="fw-bold">標準化建立年</h5>
                            <p class="mb-3">建立行業標準與認證體系</p>
                            <ul class="mb-0">
                                <li>制定國際療癒標準</li>
                                <li>建立認證考試制度</li>
                                <li>設立質量監督機制</li>
                                <li>推動政策法規制定</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="roadmap-item">
                        <div class="roadmap-year">2028</div>
                        <div class="roadmap-content">
                            <h5 class="fw-bold">智能融合年</h5>
                            <p class="mb-3">深度整合人工智能與傳統智慧</p>
                            <ul class="mb-0">
                                <li>推出AI療癒助手</li>
                                <li>建立預測診斷系統</li>
                                <li>開發個人化療程設計</li>
                                <li>實現遠程智能監護</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="roadmap-item">
                        <div class="roadmap-year">2030</div>
                        <div class="roadmap-content">
                            <h5 class="fw-bold">全球影響年</h5>
                            <p class="mb-3">成為全球健康福祉的重要力量</p>
                            <ul class="mb-0">
                                <li>影響全球健康政策</li>
                                <li>推動聯合國SDGs實現</li>
                                <li>建立全球療癒網絡</li>
                                <li>實現可持續發展模式</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 創新項目 -->
        <section class="innovation-section">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 class="section-title h1 fw-bold">創新項目</h2>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-lg-6">
                        <div class="innovation-item">
                            <h5 class="fw-bold">
                                <i class="fas fa-vr-cardboard innovation-icon"></i>
                                VR佛教療癒體驗
                            </h5>
                            <p>結合虛擬實境技術，創造沉浸式的禪修與療癒環境，讓使用者能在虛擬空間中體驗西藏寺廟、喜馬拉雅山等聖地的療癒能量。</p>
                        </div>
                        
                        <div class="innovation-item">
                            <h5 class="fw-bold">
                                <i class="fas fa-robot innovation-icon"></i>
                                AI智能診斷系統
                            </h5>
                            <p>運用機器學習分析用戶的語音、表情、生理指標，結合傳統藏醫三根理論，提供個人化的身心狀態評估與療癒建議。</p>
                        </div>
                        
                        <div class="innovation-item">
                            <h5 class="fw-bold">
                                <i class="fas fa-mobile-alt innovation-icon"></i>
                                隨身療癒助手
                            </h5>
                            <p>開發智能手機應用，提供24小時個人化療癒指導，包括正念提醒、呼吸練習、情緒調節等功能，隨時隨地提供支援。</p>
                        </div>
                    </div>
                    
                    <div class="col-lg-6">
                        <div class="innovation-item">
                            <h5 class="fw-bold">
                                <i class="fas fa-dna innovation-icon"></i>
                                基因療癒配對
                            </h5>
                            <p>結合基因檢測技術，分析個人體質特徵，配對最適合的佛教療癒方法，實現精準個人化的療癒方案設計。</p>
                        </div>
                        
                        <div class="innovation-item">
                            <h5 class="fw-bold">
                                <i class="fas fa-satellite innovation-icon"></i>
                                全球療癒網絡
                            </h5>
                            <p>建立跨國的療癒師網絡平台，實現實時的遠程指導與會診，讓全球任何角落的人都能獲得專業的佛教療癒服務。</p>
                        </div>
                        
                        <div class="innovation-item">
                            <h5 class="fw-bold">
                                <i class="fas fa-leaf innovation-icon"></i>
                                生態療癒園區
                            </h5>
                            <p>在全球建立結合自然環境與佛教文化的療癒園區，提供身心靈完整的療癒體驗，成為現代人的心靈綠洲。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 區域擴展計畫 -->
        <section class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 class="section-title h1 fw-bold">區域擴展計畫</h2>
                    </div>
                </div>
                
                <div class="expansion-map">
                    <h5 class="fw-bold mb-4 text-center">全球佈局時程</h5>
                    
                    <div class="region-item">
                        <div class="region-info">
                            <span class="region-flag">🌏</span>
                            <div class="region-details">
                                <h6>東南亞地區</h6>
                                <small>泰國、新加坡、馬來西亞、印尼</small>
                            </div>
                        </div>
                        <span class="region-status status-developing">開發中</span>
                    </div>
                    
                    <div class="region-item">
                        <div class="region-info">
                            <span class="region-flag">🌍</span>
                            <div class="region-details">
                                <h6>歐洲地區</h6>
                                <small>英國、德國、法國、荷蘭</small>
                            </div>
                        </div>
                        <span class="region-status status-planning">規劃中</span>
                    </div>
                    
                    <div class="region-item">
                        <div class="region-info">
                            <span class="region-flag">🌎</span>
                            <div class="region-details">
                                <h6>北美地區</h6>
                                <small>美國、加拿大</small>
                            </div>
                        </div>
                        <span class="region-status status-launching">即將啟動</span>
                    </div>
                    
                    <div class="region-item">
                        <div class="region-info">
                            <span class="region-flag">🌎</span>
                            <div class="region-details">
                                <h6>南美地區</h6>
                                <small>巴西、阿根廷、智利</small>
                            </div>
                        </div>
                        <span class="region-status status-planning">規劃中</span>
                    </div>
                    
                    <div class="region-item">
                        <div class="region-info">
                            <span class="region-flag">🌍</span>
                            <div class="region-details">
                                <h6>非洲地區</h6>
                                <small>南非、肯亞、摩洛哥</small>
                            </div>
                        </div>
                        <span class="region-status status-planning">規劃中</span>
                    </div>
                    
                    <div class="region-item">
                        <div class="region-info">
                            <span class="region-flag">🌏</span>
                            <div class="region-details">
                                <h6>大洋洲地區</h6>
                                <small>澳洲、紐西蘭</small>
                            </div>
                        </div>
                        <span class="region-status status-developing">開發中</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 合作呼籲 -->
        <section class="py-5">
            <div class="container">
                <div class="collaboration-call">
                    <h3 class="fw-bold mb-4">
                        <i class="fas fa-hands-helping me-3"></i>
                        邀請您成為改變世界的夥伴
                    </h3>
                    <p class="lead mb-4">
                        我們相信，透過集結全球的智慧與力量，能夠為人類的身心健康帶來深遠的正面影響。
                        誠摯邀請您加入這個有意義的使命，一起創造一個更慈悲、更和諧的世界。
                    </p>
                    
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <div class="row text-center">
                                <div class="col-md-3 mb-3">
                                    <h5 class="fw-bold">🏥 醫療機構</h5>
                                    <p class="small">合作建立整合醫療服務</p>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <h5 class="fw-bold">🎓 學術單位</h5>
                                    <p class="small">共同進行研究與教育</p>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <h5 class="fw-bold">💼 企業夥伴</h5>
                                    <p class="small">提供技術與資源支持</p>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <h5 class="fw-bold">🌍 國際組織</h5>
                                    <p class="small">推動全球政策制定</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <a href="membership.html" class="btn-future me-3">立即加入</a>
                        <a href="about.html" class="btn btn-outline-light">了解更多</a>
                    </div>
                    
                    <div class="mt-4">
                        <small>
                            <i class="fas fa-envelope me-2"></i>
                            合作洽詢：<EMAIL>
                        </small>
                    </div>
                </div>
            </div>
        </section>

        <!-- 願景宣言 -->
        <section class="py-5" style="background: var(--light-bg);">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="future-card text-center">
                            <div class="future-icon">
                                <i class="fas fa-heart"></i>
                            </div>
                            <h3 class="fw-bold mb-4">我們的願景</h3>
                            <blockquote class="blockquote">
                                <p class="lead">"在不久的將來，每個人都能輕易獲得佛教身心療癒的智慧與方法，讓古老的療癒智慧在現代世界中綻放光芒，為全人類的福祉貢獻力量。"</p>
                            </blockquote>
                            
                            <div class="row mt-5">
                                <div class="col-md-4 mb-3">
                                    <h5 class="fw-bold text-primary">🕊️ 和平</h5>
                                    <p class="small">促進內心與世界的和平</p>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <h5 class="fw-bold text-primary">💚 療癒</h5>
                                    <p class="small">撫平身心靈的創傷</p>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <h5 class="fw-bold text-primary">🌟 智慧</h5>
                                    <p class="small">傳承與發揚古老智慧</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // 導航欄滾動效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar-custom');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // 路線圖年份動畫
        function animateRoadmapYears() {
            const years = document.querySelectorAll('.roadmap-year');
            years.forEach((year, index) => {
                setTimeout(() => {
                    year.style.transform = 'translate(-50%, -50%) scale(1.1)';
                    setTimeout(() => {
                        year.style.transform = 'translate(-50%, -50%) scale(1)';
                    }, 200);
                }, index * 300);
            });
        }

        // 卡片動畫
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                    
                    // 如果是路線圖部分，觸發年份動畫
                    if (entry.target.classList.contains('roadmap')) {
                        setTimeout(animateRoadmapYears, 500);
                    }
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.future-card, .goal-card, .roadmap-item, .innovation-item, .collaboration-call');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = `all 0.6s ease ${index * 0.1}s`;
                observer.observe(card);
            });
            
            // 特別處理路線圖
            const roadmap = document.querySelector('.roadmap');
            if (roadmap) {
                observer.observe(roadmap);
            }
        });

        // 區域狀態動畫
        document.querySelectorAll('.region-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                const status = this.querySelector('.region-status');
                status.style.transform = 'scale(1.1)';
                status.style.transition = 'transform 0.3s ease';
            });
            
            item.addEventListener('mouseleave', function() {
                const status = this.querySelector('.region-status');
                status.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>