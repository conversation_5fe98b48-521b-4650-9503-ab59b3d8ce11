<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>佛學智慧 - 澄源閱讀</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fc;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 頁面標題區域 */
        .page-header {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
            color: white;
            padding: 50px 30px;
            text-align: center;
            border-radius: 20px;
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="white" opacity="0.1"/><circle cx="80" cy="80" r="3" fill="white" opacity="0.1"/><circle cx="40" cy="60" r="1" fill="white" opacity="0.1"/></svg>');
            animation: float-pattern 30s linear infinite;
        }

        @keyframes float-pattern {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .page-header-content {
            position: relative;
            z-index: 2;
        }

        .page-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            animation: pulse 3s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .page-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        /* 篩選和搜索區域 */
        .filters-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .filters-row {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
            margin-bottom: 20px;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-label {
            font-weight: 500;
            color: #555;
            white-space: nowrap;
        }

        .filter-select {
            padding: 8px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 0.9rem;
            background: white;
            min-width: 120px;
            transition: border-color 0.3s ease;
        }

        .filter-select:focus {
            outline: none;
            border-color: #ff6b35;
        }

        .search-box {
            flex: 1;
            min-width: 250px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 12px 45px 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #ff6b35;
            box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
        }

        .search-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, #ff9a56, #ff6b35);
            color: white;
            border: none;
            border-radius: 6px;
            width: 32px;
            height: 32px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            transform: translateY(-50%) scale(1.1);
        }

        .filter-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .filter-tag {
            background: #f0f8ff;
            color: #1976d2;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .filter-tag:hover,
        .filter-tag.active {
            background: #1976d2;
            color: white;
            border-color: #1565c0;
        }

        /* 文章網格 */
        .articles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .article-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .article-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .article-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            overflow: hidden;
        }

        .article-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 30%, rgba(255,255,255,0.2) 0%, transparent 50%);
        }

        .article-content {
            padding: 25px;
        }

        .article-meta-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .article-category {
            background: #ffe0d6;
            color: #d84315;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .article-difficulty {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #666;
            font-size: 0.8rem;
        }

        .difficulty-stars {
            color: #ffa726;
        }

        .article-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 12px;
            color: #333;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .article-excerpt {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.6;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .article-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 15px;
        }

        .article-tag {
            background: #f0f0f0;
            color: #666;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
        }

        .article-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
            font-size: 0.8rem;
            color: #999;
        }

        .article-date {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .article-stats {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 3px;
        }

        /* 載入更多 */
        .load-more-section {
            text-align: center;
            margin-top: 40px;
        }

        .load-more-btn {
            background: linear-gradient(135deg, #ff9a56, #ff6b35);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .load-more-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 107, 53, 0.3);
        }

        .load-more-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        /* 空狀態 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-icon {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 20px;
        }

        .empty-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .empty-description {
            color: #999;
            line-height: 1.6;
        }

        /* 側邊欄（桌面端） */
        .content-layout {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
            margin-top: 20px;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .sidebar-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .sidebar-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .popular-topics {
            list-style: none;
        }

        .topic-item {
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .topic-item:last-child {
            border-bottom: none;
        }

        .topic-item:hover {
            color: #ff6b35;
        }

        .quick-links {
            list-style: none;
        }

        .quick-link {
            margin-bottom: 12px;
        }

        .quick-link a {
            color: #666;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .quick-link a:hover {
            background: #f0f8ff;
            color: #1976d2;
        }

        /* 響應式設計 */
        @media (max-width: 1024px) {
            .content-layout {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                flex-direction: row;
                overflow-x: auto;
            }
            
            .sidebar-card {
                min-width: 250px;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .page-header {
                padding: 40px 20px;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .filters-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-group {
                flex-direction: column;
                align-items: stretch;
            }
            
            .articles-grid {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                flex-direction: column;
            }
        }

        @media (max-width: 480px) {
            .page-title {
                font-size: 1.5rem;
            }
            
            .article-content {
                padding: 20px;
            }
            
            .filters-section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 頁面標題 -->
        <header class="page-header">
            <div class="page-header-content">
                <div class="page-icon">
                    <i class="fas fa-dharmachakra"></i>
                </div>
                <h1 class="page-title">佛學智慧</h1>
                <p class="page-subtitle">探索西藏佛教的深邃智慧，領悟古老教法的現代意義<br>在修行的道路上尋找內心的平靜與解脫</p>
            </div>
        </header>

        <!-- 篩選和搜索 -->
        <section class="filters-section">
            <div class="filters-row">
                <div class="filter-group">
                    <label class="filter-label">分類：</label>
                    <select class="filter-select" id="category-filter">
                        <option value="">全部分類</option>
                        <option value="meditation">禪修指導</option>
                        <option value="philosophy">佛教哲學</option>
                        <option value="practice">修行方法</option>
                        <option value="sutras">經典解讀</option>
                        <option value="masters">大師教導</option>
                        <option value="ethics">佛教倫理</option>
                        <option value="tradition">傳承體系</option>
                        <option value="stories">佛教故事</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">難度：</label>
                    <select class="filter-select" id="difficulty-filter">
                        <option value="">全部難度</option>
                        <option value="beginner">入門</option>
                        <option value="intermediate">進階</option>
                        <option value="advanced">高級</option>
                    </select>
                </div>
                
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="搜索佛學文章..." id="search-input">
                    <button class="search-btn" id="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            
            <div class="filter-tags">
                <span class="filter-tag active" data-tag="">全部</span>
                <span class="filter-tag" data-tag="compassion">慈悲修持</span>
                <span class="filter-tag" data-tag="wisdom">智慧開發</span>
                <span class="filter-tag" data-tag="mindfulness">正念覺察</span>
                <span class="filter-tag" data-tag="emptiness">空性教法</span>
                <span class="filter-tag" data-tag="karma">因果業力</span>
                <span class="filter-tag" data-tag="rebirth">輪迴生死</span>
            </div>
        </section>

        <!-- 內容佈局 -->
        <div class="content-layout">
            <!-- 主要內容區域 -->
            <main class="main-content">
                <!-- 文章網格 -->
                <div class="articles-grid" id="articles-grid">
                    <!-- 文章將通過 JavaScript 動態載入 -->
                </div>

                <!-- 空狀態 -->
                <div class="empty-state" id="empty-state" style="display: none;">
                    <div class="empty-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3 class="empty-title">沒有找到相關文章</h3>
                    <p class="empty-description">請嘗試調整搜索條件或瀏覽其他分類的內容</p>
                </div>

                <!-- 載入更多 -->
                <div class="load-more-section">
                    <button class="load-more-btn" id="load-more-btn">
                        <span class="btn-text">載入更多文章</span>
                        <span class="btn-loading" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i>
                        </span>
                    </button>
                </div>
            </main>

            <!-- 側邊欄 -->
            <aside class="sidebar">
                <div class="sidebar-card">
                    <h3 class="sidebar-title">
                        <i class="fas fa-fire"></i>
                        熱門主題
                    </h3>
                    <ul class="popular-topics">
                        <li class="topic-item" data-topic="compassion">慈悲心的培養</li>
                        <li class="topic-item" data-topic="meditation">藏傳禪修方法</li>
                        <li class="topic-item" data-topic="wisdom">般若智慧開發</li>
                        <li class="topic-item" data-topic="emptiness">中觀空性見</li>
                        <li class="topic-item" data-topic="bodhicitta">菩提心修持</li>
                        <li class="topic-item" data-topic="karma">業力與因果</li>
                    </ul>
                </div>

                <div class="sidebar-card">
                    <h3 class="sidebar-title">
                        <i class="fas fa-bookmark"></i>
                        快速導航
                    </h3>
                    <ul class="quick-links">
                        <li class="quick-link">
                            <a href="javascript:void(0)" data-page="healing">
                                <i class="fas fa-spa"></i>
                                身心療癒
                            </a>
                        </li>
                        <li class="quick-link">
                            <a href="javascript:void(0)" data-page="research">
                                <i class="fas fa-microscope"></i>
                                最新研究
                            </a>
                        </li>
                        <li class="quick-link">
                            <a href="javascript:void(0)" data-page="favorites">
                                <i class="fas fa-heart"></i>
                                我的收藏
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="sidebar-card">
                    <h3 class="sidebar-title">
                        <i class="fas fa-quote-left"></i>
                        每日法語
                    </h3>
                    <blockquote style="font-style: italic; color: #666; line-height: 1.6;">
                        "慈悲心是菩薩道的根本，智慧是解脫的關鍵。當我們將慈悲與智慧結合，就能真正幫助自己和眾生獲得究竟的快樂。"
                        <footer style="margin-top: 10px; font-size: 0.8rem; color: #999;">
                            — 達賴喇嘛
                        </footer>
                    </blockquote>
                </div>
            </aside>
        </div>
    </div>

    <script>
        // 模擬文章數據
        const articlesData = [
            {
                id: 1,
                title: "藏傳佛教中的慈悲修持法門",
                excerpt: "慈悲心是藏傳佛教修行的核心。本文深入探討如何在日常生活中培養真正的慈悲心，包括四無量心的修持方法、慈悲禪修的具體步驟，以及如何將慈悲心運用到人際關係中。",
                category: "practice",
                difficulty: "beginner",
                tags: ["慈悲修持", "四無量心", "禪修"],
                date: "2025-01-10",
                views: 1245,
                likes: 89,
                icon: "fas fa-heart"
            },
            {
                id: 2,
                title: "中觀派的空性哲學解析",
                excerpt: "空性是大乘佛教的核心思想，中觀派對此有深刻的闡述。本文從龍樹菩薩的根本思想出發，解釋緣起性空的深義，並探討如何在修行中體悟空性。",
                category: "philosophy",
                difficulty: "advanced",
                tags: ["空性教法", "中觀哲學", "緣起"],
                date: "2025-01-08",
                views: 892,
                likes: 67,
                icon: "fas fa-circle-notch"
            },
            {
                id: 3,
                title: "菩提心的培養與實修",
                excerpt: "菩提心是成佛的種子，是大乘修行的根本動機。文章詳細介紹了世俗菩提心和勝義菩提心的區別，以及七重因果教授和自他相換的修持方法。",
                category: "practice",
                difficulty: "intermediate",
                tags: ["菩提心", "七重因果", "自他相換"],
                date: "2025-01-05",
                views: 1567,
                likes: 134,
                icon: "fas fa-seedling"
            },
            {
                id: 4,
                title: "密宗金剛乘的修行次第",
                excerpt: "金剛乘是藏傳佛教的特色，具有完整的修行體系。本文介紹了密宗修行的四個次第：事部、行部、瑜伽部和無上瑜伽部，以及各自的特點和修持方法。",
                category: "practice",
                difficulty: "advanced",
                tags: ["金剛乘", "密宗修行", "四部密續"],
                date: "2025-01-03",
                views: 743,
                likes: 56,
                icon: "fas fa-gem"
            },
            {
                id: 5,
                title: "正念覺察在日常生活中的應用",
                excerpt: "正念是佛教修行的基礎，也是現代心理治療的重要方法。文章探討如何將佛教的正念修持融入現代生活，包括正念飲食、正念行走、正念工作等實用技巧。",
                category: "meditation",
                difficulty: "beginner",
                tags: ["正念覺察", "日常修行", "生活禪"],
                date: "2025-01-01",
                views: 2134,
                likes: 178,
                icon: "fas fa-eye"
            },
            {
                id: 6,
                title: "《菩提道次第廣論》修學指導",
                excerpt: "宗喀巴大師的《菩提道次第廣論》是藏傳佛教格魯派的根本典籍。本文提供了系統學習此論的方法，包括三士道的修學次第和關鍵要點的把握。",
                category: "sutras",
                difficulty: "intermediate",
                tags: ["菩提道次第", "三士道", "宗喀巴"],
                date: "2024-12-28",
                views: 1876,
                likes: 145,
                icon: "fas fa-book"
            },
            {
                id: 7,
                title: "禪修入門：止觀雙運的修持方法",
                excerpt: "止觀是佛教禪修的核心，止能令心安定，觀能生起智慧。本文詳細介紹了九住心的修持階段，以及如何在日常生活中培養專注力和觀察力。",
                category: "meditation",
                difficulty: "beginner",
                tags: ["禪修", "止觀雙運", "九住心"],
                date: "2024-12-25",
                views: 2456,
                likes: 189,
                icon: "fas fa-meditation"
            },
            {
                id: 8,
                title: "《心經》的般若智慧解讀",
                excerpt: "《般若波羅蜜多心經》雖然只有260字，卻濃縮了般若經的精華。本文逐句解釋心經的深義，探討觀自在菩薩的修行境界和空性智慧的體現。",
                category: "sutras",
                difficulty: "intermediate",
                tags: ["心經", "般若智慧", "空性"],
                date: "2024-12-22",
                views: 3456,
                likes: 267,
                icon: "fas fa-scroll"
            },
            {
                id: 9,
                title: "五戒十善：佛教倫理的基礎",
                excerpt: "五戒十善是佛教倫理體系的基礎，為在家修行者提供了清晰的行為準則。文章詳細解釋了不殺生、不偷盜、不邪淫、不妄語、不飲酒的深層含義。",
                category: "ethics",
                difficulty: "beginner",
                tags: ["五戒", "十善", "佛教倫理"],
                date: "2024-12-20",
                views: 1987,
                likes: 156,
                icon: "fas fa-balance-scale"
            },
            {
                id: 10,
                title: "六道輪迴與業力因果",
                excerpt: "輪迴是佛教的基本觀念，業力是推動輪迴的根本力量。本文探討六道眾生的生命狀態，以及善惡業如何影響未來的生命走向，並提供解脫的修行方法。",
                category: "philosophy",
                difficulty: "intermediate",
                tags: ["輪迴", "業力", "六道"],
                date: "2024-12-18",
                views: 2789,
                likes: 203,
                icon: "fas fa-sync"
            },
            {
                id: 11,
                title: "蓮花生大士的修行教法",
                excerpt: "蓮花生大士是藏傳佛教寧瑪派的開創者，被譽為第二佛陀。本文介紹了蓮師的生平事蹟，以及他所傳授的大圓滿法門和伏藏教法的特色。",
                category: "masters",
                difficulty: "advanced",
                tags: ["蓮花生", "大圓滿", "伏藏"],
                date: "2024-12-15",
                views: 1654,
                likes: 98,
                icon: "fas fa-lotus"
            },
            {
                id: 12,
                title: "四聖諦：佛陀的根本教義",
                excerpt: "四聖諦是佛陀初轉法輪時所說的根本教義：苦諦、集諦、滅諦、道諦。本文深入分析了苦的本質、苦的成因、解脫的可能性以及解脫的方法。",
                category: "philosophy",
                difficulty: "beginner",
                tags: ["四聖諦", "苦集滅道", "初轉法輪"],
                date: "2024-12-12",
                views: 4123,
                likes: 312,
                icon: "fas fa-dharmachakra"
            },
            {
                id: 13,
                title: "密勒日巴的修行故事與啟示",
                excerpt: "密勒日巴是西藏最著名的瑜伽士，他的修行故事充滿了傳奇色彩。從早年的復仇心理到後來的證悟成就，他的生命歷程為我們展示了修行轉化的可能性。",
                category: "masters",
                difficulty: "intermediate",
                tags: ["密勒日巴", "修行故事", "瑜伽士"],
                date: "2024-12-10",
                views: 2967,
                likes: 234,
                icon: "fas fa-mountain"
            },
            {
                id: 14,
                title: "唯識學說：心識的奧秘",
                excerpt: "唯識學是大乘佛教重要的哲學體系，由玄奘大師傳入中國。本文介紹了八識的結構、種子說的理論，以及轉識成智的修行目標。",
                category: "philosophy",
                difficulty: "advanced",
                tags: ["唯識", "八識", "種子說"],
                date: "2024-12-08",
                views: 1789,
                likes: 143,
                icon: "fas fa-brain"
            },
            {
                id: 15,
                title: "《金剛經》的無住生心智慧",
                excerpt: "《金剛經》是般若經的代表作，以「應無所住而生其心」最為著名。本文探討了無住的真義、如何在不執著中生起慈悲心，以及金剛般若的修持方法。",
                category: "sutras",
                difficulty: "advanced",
                tags: ["金剛經", "無住生心", "般若"],
                date: "2024-12-05",
                views: 3789,
                likes: 289,
                icon: "fas fa-gem"
            },
            {
                id: 16,
                title: "念佛法門：淨土修行指南",
                excerpt: "念佛是最簡單也最直接的修行方法，適合各種根機的修行者。本文介紹了持名念佛、觀想念佛、實相念佛的不同方法，以及往生淨土的條件。",
                category: "practice",
                difficulty: "beginner",
                tags: ["念佛", "淨土", "往生"],
                date: "2024-12-03",
                views: 5678,
                likes: 456,
                icon: "fas fa-praying-hands"
            },
            {
                id: 17,
                title: "八正道：解脫的完整之路",
                excerpt: "八正道是佛陀所開示的解脫道路，包括正見、正思惟、正語、正業、正命、正精進、正念、正定。本文詳細解釋每一正道的內容和修持方法。",
                category: "practice",
                difficulty: "intermediate",
                tags: ["八正道", "解脫道", "正念"],
                date: "2024-12-01",
                views: 3456,
                likes: 278,
                icon: "fas fa-route"
            },
            {
                id: 18,
                title: "藏傳佛教的傳承體系",
                excerpt: "藏傳佛教有著完整的傳承體系，包括格魯、薩迦、噶舉、寧瑪四大教派。本文介紹各教派的特色、主要修行法門，以及上師與弟子的關係。",
                category: "tradition",
                difficulty: "intermediate",
                tags: ["藏傳佛教", "四大教派", "傳承"],
                date: "2024-11-28",
                views: 2345,
                likes: 167,
                icon: "fas fa-university"
            },
            {
                id: 19,
                title: "死亡與中陰：生命的終極考驗",
                excerpt: "《西藏度亡經》詳細描述了死亡過程和中陰階段的體驗。本文解釋了死亡八階段、中陰身的特性，以及如何通過修行準備面對死亡和中陰。",
                category: "philosophy",
                difficulty: "advanced",
                tags: ["中陰", "死亡", "度亡經"],
                date: "2024-11-25",
                views: 2876,
                likes: 198,
                icon: "fas fa-hourglass"
            },
            {
                id: 20,
                title: "慈悲喜捨四無量心修持",
                excerpt: "四無量心是佛教修行的重要內容，包括慈心、悲心、喜心、捨心。本文提供了系統的修持方法，從親人開始逐步擴展到一切眾生的慈悲觀修。",
                category: "practice",
                difficulty: "beginner",
                tags: ["四無量心", "慈悲", "觀修"],
                date: "2024-11-22",
                views: 4567,
                likes: 367,
                icon: "fas fa-heart"
            }
        ];

        let currentPage = 1;
        let filteredArticles = [...articlesData];
        let currentFilters = {
            category: '',
            difficulty: '',
            tag: '',
            search: ''
        };

        // 渲染文章
        function renderArticles() {
            const grid = document.getElementById('articles-grid');
            const emptyState = document.getElementById('empty-state');
            
            if (filteredArticles.length === 0) {
                grid.innerHTML = '';
                emptyState.style.display = 'block';
                document.getElementById('load-more-btn').style.display = 'none';
                return;
            }

            emptyState.style.display = 'none';
            
            const articlesToShow = filteredArticles.slice(0, currentPage * 6);
            
            grid.innerHTML = articlesToShow.map(article => `
                <article class="article-card" data-article="${article.id}">
                    <div class="article-image">
                        <i class="${article.icon}"></i>
                    </div>
                    <div class="article-content">
                        <div class="article-meta-top">
                            <span class="article-category">${getCategoryName(article.category)}</span>
                            <div class="article-difficulty">
                                <span class="difficulty-stars">${getDifficultyStars(article.difficulty)}</span>
                                <span>${getDifficultyName(article.difficulty)}</span>
                            </div>
                        </div>
                        <h3 class="article-title">${article.title}</h3>
                        <p class="article-excerpt">${article.excerpt}</p>
                        <div class="article-tags">
                            ${article.tags.map(tag => `<span class="article-tag">${tag}</span>`).join('')}
                        </div>
                        <div class="article-footer">
                            <span class="article-date">
                                <i class="fas fa-calendar"></i>
                                ${article.date}
                            </span>
                            <div class="article-stats">
                                <span class="stat-item">
                                    <i class="fas fa-eye"></i>
                                    ${article.views}
                                </span>
                                <span class="stat-item">
                                    <i class="fas fa-heart"></i>
                                    ${article.likes}
                                </span>
                            </div>
                        </div>
                    </div>
                </article>
            `).join('');

            // 更新載入更多按鈕
            const loadMoreBtn = document.getElementById('load-more-btn');
            if (articlesToShow.length >= filteredArticles.length) {
                loadMoreBtn.style.display = 'none';
            } else {
                loadMoreBtn.style.display = 'block';
            }
        }

        // 工具函數
        function getCategoryName(category) {
            const names = {
                'meditation': '禪修指導',
                'philosophy': '佛教哲學',
                'practice': '修行方法',
                'stories': '佛教故事',
                'sutras': '經典解讀',
                'masters': '大師教導'
            };
            return names[category] || '佛學智慧';
        }

        function getDifficultyName(difficulty) {
            const names = {
                'beginner': '入門',
                'intermediate': '進階',
                'advanced': '高級'
            };
            return names[difficulty] || '';
        }

        function getDifficultyStars(difficulty) {
            const stars = {
                'beginner': '★☆☆',
                'intermediate': '★★☆',
                'advanced': '★★★'
            };
            return stars[difficulty] || '';
        }

        // 篩選函數
        function filterArticles() {
            filteredArticles = articlesData.filter(article => {
                const matchCategory = !currentFilters.category || article.category === currentFilters.category;
                const matchDifficulty = !currentFilters.difficulty || article.difficulty === currentFilters.difficulty;
                const matchSearch = !currentFilters.search || 
                    article.title.toLowerCase().includes(currentFilters.search.toLowerCase()) ||
                    article.excerpt.toLowerCase().includes(currentFilters.search.toLowerCase()) ||
                    article.tags.some(tag => tag.toLowerCase().includes(currentFilters.search.toLowerCase()));
                const matchTag = !currentFilters.tag || 
                    article.tags.some(tag => tag.includes(currentFilters.tag));

                return matchCategory && matchDifficulty && matchSearch && matchTag;
            });

            currentPage = 1;
            renderArticles();
        }

        // 事件監聽
        document.getElementById('category-filter').addEventListener('change', function(e) {
            currentFilters.category = e.target.value;
            filterArticles();
        });

        document.getElementById('difficulty-filter').addEventListener('change', function(e) {
            currentFilters.difficulty = e.target.value;
            filterArticles();
        });

        document.getElementById('search-input').addEventListener('input', function(e) {
            currentFilters.search = e.target.value;
            filterArticles();
        });

        document.getElementById('search-btn').addEventListener('click', function() {
            const searchInput = document.getElementById('search-input');
            currentFilters.search = searchInput.value;
            filterArticles();
        });

        // 標籤篩選
        document.querySelectorAll('.filter-tag').forEach(tag => {
            tag.addEventListener('click', function() {
                document.querySelectorAll('.filter-tag').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                currentFilters.tag = this.getAttribute('data-tag');
                filterArticles();
            });
        });

        // 載入更多
        document.getElementById('load-more-btn').addEventListener('click', function() {
            const btn = this;
            const btnText = btn.querySelector('.btn-text');
            const btnLoading = btn.querySelector('.btn-loading');
            
            btnText.style.display = 'none';
            btnLoading.style.display = 'inline-block';
            btn.disabled = true;

            setTimeout(() => {
                currentPage++;
                renderArticles();
                
                btnText.style.display = 'inline-block';
                btnLoading.style.display = 'none';
                btn.disabled = false;
            }, 1000);
        });

        // 文章點擊
        document.addEventListener('click', function(e) {
            const articleCard = e.target.closest('.article-card');
            if (articleCard) {
                const articleId = articleCard.getAttribute('data-article');
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'navigate',
                        page: 'article',
                        articleId: articleId
                    }, '*');
                }
            }
        });

        // 側邊欄導航
        document.addEventListener('click', function(e) {
            const link = e.target.closest('[data-page]');
            if (link) {
                e.preventDefault();
                const page = link.getAttribute('data-page');
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'navigate',
                        page: page
                    }, '*');
                }
            }
        });

        // 熱門主題點擊
        document.querySelectorAll('.topic-item').forEach(item => {
            item.addEventListener('click', function() {
                const topic = this.getAttribute('data-topic');
                currentFilters.tag = topic;
                
                // 更新標籤狀態
                document.querySelectorAll('.filter-tag').forEach(tag => {
                    tag.classList.remove('active');
                    if (tag.getAttribute('data-tag') === topic) {
                        tag.classList.add('active');
                    }
                });
                
                filterArticles();
            });
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderArticles();
        });

        // 回車搜索
        document.getElementById('search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                currentFilters.search = this.value;
                filterArticles();
            }
        });
    </script>
</body>
</html>