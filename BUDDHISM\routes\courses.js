const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const Course = require('../models/Course');
const User = require('../models/User');
const authenticate = require('../middleware/auth');

// 驗證錯誤處理中間件
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// 會員權限檢查中間件
const checkMembershipAccess = (requiredLevel = 'basic') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const membershipLevels = { basic: 1, premium: 2, vip: 3 };
    const userLevel = membershipLevels[req.user.membership.plan];
    const requiredMembershipLevel = membershipLevels[requiredLevel];

    if (userLevel < requiredMembershipLevel) {
      return res.status(403).json({ 
        error: 'Membership upgrade required',
        required: requiredLevel,
        current: req.user.membership.plan
      });
    }

    next();
  };
};

// 獲取課程列表
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      category,
      difficulty,
      search,
      sort = 'created',
      instructor,
      free,
      membership
    } = req.query;

    const skip = (page - 1) * limit;
    const query = { status: 'published' };

    // 添加篩選條件
    if (category) {
      query.category = { $regex: category, $options: 'i' };
    }
    if (difficulty) {
      query.difficulty = difficulty;
    }
    if (instructor) {
      query.instructor = instructor;
    }
    if (free === 'true') {
      query['pricing.free'] = true;
    }
    if (membership) {
      query['pricing.membershipRequired'] = membership;
    }

    // 搜尋功能
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } },
        { category: { $regex: search, $options: 'i' } }
      ];
    }

    // 排序邏輯
    const sortOptions = {
      'created': { created: -1 },
      'updated': { updated: -1 },
      'popularity': { 'statistics.enrollments': -1 },
      'rating': { 'statistics.averageRating': -1 },
      'title': { title: 1 },
      'difficulty': { difficulty: 1 },
      'duration': { duration: 1 }
    };

    const courses = await Course.find(query)
      .sort(sortOptions[sort] || sortOptions.created)
      .skip(skip)
      .limit(parseInt(limit))
      .populate('instructor', 'profile.firstName profile.lastName profile.avatar')
      .populate('prerequisites', 'title difficulty')
      .select('-content.modules.assignments -content.modules.documents'); // 隱藏詳細內容

    const total = await Course.countDocuments(query);

    // 獲取分類統計
    const categoryStats = await Course.aggregate([
      { $match: { status: 'published' } },
      { $group: { _id: '$category', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    res.json({
      message: 'Courses retrieved successfully',
      courses,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      },
      filters: {
        categories: categoryStats,
        difficulties: ['beginner', 'intermediate', 'advanced'],
        membershipLevels: ['basic', 'premium', 'vip']
      }
    });
  } catch (error) {
    console.error('Get courses error:', error);
    res.status(500).json({ error: 'Failed to fetch courses' });
  }
});

// 獲取特定課程詳情
router.get('/:id', async (req, res) => {
  try {
    const courseId = req.params.id;
    
    const course = await Course.findOne({
      _id: courseId,
      status: 'published'
    })
      .populate('instructor', 'profile.firstName profile.lastName profile.avatar profile.bio')
      .populate('prerequisites', 'title difficulty category')
      .populate('statistics.reviews');

    if (!course) {
      return res.status(404).json({ error: 'Course not found' });
    }

    // 檢查用戶是否已註冊此課程（如果有認證）
    let userEnrollment = null;
    if (req.headers.authorization) {
      try {
        const token = req.headers.authorization.replace('Bearer ', '');
        const jwt = require('jsonwebtoken');
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
        const user = await User.findById(decoded.sub);
        
        if (user) {
          userEnrollment = {
            enrolled: user.progress.currentCourses.includes(courseId) || 
                     user.progress.coursesCompleted.includes(courseId),
            completed: user.progress.coursesCompleted.includes(courseId)
          };
        }
      } catch (err) {
        // 忽略token錯誤，繼續返回課程信息
      }
    }

    // 獲取相關課程推薦
    const relatedCourses = await Course.find({
      _id: { $ne: courseId },
      status: 'published',
      $or: [
        { category: course.category },
        { tags: { $in: course.tags } },
        { difficulty: course.difficulty }
      ]
    })
      .limit(5)
      .populate('instructor', 'profile.firstName profile.lastName')
      .select('title category difficulty duration statistics.averageRating');

    res.json({
      message: 'Course details retrieved successfully',
      course,
      userEnrollment,
      relatedCourses
    });
  } catch (error) {
    console.error('Get course details error:', error);
    res.status(500).json({ error: 'Failed to fetch course details' });
  }
});

// 報名課程
router.post('/:id/enroll', authenticate, async (req, res) => {
  try {
    const courseId = req.params.id;
    const userId = req.user._id;

    // 檢查課程是否存在
    const course = await Course.findOne({
      _id: courseId,
      status: 'published'
    });

    if (!course) {
      return res.status(404).json({ error: 'Course not found' });
    }

    // 檢查會員權限
    if (!course.pricing.free && course.pricing.membershipRequired) {
      const membershipLevels = { basic: 1, premium: 2, vip: 3 };
      const userLevel = membershipLevels[req.user.membership.plan];
      const requiredLevel = membershipLevels[course.pricing.membershipRequired];

      if (userLevel < requiredLevel) {
        return res.status(403).json({ 
          error: 'Membership upgrade required',
          required: course.pricing.membershipRequired,
          current: req.user.membership.plan
        });
      }
    }

    // 檢查先修課程
    if (course.prerequisites && course.prerequisites.length > 0) {
      const user = await User.findById(userId);
      const completedCourseIds = user.progress.coursesCompleted.map(id => id.toString());
      const missingPrerequisites = course.prerequisites.filter(
        prereq => !completedCourseIds.includes(prereq._id.toString())
      );

      if (missingPrerequisites.length > 0) {
        return res.status(400).json({ 
          error: 'Prerequisites not met',
          missing: missingPrerequisites
        });
      }
    }

    // 檢查是否已報名
    const user = await User.findById(userId);
    if (user.progress.currentCourses.includes(courseId)) {
      return res.status(400).json({ error: 'Already enrolled in this course' });
    }

    if (user.progress.coursesCompleted.includes(courseId)) {
      return res.status(400).json({ error: 'Course already completed' });
    }

    // 報名課程
    await User.findByIdAndUpdate(userId, {
      $addToSet: { 'progress.currentCourses': courseId },
      $set: { 'progress.lastActiveDate': new Date() }
    });

    // 更新課程統計
    await Course.findByIdAndUpdate(courseId, {
      $inc: { 'statistics.enrollments': 1 }
    });

    res.json({ 
      message: 'Successfully enrolled in course',
      courseId: courseId,
      enrollmentDate: new Date()
    });
  } catch (error) {
    console.error('Enroll course error:', error);
    res.status(500).json({ error: 'Failed to enroll in course' });
  }
});

// 退課
router.delete('/:id/unenroll', authenticate, async (req, res) => {
  try {
    const courseId = req.params.id;
    const userId = req.user._id;

    const user = await User.findById(userId);
    if (!user.progress.currentCourses.includes(courseId)) {
      return res.status(400).json({ error: 'Not enrolled in this course' });
    }

    // 移除課程
    await User.findByIdAndUpdate(userId, {
      $pull: { 'progress.currentCourses': courseId }
    });

    // 更新課程統計
    await Course.findByIdAndUpdate(courseId, {
      $inc: { 'statistics.enrollments': -1 }
    });

    res.json({ message: 'Successfully unenrolled from course' });
  } catch (error) {
    console.error('Unenroll course error:', error);
    res.status(500).json({ error: 'Failed to unenroll from course' });
  }
});

// 標記課程完成
router.post('/:id/complete', authenticate, async (req, res) => {
  try {
    const courseId = req.params.id;
    const userId = req.user._id;

    const user = await User.findById(userId);
    if (!user.progress.currentCourses.includes(courseId)) {
      return res.status(400).json({ error: 'Not enrolled in this course' });
    }

    if (user.progress.coursesCompleted.includes(courseId)) {
      return res.status(400).json({ error: 'Course already completed' });
    }

    // 標記完成
    await User.findByIdAndUpdate(userId, {
      $pull: { 'progress.currentCourses': courseId },
      $addToSet: { 'progress.coursesCompleted': courseId },
      $set: { 'progress.lastActiveDate': new Date() }
    });

    // 更新課程統計
    await Course.findByIdAndUpdate(courseId, {
      $inc: { 'statistics.completions': 1 }
    });

    const course = await Course.findById(courseId).select('title category duration');

    res.json({ 
      message: 'Course completed successfully',
      course: {
        id: course._id,
        title: course.title,
        category: course.category,
        completionDate: new Date()
      },
      certificateAvailable: true // 可以根據課程設定決定
    });
  } catch (error) {
    console.error('Complete course error:', error);
    res.status(500).json({ error: 'Failed to mark course as completed' });
  }
});

// 獲取課程進度
router.get('/:id/progress', authenticate, async (req, res) => {
  try {
    const courseId = req.params.id;
    const userId = req.user._id;

    const user = await User.findById(userId);
    const course = await Course.findById(courseId);

    if (!course) {
      return res.status(404).json({ error: 'Course not found' });
    }

    const isEnrolled = user.progress.currentCourses.includes(courseId);
    const isCompleted = user.progress.coursesCompleted.includes(courseId);

    if (!isEnrolled && !isCompleted) {
      return res.status(403).json({ error: 'Not enrolled in this course' });
    }

    // 模擬進度數據（實際應從詳細的進度追蹤系統獲取）
    const totalModules = course.content.modules.length;
    const completedModules = Math.floor(totalModules * (isCompleted ? 1 : Math.random() * 0.8));
    const progressPercentage = Math.round((completedModules / totalModules) * 100);

    const progress = {
      courseId: courseId,
      userId: userId,
      status: isCompleted ? 'completed' : 'in_progress',
      progress: {
        percentage: progressPercentage,
        completedModules: completedModules,
        totalModules: totalModules,
        lastAccessDate: user.progress.lastActiveDate,
        estimatedTimeRemaining: isCompleted ? 0 : Math.round((course.duration * (100 - progressPercentage)) / 100)
      },
      modules: course.content.modules.map((module, index) => ({
        id: index,
        title: module.title,
        completed: index < completedModules,
        lastAccessed: index < completedModules ? new Date() : null
      }))
    };

    res.json({
      message: 'Course progress retrieved successfully',
      progress
    });
  } catch (error) {
    console.error('Get course progress error:', error);
    res.status(500).json({ error: 'Failed to fetch course progress' });
  }
});

// 更新學習進度
router.put('/:id/progress', authenticate, [
  body('moduleId').isInt({ min: 0 }).withMessage('Valid module ID required'),
  body('completed').isBoolean().withMessage('Completed status must be boolean'),
  body('studyTime').optional().isInt({ min: 0 }).withMessage('Study time must be positive')
], handleValidationErrors, async (req, res) => {
  try {
    const courseId = req.params.id;
    const userId = req.user._id;
    const { moduleId, completed, studyTime = 0 } = req.body;

    const user = await User.findById(userId);
    if (!user.progress.currentCourses.includes(courseId)) {
      return res.status(403).json({ error: 'Not enrolled in this course' });
    }

    // 更新學習時間
    if (studyTime > 0) {
      await User.findByIdAndUpdate(userId, {
        $inc: { 'progress.totalStudyTime': studyTime },
        $set: { 'progress.lastActiveDate': new Date() }
      });
    }

    // 這裡應該更新詳細的模組進度（需要更複雜的數據結構）
    // 暫時返回成功訊息

    res.json({
      message: 'Progress updated successfully',
      moduleId: moduleId,
      completed: completed,
      studyTime: studyTime,
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Update progress error:', error);
    res.status(500).json({ error: 'Failed to update progress' });
  }
});

// 課程評價
router.post('/:id/review', authenticate, [
  body('rating').isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
  body('comment').optional().isLength({ max: 1000 }).withMessage('Comment must be less than 1000 characters')
], handleValidationErrors, async (req, res) => {
  try {
    const courseId = req.params.id;
    const userId = req.user._id;
    const { rating, comment } = req.body;

    const user = await User.findById(userId);
    
    // 只有完成課程的學員可以評價
    if (!user.progress.coursesCompleted.includes(courseId)) {
      return res.status(403).json({ error: 'Must complete course to leave a review' });
    }

    // 檢查是否已評價（這裡簡化處理，實際應該有評價模型）
    // 暫時直接更新課程評分

    const course = await Course.findById(courseId);
    if (!course) {
      return res.status(404).json({ error: 'Course not found' });
    }

    // 簡化的評分更新邏輯
    const currentAverage = course.statistics.averageRating || 0;
    const currentCount = course.statistics.reviews.length || 0;
    const newAverage = ((currentAverage * currentCount) + rating) / (currentCount + 1);

    await Course.findByIdAndUpdate(courseId, {
      $set: { 'statistics.averageRating': Math.round(newAverage * 10) / 10 },
      $inc: { 'statistics.reviews': 1 }
    });

    res.json({
      message: 'Review submitted successfully',
      review: {
        courseId: courseId,
        userId: userId,
        rating: rating,
        comment: comment,
        timestamp: new Date()
      }
    });
  } catch (error) {
    console.error('Submit review error:', error);
    res.status(500).json({ error: 'Failed to submit review' });
  }
});

// 獲取用戶的課程列表
router.get('/my/enrolled', authenticate, async (req, res) => {
  try {
    const { status = 'all' } = req.query;
    const userId = req.user._id;

    const user = await User.findById(userId)
      .populate('progress.currentCourses', 'title category difficulty duration instructor statistics')
      .populate('progress.coursesCompleted', 'title category difficulty duration instructor statistics');

    let courses = [];
    
    if (status === 'current' || status === 'all') {
      courses = courses.concat(user.progress.currentCourses.map(course => ({
        ...course.toObject(),
        enrollmentStatus: 'current'
      })));
    }
    
    if (status === 'completed' || status === 'all') {
      courses = courses.concat(user.progress.coursesCompleted.map(course => ({
        ...course.toObject(),
        enrollmentStatus: 'completed'
      })));
    }

    res.json({
      message: 'User courses retrieved successfully',
      courses: courses,
      summary: {
        current: user.progress.currentCourses.length,
        completed: user.progress.coursesCompleted.length,
        total: user.progress.currentCourses.length + user.progress.coursesCompleted.length
      }
    });
  } catch (error) {
    console.error('Get user courses error:', error);
    res.status(500).json({ error: 'Failed to fetch user courses' });
  }
});

// 搜尋課程
router.get('/search/advanced', async (req, res) => {
  try {
    const {
      q,
      categories,
      difficulties,
      durations,
      instructors,
      tags,
      rating,
      page = 1,
      limit = 20,
      sort = 'relevance'
    } = req.query;

    const skip = (page - 1) * limit;
    const query = { status: 'published' };

    // 文本搜尋
    if (q) {
      query.$text = { $search: q };
    }

    // 多選篩選
    if (categories) {
      const categoryArray = categories.split(',');
      query.category = { $in: categoryArray };
    }

    if (difficulties) {
      const difficultyArray = difficulties.split(',');
      query.difficulty = { $in: difficultyArray };
    }

    if (instructors) {
      const instructorArray = instructors.split(',');
      query.instructor = { $in: instructorArray };
    }

    if (tags) {
      const tagArray = tags.split(',');
      query.tags = { $in: tagArray };
    }

    // 數值範圍篩選
    if (durations) {
      const [min, max] = durations.split('-').map(Number);
      query.duration = { $gte: min, $lte: max };
    }

    if (rating) {
      query['statistics.averageRating'] = { $gte: parseFloat(rating) };
    }

    // 排序邏輯
    let sortOption = {};
    switch (sort) {
      case 'relevance':
        sortOption = q ? { score: { $meta: 'textScore' } } : { created: -1 };
        break;
      case 'newest':
        sortOption = { created: -1 };
        break;
      case 'oldest':
        sortOption = { created: 1 };
        break;
      case 'popular':
        sortOption = { 'statistics.enrollments': -1 };
        break;
      case 'rating':
        sortOption = { 'statistics.averageRating': -1 };
        break;
      case 'duration_asc':
        sortOption = { duration: 1 };
        break;
      case 'duration_desc':
        sortOption = { duration: -1 };
        break;
      default:
        sortOption = { created: -1 };
    }

    const courses = await Course.find(query)
      .sort(sortOption)
      .skip(skip)
      .limit(parseInt(limit))
      .populate('instructor', 'profile.firstName profile.lastName profile.avatar')
      .select('-content.modules');

    const total = await Course.countDocuments(query);

    res.json({
      message: 'Search completed successfully',
      query: { q, categories, difficulties, durations, instructors, tags, rating },
      courses,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Advanced search error:', error);
    res.status(500).json({ error: 'Failed to perform search' });
  }
});

module.exports = router;