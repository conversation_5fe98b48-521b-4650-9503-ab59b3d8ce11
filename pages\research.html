<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最新研究 - 澄源閱讀</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f7ff;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 頁面標題區域 */
        .page-header {
            background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
            color: white;
            padding: 50px 30px;
            text-align: center;
            border-radius: 20px;
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="25" cy="25" r="2" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="3" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/></svg>');
            animation: scientific-drift 35s linear infinite;
        }

        @keyframes scientific-drift {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .page-header-content {
            position: relative;
            z-index: 2;
        }

        .page-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            animation: research-pulse 3s infinite;
        }

        @keyframes research-pulse {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.1) rotate(5deg); }
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .page-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        /* 篩選區域 */
        .research-filters {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .filter-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }

        .filter-tab {
            padding: 10px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            background: white;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .filter-tab.active {
            background: linear-gradient(135deg, #6c5ce7, #a29bfe);
            color: white;
            border-color: #6c5ce7;
        }

        .filter-tab:hover:not(.active) {
            border-color: #6c5ce7;
            color: #6c5ce7;
        }

        .search-row {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .search-input {
            flex: 1;
            padding: 12px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #6c5ce7;
        }

        .sort-select {
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            background: white;
            min-width: 150px;
        }

        /* 研究文章網格 */
        .research-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .research-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .research-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .research-header {
            background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
            color: white;
            padding: 20px;
            position: relative;
        }

        .research-type {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255,255,255,0.2);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            backdrop-filter: blur(10px);
        }

        .research-institution {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .research-title {
            font-size: 1.3rem;
            font-weight: 600;
            line-height: 1.4;
            margin-bottom: 10px;
        }

        .research-authors {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .research-body {
            padding: 25px;
        }

        .research-abstract {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
            display: -webkit-box;
            -webkit-line-clamp: 4;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .research-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 0.8rem;
            color: #999;
        }

        .research-date {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .research-impact {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .impact-score {
            display: flex;
            align-items: center;
            gap: 3px;
            color: #ff6b35;
            font-weight: 500;
        }

        .research-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .research-tag {
            background: #f0f4ff;
            color: #6c5ce7;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        /* 特色研究 */
        .featured-research {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .featured-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 25px;
        }

        .featured-item {
            background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
            border-radius: 12px;
            padding: 25px;
            border-left: 4px solid #6c5ce7;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .featured-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(108, 92, 231, 0.2);
        }

        .featured-icon {
            font-size: 2rem;
            color: #6c5ce7;
            margin-bottom: 15px;
        }

        .featured-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }

        .featured-description {
            color: #666;
            line-height: 1.5;
            margin-bottom: 10px;
        }

        .featured-source {
            font-size: 0.8rem;
            color: #6c5ce7;
            font-weight: 500;
        }

        /* 統計儀表板 */
        .stats-dashboard {
            background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
        }

        .stats-dashboard::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect x="10" y="10" width="2" height="2" fill="white" opacity="0.1"/><rect x="80" y="20" width="1" height="1" fill="white" opacity="0.1"/><rect x="30" y="80" width="1.5" height="1.5" fill="white" opacity="0.1"/></svg>');
            animation: stats-pattern 40s linear infinite;
        }

        @keyframes stats-pattern {
            0% { transform: translate(0, 0); }
            100% { transform: translate(-50px, -50px); }
        }

        .stats-content {
            position: relative;
            z-index: 2;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 25px;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 25px;
            color: #333;
            position: relative;
            padding-left: 20px;
        }

        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 30px;
            background: linear-gradient(135deg, #6c5ce7, #a29bfe);
            border-radius: 2px;
        }

        /* 研究工具 */
        .research-tools {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 25px;
        }

        .tool-item {
            text-align: center;
            padding: 20px;
            border: 2px solid #f0f4ff;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tool-item:hover {
            border-color: #6c5ce7;
            background: #f8f9ff;
            transform: translateY(-2px);
        }

        .tool-icon {
            font-size: 2rem;
            color: #6c5ce7;
            margin-bottom: 15px;
        }

        .tool-name {
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }

        .tool-description {
            font-size: 0.8rem;
            color: #666;
            line-height: 1.4;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .page-header {
                padding: 40px 20px;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .research-grid {
                grid-template-columns: 1fr;
            }
            
            .filter-tabs {
                flex-direction: column;
            }
            
            .search-row {
                flex-direction: column;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .page-title {
                font-size: 1.5rem;
            }
            
            .section-title {
                font-size: 1.5rem;
            }
            
            .research-card,
            .featured-research,
            .stats-dashboard,
            .research-tools {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 頁面標題 -->
        <header class="page-header">
            <div class="page-header-content">
                <div class="page-icon">
                    <i class="fas fa-microscope"></i>
                </div>
                <h1 class="page-title">最新研究</h1>
                <p class="page-subtitle">關注學術前沿，分享最新研究成果<br>以科學方法驗證古老智慧，探索身心靈健康的奧秘</p>
            </div>
        </header>

        <!-- 統計儀表板 -->
        <section class="stats-dashboard">
            <div class="stats-content">
                <h2 class="section-title" style="color: white; padding-left: 0;">
                    <i class="fas fa-chart-line"></i>
                    研究統計
                </h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number" data-target="156">0</span>
                        <span class="stat-label">研究論文</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" data-target="23">0</span>
                        <span class="stat-label">頂級期刊</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" data-target="89">0</span>
                        <span class="stat-label">研究機構</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" data-target="12">0</span>
                        <span class="stat-label">本月新增</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 篩選區域 -->
        <section class="research-filters">
            <div class="filter-tabs">
                <div class="filter-tab active" data-category="all">全部研究</div>
                <div class="filter-tab" data-category="neuroscience">神經科學</div>
                <div class="filter-tab" data-category="psychology">心理學</div>
                <div class="filter-tab" data-category="meditation">冥想研究</div>
                <div class="filter-tab" data-category="healthcare">醫療健康</div>
                <div class="filter-tab" data-category="cognitive">認知科學</div>
            </div>
            <div class="search-row">
                <input type="text" class="search-input" placeholder="搜索研究主題、作者或關鍵詞..." id="search-input">
                <select class="sort-select" id="sort-select">
                    <option value="date">按發布時間</option>
                    <option value="impact">按影響因子</option>
                    <option value="citations">按引用次數</option>
                    <option value="relevance">按相關度</option>
                </select>
            </div>
        </section>

        <!-- 特色研究亮點 -->
        <section class="featured-research">
            <h2 class="section-title">
                <i class="fas fa-star"></i>
                本月研究亮點
            </h2>
            <div class="featured-grid">
                <div class="featured-item" data-research="highlight1">
                    <div class="featured-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3 class="featured-title">冥想改變大腦結構的最新發現</h3>
                    <p class="featured-description">哈佛大學最新研究顯示，僅8週的正念冥想練習就能改變大腦海馬體結構，提升記憶力和情緒調節能力。</p>
                    <div class="featured-source">Harvard Medical School</div>
                </div>
                
                <div class="featured-item" data-research="highlight2">
                    <div class="featured-icon">
                        <i class="fas fa-heart-pulse"></i>
                    </div>
                    <h3 class="featured-title">慈悲冥想對心血管健康的影響</h3>
                    <p class="featured-description">史丹佛大學研究發現，慈悲冥想能顯著降低血壓和心率變異性，改善整體心血管健康指標。</p>
                    <div class="featured-source">Stanford University</div>
                </div>
                
                <div class="featured-item" data-research="highlight3">
                    <div class="featured-icon">
                        <i class="fas fa-dna"></i>
                    </div>
                    <h3 class="featured-title">正念練習對基因表達的影響</h3>
                    <p class="featured-description">威斯康辛大學研究團隊發現，長期正念練習能調節炎症相關基因的表達，增強免疫系統功能。</p>
                    <div class="featured-source">University of Wisconsin</div>
                </div>
            </div>
        </section>

        <!-- 研究文章網格 -->
        <section class="research-articles">
            <h2 class="section-title">
                <i class="fas fa-file-alt"></i>
                最新研究論文
            </h2>
            <div class="research-grid" id="research-grid">
                <!-- 研究文章將通過 JavaScript 動態載入 -->
            </div>
        </section>

        <!-- 研究工具 -->
        <section class="research-tools">
            <h2 class="section-title">
                <i class="fas fa-tools"></i>
                研究工具與資源
            </h2>
            <div class="tools-grid">
                <div class="tool-item" data-tool="search">
                    <div class="tool-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3 class="tool-name">學術搜索</h3>
                    <p class="tool-description">快速搜索相關領域的最新研究論文</p>
                </div>
                
                <div class="tool-item" data-tool="calculator">
                    <div class="tool-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <h3 class="tool-name">統計計算器</h3>
                    <p class="tool-description">研究數據統計分析工具</p>
                </div>
                
                <div class="tool-item" data-tool="reference">
                    <div class="tool-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <h3 class="tool-name">文獻管理</h3>
                    <p class="tool-description">整理和管理研究文獻引用</p>
                </div>
                
                <div class="tool-item" data-tool="collaboration">
                    <div class="tool-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="tool-name">學者網絡</h3>
                    <p class="tool-description">連接全球相關領域研究者</p>
                </div>
            </div>
        </section>
    </div>

    <script>
        // 研究數據
        const researchData = [
            {
                id: 1,
                title: "Mindfulness-Based Stress Reduction Effects on Neural Networks",
                institution: "Johns Hopkins University",
                authors: "Dr. Sarah Chen, Dr. Michael Rodriguez",
                abstract: "This study examines the neuroplastic changes induced by an 8-week mindfulness-based stress reduction program. Using fMRI imaging, we observed significant increases in grey matter density in the hippocampus and decreased amygdala reactivity to stress stimuli.",
                category: "neuroscience",
                type: "原創研究",
                date: "2025-01-08",
                impact: 8.5,
                citations: 234,
                tags: ["正念", "神經可塑性", "壓力管理", "腦部影像"]
            },
            {
                id: 2,
                title: "Compassion Meditation and Inflammatory Markers: A Randomized Trial",
                institution: "University of California, San Francisco",
                authors: "Dr. Lisa Wong, Dr. James Patterson",
                abstract: "A randomized controlled trial investigating the effects of compassion meditation on inflammatory biomarkers. Results show significant reductions in IL-6 and TNF-α levels after 12 weeks of practice.",
                category: "healthcare",
                type: "隨機對照試驗",
                date: "2025-01-05",
                impact: 9.2,
                citations: 189,
                tags: ["慈悲冥想", "炎症標記", "免疫系統", "隨機試驗"]
            },
            {
                id: 3,
                title: "Cognitive Benefits of Tibetan Buddhist Meditation Practices",
                institution: "Oxford University",
                authors: "Dr. Emma Thompson, Dr. David Kumar",
                abstract: "This longitudinal study tracks cognitive improvements in practitioners of Tibetan Buddhist meditation over 2 years, showing enhanced working memory, attention regulation, and emotional intelligence.",
                category: "cognitive",
                type: "縱向研究",
                date: "2025-01-02",
                impact: 7.8,
                citations: 156,
                tags: ["藏傳佛教", "認知能力", "注意力", "情緒智能"]
            },
            {
                id: 4,
                title: "Breath-Based Interventions for Anxiety Disorders: Meta-Analysis",
                institution: "Yale University",
                authors: "Dr. Robert Kim, Dr. Maria Santos",
                abstract: "A comprehensive meta-analysis of 47 studies examining breath-based interventions for anxiety disorders, demonstrating significant efficacy across diverse populations and anxiety subtypes.",
                category: "psychology",
                type: "薈萃分析",
                date: "2024-12-28",
                impact: 8.9,
                citations: 298,
                tags: ["呼吸練習", "焦慮症", "薈萃分析", "心理治療"]
            },
            {
                id: 5,
                title: "Neural Correlates of Loving-Kindness Meditation",
                institution: "MIT",
                authors: "Dr. Amanda Foster, Dr. Kevin Liu",
                abstract: "Using advanced neuroimaging techniques, this study maps the neural networks activated during loving-kindness meditation, revealing increased connectivity in empathy-related brain regions.",
                category: "neuroscience",
                type: "神經影像研究",
                date: "2024-12-25",
                impact: 8.1,
                citations: 167,
                tags: ["慈心冥想", "神經網絡", "同理心", "腦部連接"]
            },
            {
                id: 6,
                title: "Mindful Eating and Metabolic Health: Clinical Trial Results",
                institution: "Mayo Clinic",
                authors: "Dr. Jennifer Brown, Dr. Mark Wilson",
                abstract: "A 16-week clinical trial investigating mindful eating practices in diabetic patients, showing improved glucose control, weight management, and eating behavior patterns.",
                category: "healthcare",
                type: "臨床試驗",
                date: "2024-12-22",
                impact: 7.6,
                citations: 123,
                tags: ["正念飲食", "糖尿病", "代謝健康", "行為改變"]
            }
        ];

        let filteredData = [...researchData];
        let currentCategory = 'all';

        // 渲染研究文章
        function renderResearch() {
            const grid = document.getElementById('research-grid');
            
            grid.innerHTML = filteredData.map(research => `
                <div class="research-card" data-research="${research.id}">
                    <div class="research-header">
                        <div class="research-type">${research.type}</div>
                        <div class="research-institution">${research.institution}</div>
                        <h3 class="research-title">${research.title}</h3>
                        <div class="research-authors">${research.authors}</div>
                    </div>
                    <div class="research-body">
                        <div class="research-meta">
                            <span class="research-date">
                                <i class="fas fa-calendar"></i>
                                ${research.date}
                            </span>
                            <div class="research-impact">
                                <span class="impact-score">
                                    <i class="fas fa-star"></i>
                                    ${research.impact}
                                </span>
                                <span>
                                    <i class="fas fa-quote-left"></i>
                                    ${research.citations}
                                </span>
                            </div>
                        </div>
                        <p class="research-abstract">${research.abstract}</p>
                        <div class="research-tags">
                            ${research.tags.map(tag => `<span class="research-tag">${tag}</span>`).join('')}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 篩選功能
        function filterResearch(category) {
            currentCategory = category;
            
            if (category === 'all') {
                filteredData = [...researchData];
            } else {
                filteredData = researchData.filter(research => research.category === category);
            }
            
            renderResearch();
        }

        // 搜索功能
        function searchResearch(query) {
            if (!query.trim()) {
                filterResearch(currentCategory);
                return;
            }
            
            const baseData = currentCategory === 'all' ? researchData : 
                researchData.filter(research => research.category === currentCategory);
            
            filteredData = baseData.filter(research => 
                research.title.toLowerCase().includes(query.toLowerCase()) ||
                research.abstract.toLowerCase().includes(query.toLowerCase()) ||
                research.authors.toLowerCase().includes(query.toLowerCase()) ||
                research.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
            );
            
            renderResearch();
        }

        // 排序功能
        function sortResearch(sortBy) {
            filteredData.sort((a, b) => {
                switch(sortBy) {
                    case 'date':
                        return new Date(b.date) - new Date(a.date);
                    case 'impact':
                        return b.impact - a.impact;
                    case 'citations':
                        return b.citations - a.citations;
                    default:
                        return 0;
                }
            });
            
            renderResearch();
        }

        // 事件監聽
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                const category = this.getAttribute('data-category');
                filterResearch(category);
            });
        });

        document.getElementById('search-input').addEventListener('input', function(e) {
            searchResearch(e.target.value);
        });

        document.getElementById('sort-select').addEventListener('change', function(e) {
            sortResearch(e.target.value);
        });

        // 研究卡片點擊
        document.addEventListener('click', function(e) {
            const researchCard = e.target.closest('.research-card');
            if (researchCard) {
                const researchId = researchCard.getAttribute('data-research');
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'navigate',
                        page: 'article',
                        articleId: researchId,
                        category: 'research'
                    }, '*');
                }
            }
        });

        // 特色研究點擊
        document.querySelectorAll('.featured-item').forEach(item => {
            item.addEventListener('click', function() {
                const researchId = this.getAttribute('data-research');
                showNotification('正在載入特色研究詳情...');
            });
        });

        // 工具點擊
        document.querySelectorAll('.tool-item').forEach(tool => {
            tool.addEventListener('click', function() {
                const toolType = this.getAttribute('data-tool');
                const toolName = this.querySelector('.tool-name').textContent;
                
                switch(toolType) {
                    case 'search':
                        showAdvancedSearch();
                        break;
                    case 'calculator':
                        openStatCalculator();
                        break;
                    case 'reference':
                        openReferenceManager();
                        break;
                    case 'collaboration':
                        openScholarNetwork();
                        break;
                    default:
                        showNotification(`正在啟動${toolName}...`);
                }
            });
        });

        // 工具函數
        function showAdvancedSearch() {
            showNotification('高級搜索功能即將推出，敬請期待！');
        }

        function openStatCalculator() {
            showNotification('統計計算器正在載入中...');
        }

        function openReferenceManager() {
            showNotification('文獻管理工具開發中，即將上線！');
        }

        function openScholarNetwork() {
            showNotification('學者網絡功能正在建設中...');
        }

        // 統計動畫
        function animateStats() {
            const stats = document.querySelectorAll('.stat-number');
            
            stats.forEach(stat => {
                const target = parseInt(stat.getAttribute('data-target'));
                let current = 0;
                const increment = target / 50;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    stat.textContent = Math.floor(current);
                }, 40);
            });
        }

        // 通知函數
        function showNotification(message) {
            if (window.parent !== window) {
                window.parent.postMessage({
                    type: 'notification',
                    message: message,
                    level: 'info'
                }, '*');
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderResearch();
            
            // 啟動統計動畫
            setTimeout(animateStats, 500);
            
            // 添加進入動畫
            setTimeout(() => {
                document.querySelectorAll('.research-card').forEach((card, index) => {
                    setTimeout(() => {
                        card.style.opacity = '0';
                        card.style.transform = 'translateY(30px)';
                        card.style.transition = 'all 0.6s ease';
                        
                        setTimeout(() => {
                            card.style.opacity = '1';
                            card.style.transform = 'translateY(0)';
                        }, 100);
                    }, index * 150);
                });
            }, 800);
        });

        // 搜索框回車事件
        document.getElementById('search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchResearch(this.value);
            }
        });

        // 響應式卡片懸停效果
        document.querySelectorAll('.research-card, .featured-item, .tool-item').forEach(card => {
            card.addEventListener('mouseenter', function() {
                if (window.innerWidth > 768) {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                }
            });
            
            card.addEventListener('mouseleave', function() {
                if (window.innerWidth > 768) {
                    this.style.transform = 'translateY(0) scale(1)';
                }
            });
        });
    </script>
</body>
</html>