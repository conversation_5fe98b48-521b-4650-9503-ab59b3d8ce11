# Cloudflare Pages Headers 配置

# 全局安全標頭
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: geolocation=(), microphone=(), camera=()

# HTML 文件緩存
/*.html
  Cache-Control: public, max-age=300, s-maxage=600

# CSS 和 JS 文件緩存
/css/*
  Cache-Control: public, max-age=31536000, immutable
  
/js/*
  Cache-Control: public, max-age=31536000, immutable

# 圖片文件緩存
/assets/images/*
  Cache-Control: public, max-age=31536000, immutable
  
/assets/icons/*
  Cache-Control: public, max-age=31536000, immutable

# API 路由 CORS
/api/*
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization, X-Session-ID
  Access-Control-Max-Age: 86400

# 字體文件
/assets/fonts/*
  Cache-Control: public, max-age=31536000, immutable
  Access-Control-Allow-Origin: *

# Favicon
/favicon.ico
  Cache-Control: public, max-age=86400

# 安全文件
/robots.txt
  Cache-Control: public, max-age=3600
  
/sitemap.xml
  Cache-Control: public, max-age=3600