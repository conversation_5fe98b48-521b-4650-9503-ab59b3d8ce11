/**
 * 澄源閱讀 - 搜索 API
 * Cloudflare Workers 函數
 * 
 * 功能：
 * - 全文搜索文章
 * - 搜索建議
 * - 熱門搜索
 * - 搜索統計
 * - 搜索歷史記錄
 */

// CORS 處理
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Session-ID',
  'Access-Control-Max-Age': '86400',
};

// 處理 CORS 預檢請求
function handleCORS(request) {
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }
}

// 錯誤處理
function createErrorResponse(message, status = 400) {
  return new Response(JSON.stringify({
    success: false,
    error: message,
    timestamp: new Date().toISOString()
  }), {
    status,
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders
    }
  });
}

// 成功響應
function createSuccessResponse(data, meta = {}) {
  return new Response(JSON.stringify({
    success: true,
    data,
    meta,
    timestamp: new Date().toISOString()
  }), {
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders
    }
  });
}

// 獲取客戶端IP
function getClientIP(request) {
  return request.headers.get('CF-Connecting-IP') || 
         request.headers.get('X-Forwarded-For') || 
         '127.0.0.1';
}

// 獲取會話ID
function getSessionId(request) {
  return request.headers.get('X-Session-ID') || 
         crypto.randomUUID();
}

// 主處理函數
export default {
  async fetch(request, env, ctx) {
    // 處理 CORS
    const corsResponse = handleCORS(request);
    if (corsResponse) return corsResponse;

    try {
      const url = new URL(request.url);
      const path = url.pathname;
      const method = request.method;

      // 路由處理
      if (path === '/api/search' && method === 'GET') {
        return await searchArticles(request, env);
      }
      
      if (path === '/api/search' && method === 'POST') {
        return await searchArticles(request, env);
      }
      
      if (path === '/api/search/suggestions' && method === 'GET') {
        return await getSearchSuggestions(request, env);
      }
      
      if (path === '/api/search/popular' && method === 'GET') {
        return await getPopularSearches(request, env);
      }
      
      if (path === '/api/search/history' && method === 'GET') {
        return await getSearchHistory(request, env);
      }
      
      if (path === '/api/search/stats' && method === 'GET') {
        return await getSearchStats(request, env);
      }

      return createErrorResponse('Search API endpoint not found', 404);

    } catch (error) {
      console.error('Search API Error:', error);
      return createErrorResponse('Internal server error', 500);
    }
  }
};

// 搜索文章
async function searchArticles(request, env) {
  let query, category, sort, page, limit;

  if (request.method === 'POST') {
    const body = await request.json();
    query = body.query;
    category = body.category;
    sort = body.sort;
    page = body.page;
    limit = body.limit;
  } else {
    const url = new URL(request.url);
    query = url.searchParams.get('q') || url.searchParams.get('query');
    category = url.searchParams.get('category');
    sort = url.searchParams.get('sort') || 'relevance';
    page = parseInt(url.searchParams.get('page')) || 1;
    limit = Math.min(parseInt(url.searchParams.get('limit')) || 12, 50);
  }

  if (!query || query.trim().length < 2) {
    return createErrorResponse('Search query must be at least 2 characters long');
  }

  const sessionId = getSessionId(request);
  const clientIP = getClientIP(request);
  const offset = (page - 1) * limit;

  try {
    // 記錄搜索日誌
    await recordSearchLog(env, query, sessionId, clientIP, request);

    // 構建搜索條件
    const searchTerms = query.trim().toLowerCase().split(/\s+/);
    let whereConditions = ['status = ?'];
    let params = ['published'];

    if (category) {
      whereConditions.push('category = ?');
      params.push(category);
    }

    // 構建搜索匹配條件（模糊匹配）
    const searchConditions = searchTerms.map(() => `
      (LOWER(title) LIKE ? OR 
       LOWER(content) LIKE ? OR 
       LOWER(excerpt) LIKE ? OR 
       LOWER(tags) LIKE ? OR 
       LOWER(author) LIKE ?)
    `).join(' AND ');

    whereConditions.push(`(${searchConditions})`);

    // 為每個搜索詞添加參數
    searchTerms.forEach(term => {
      const wildcard = `%${term}%`;
      params.push(wildcard, wildcard, wildcard, wildcard, wildcard);
    });

    const whereClause = whereConditions.join(' AND ');

    // 獲取搜索結果總數
    const countQuery = `SELECT COUNT(*) as total FROM articles WHERE ${whereClause}`;
    const countResult = await env.DB.prepare(countQuery).bind(...params).first();
    const total = countResult.total;

    // 構建排序條件
    let orderBy = 'published_at DESC';
    switch (sort) {
      case 'relevance':
        // 簡單的相關性計算：標題匹配權重更高
        orderBy = `
          (CASE 
            WHEN LOWER(title) LIKE ? THEN 3
            WHEN LOWER(excerpt) LIKE ? THEN 2
            ELSE 1
          END) DESC, 
          views DESC, 
          published_at DESC
        `;
        params.push(`%${query.toLowerCase()}%`, `%${query.toLowerCase()}%`);
        break;
      case 'date':
        orderBy = 'published_at DESC';
        break;
      case 'views':
        orderBy = 'views DESC, published_at DESC';
        break;
      case 'likes':
        orderBy = 'likes DESC, published_at DESC';
        break;
    }

    // 獲取搜索結果
    const searchQuery = `
      SELECT 
        id, title, excerpt, category, author, author_avatar,
        published_at, views, likes, reading_time, difficulty,
        featured_image, featured_image_alt, tags, slug
      FROM articles 
      WHERE ${whereClause}
      ORDER BY ${orderBy}
      LIMIT ? OFFSET ?
    `;

    const results = await env.DB.prepare(searchQuery)
      .bind(...params, limit, offset)
      .all();

    // 處理搜索結果，高亮關鍵詞
    const processedResults = results.results.map(article => {
      const highlighted = highlightSearchTerms(article, searchTerms);
      return {
        ...highlighted,
        tags: article.tags ? JSON.parse(article.tags) : [],
        published_at: new Date(article.published_at).toISOString(),
        category_name: getCategoryName(article.category)
      };
    });

    // 更新搜索日誌中的結果數量
    await updateSearchResults(env, sessionId, query, total);

    const totalPages = Math.ceil(total / limit);

    return createSuccessResponse(processedResults, {
      search: {
        query,
        category,
        sort,
        total,
        showing: processedResults.length
      },
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Search articles error:', error);
    return createErrorResponse('Search failed');
  }
}

// 獲取搜索建議
async function getSearchSuggestions(request, env) {
  const url = new URL(request.url);
  const query = url.searchParams.get('q') || url.searchParams.get('query');
  const limit = Math.min(parseInt(url.searchParams.get('limit')) || 10, 20);

  if (!query || query.trim().length < 2) {
    return createSuccessResponse([]);
  }

  try {
    const searchTerm = `%${query.toLowerCase()}%`;

    // 從文章標題和標籤中獲取建議
    const titleSuggestions = await env.DB.prepare(`
      SELECT DISTINCT title as suggestion, 'title' as type, views
      FROM articles 
      WHERE LOWER(title) LIKE ? AND status = 'published'
      ORDER BY views DESC
      LIMIT ?
    `).bind(searchTerm, Math.ceil(limit / 2)).all();

    // 從標籤中獲取建議
    const tagSuggestions = await env.DB.prepare(`
      SELECT DISTINCT name as suggestion, 'tag' as type, usage_count as views
      FROM tags 
      WHERE LOWER(name) LIKE ?
      ORDER BY usage_count DESC
      LIMIT ?
    `).bind(searchTerm, Math.ceil(limit / 2)).all();

    // 從熱門搜索中獲取建議
    const popularSuggestions = await env.DB.prepare(`
      SELECT query as suggestion, 'popular' as type, COUNT(*) as views
      FROM search_logs 
      WHERE LOWER(query) LIKE ? 
        AND created_at > datetime('now', '-30 days')
      GROUP BY query
      ORDER BY COUNT(*) DESC
      LIMIT ?
    `).bind(searchTerm, Math.ceil(limit / 3)).all();

    // 合併和去重建議
    const allSuggestions = [
      ...titleSuggestions.results,
      ...tagSuggestions.results,
      ...popularSuggestions.results
    ];

    const uniqueSuggestions = Array.from(
      new Map(allSuggestions.map(item => [item.suggestion.toLowerCase(), item])).values()
    ).slice(0, limit);

    return createSuccessResponse(uniqueSuggestions);

  } catch (error) {
    console.error('Get search suggestions error:', error);
    return createErrorResponse('Failed to fetch suggestions');
  }
}

// 獲取熱門搜索
async function getPopularSearches(request, env) {
  const url = new URL(request.url);
  const limit = Math.min(parseInt(url.searchParams.get('limit')) || 10, 30);
  const timeRange = url.searchParams.get('timeRange') || '7'; // 天數

  try {
    const popularSearches = await env.DB.prepare(`
      SELECT 
        query,
        COUNT(*) as search_count,
        AVG(results_count) as avg_results,
        MAX(created_at) as last_searched
      FROM search_logs 
      WHERE created_at > datetime('now', '-${timeRange} days')
        AND results_count > 0
      GROUP BY LOWER(query)
      HAVING search_count > 1
      ORDER BY search_count DESC, last_searched DESC
      LIMIT ?
    `).bind(limit).all();

    const processedResults = popularSearches.results.map(item => ({
      query: item.query,
      searchCount: item.search_count,
      avgResults: Math.round(item.avg_results),
      lastSearched: new Date(item.last_searched).toISOString()
    }));

    return createSuccessResponse(processedResults);

  } catch (error) {
    console.error('Get popular searches error:', error);
    return createErrorResponse('Failed to fetch popular searches');
  }
}

// 獲取搜索歷史
async function getSearchHistory(request, env) {
  const sessionId = getSessionId(request);
  const url = new URL(request.url);
  const limit = Math.min(parseInt(url.searchParams.get('limit')) || 20, 50);

  try {
    const searchHistory = await env.DB.prepare(`
      SELECT DISTINCT query, MAX(created_at) as last_searched, MAX(results_count) as results_count
      FROM search_logs 
      WHERE session_id = ?
      GROUP BY LOWER(query)
      ORDER BY last_searched DESC
      LIMIT ?
    `).bind(sessionId, limit).all();

    const processedHistory = searchHistory.results.map(item => ({
      query: item.query,
      lastSearched: new Date(item.last_searched).toISOString(),
      resultsCount: item.results_count
    }));

    return createSuccessResponse(processedHistory);

  } catch (error) {
    console.error('Get search history error:', error);
    return createErrorResponse('Failed to fetch search history');
  }
}

// 獲取搜索統計
async function getSearchStats(request, env) {
  try {
    // 今日搜索統計
    const todayStats = await env.DB.prepare(`
      SELECT 
        COUNT(*) as total_searches,
        COUNT(DISTINCT session_id) as unique_users,
        COUNT(DISTINCT query) as unique_queries,
        AVG(results_count) as avg_results
      FROM search_logs 
      WHERE DATE(created_at) = DATE('now')
    `).first();

    // 熱門搜索類別
    const categoryStats = await env.DB.prepare(`
      SELECT 
        category,
        COUNT(*) as search_count
      FROM search_logs sl
      JOIN articles a ON sl.query LIKE '%' || a.title || '%'
      WHERE sl.created_at > datetime('now', '-7 days')
        AND a.status = 'published'
      GROUP BY category
      ORDER BY search_count DESC
      LIMIT 5
    `).all();

    // 搜索趨勢（過去7天）
    const trendStats = await env.DB.prepare(`
      SELECT 
        DATE(created_at) as search_date,
        COUNT(*) as search_count,
        COUNT(DISTINCT session_id) as unique_users
      FROM search_logs 
      WHERE created_at > datetime('now', '-7 days')
      GROUP BY DATE(created_at)
      ORDER BY search_date
    `).all();

    return createSuccessResponse({
      today: {
        totalSearches: todayStats.total_searches,
        uniqueUsers: todayStats.unique_users,
        uniqueQueries: todayStats.unique_queries,
        avgResults: Math.round(todayStats.avg_results || 0)
      },
      categories: categoryStats.results.map(cat => ({
        category: cat.category,
        categoryName: getCategoryName(cat.category),
        searchCount: cat.search_count
      })),
      trends: trendStats.results.map(trend => ({
        date: trend.search_date,
        searchCount: trend.search_count,
        uniqueUsers: trend.unique_users
      }))
    });

  } catch (error) {
    console.error('Get search stats error:', error);
    return createErrorResponse('Failed to fetch search statistics');
  }
}

// 記錄搜索日誌
async function recordSearchLog(env, query, sessionId, clientIP, request) {
  try {
    await env.DB.prepare(`
      INSERT INTO search_logs (query, session_id, ip_address, user_agent)
      VALUES (?, ?, ?, ?)
    `).bind(
      query,
      sessionId,
      clientIP,
      request.headers.get('User-Agent') || ''
    ).run();
  } catch (error) {
    console.error('Record search log error:', error);
  }
}

// 更新搜索結果數量
async function updateSearchResults(env, sessionId, query, resultsCount) {
  try {
    await env.DB.prepare(`
      UPDATE search_logs 
      SET results_count = ?
      WHERE session_id = ? AND query = ? AND results_count IS NULL
      ORDER BY created_at DESC
      LIMIT 1
    `).bind(resultsCount, sessionId, query).run();
  } catch (error) {
    console.error('Update search results error:', error);
  }
}

// 高亮搜索關鍵詞
function highlightSearchTerms(article, searchTerms) {
  const highlightedArticle = { ...article };
  
  // 高亮標題
  highlightedArticle.title = highlightText(article.title, searchTerms);
  
  // 高亮摘要
  highlightedArticle.excerpt = highlightText(article.excerpt, searchTerms);
  
  return highlightedArticle;
}

// 文本高亮函數
function highlightText(text, searchTerms) {
  if (!text) return text;
  
  let highlightedText = text;
  
  searchTerms.forEach(term => {
    const regex = new RegExp(`(${escapeRegExp(term)})`, 'gi');
    highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
  });
  
  return highlightedText;
}

// 轉義正則表達式特殊字符
function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// 輔助函數：獲取分類名稱
function getCategoryName(categorySlug) {
  const categoryNames = {
    'dharma': '佛學智慧',
    'healing': '身心療癒',
    'research': '最新研究',
    'meditation': '禪修指導',
    'philosophy': '佛教哲學',
    'practice': '修行方法'
  };
  return categoryNames[categorySlug] || categorySlug;
}