// ================================
// 🚀 PM2 生態系統配置
// 佛教身心療癒網站
// ================================

module.exports = {
  // ================================
  // 📱 應用配置
  // ================================
  apps: [{
    // 基本配置
    name: 'buddhist-healing-api',
    script: 'app.js',
    
    // 集群配置
    instances: process.env.NODE_ENV === 'production' ? 'max' : 1,
    exec_mode: 'cluster',
    
    // 環境變數
    env: {
      NODE_ENV: 'development',
      PORT: 3000,
      MONGODB_URI: 'mongodb://localhost:27017/buddhist_healing_dev',
      JWT_SECRET: 'development_secret_key'
    },
    
    env_staging: {
      NODE_ENV: 'staging',
      PORT: 3000,
      MONGODB_URI: 'mongodb://localhost:27017/buddhist_healing_staging'
    },
    
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000,
      MONGODB_URI: process.env.MONGODB_URI,
      JWT_SECRET: process.env.JWT_SECRET
    },
    
    // 日誌配置
    log_file: './logs/combined.log',
    out_file: './logs/out.log',
    error_file: './logs/error.log',
    time: true,
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    
    // 性能配置
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    
    // 自動重啟配置
    autorestart: true,
    max_restarts: 10,
    min_uptime: '10s',
    
    // 監控配置
    watch: process.env.NODE_ENV === 'development',
    watch_delay: 1000,
    ignore_watch: [
      'node_modules',
      'logs',
      'uploads',
      'temp',
      '.git'
    ],
    
    // 錯誤處理
    kill_timeout: 5000,
    wait_ready: true,
    listen_timeout: 8000,
    
    // 集群配置
    instance_var: 'INSTANCE_ID',
    
    // 進階配置
    merge_logs: true,
    combine_logs: true,
    source_map_support: true,
    
    // 自定義腳本
    post_update: ['npm install', 'echo "App updated"'],
    
    // 環境特定配置
    ...(process.env.NODE_ENV === 'production' && {
      // 生產環境特定配置
      min_uptime: '60s',
      max_restarts: 5,
      autorestart: true,
      watch: false
    }),
    
    ...(process.env.NODE_ENV === 'development' && {
      // 開發環境特定配置
      instances: 1,
      exec_mode: 'fork',
      watch: true,
      ignore_watch: [
        'node_modules',
        'logs',
        'uploads',
        '.git',
        '*.test.js',
        'test'
      ]
    })
  }],

  // ================================
  // 🚀 部署配置
  // ================================
  deploy: {
    // 生產環境部署
    production: {
      user: 'deploy',
      host: ['your-production-server.com'],
      ref: 'origin/main',
      repo: '**************:your-org/buddhist-healing-website.git',
      path: '/var/www/buddhist-healing',
      ssh_options: 'StrictHostKeyChecking=no',
      
      // 部署前腳本 (本地執行)
      'pre-deploy-local': [
        'echo "🚀 開始部署到生產環境..."',
        'git add .',
        'git commit -m "Auto commit before deploy" || true'
      ].join(' && '),
      
      // 部署前腳本 (服務器執行)
      'pre-deploy': [
        'echo "📦 準備部署環境..."',
        'git fetch --all'
      ].join(' && '),
      
      // 部署後腳本
      'post-deploy': [
        'echo "📦 安裝依賴..."',
        'npm install --only=production',
        'echo "🧪 執行健康檢查..."',
        'npm run health-check || true',
        'echo "🔄 重新載入 PM2..."',
        'pm2 reload ecosystem.config.js --env production',
        'echo "🏥 等待服務啟動..."',
        'sleep 10',
        'echo "✅ 部署完成！"'
      ].join(' && '),
      
      // 部署後腳本 (本地執行)
      'post-deploy-local': [
        'echo "🎉 生產環境部署成功！"',
        'echo "🌐 檢查網站: https://your-domain.com"'
      ].join(' && ')
    },

    // 預備環境部署
    staging: {
      user: 'deploy',
      host: ['your-staging-server.com'],
      ref: 'origin/develop',
      repo: '**************:your-org/buddhist-healing-website.git',
      path: '/var/www/buddhist-healing-staging',
      ssh_options: 'StrictHostKeyChecking=no',
      
      'pre-deploy-local': 'echo "🧪 部署到預備環境..."',
      'post-deploy': [
        'npm install',
        'npm run test || true',
        'pm2 reload ecosystem.config.js --env staging'
      ].join(' && ')
    }
  }
};

// ================================
// 📊 PM2+ 監控配置 (可選)
// ================================
if (process.env.PM2_PLUS_SECRET_KEY) {
  module.exports.apps[0].pmx = true;
  module.exports.apps[0].pm2_plus = {
    secret_key: process.env.PM2_PLUS_SECRET_KEY,
    public_key: process.env.PM2_PLUS_PUBLIC_KEY,
    machine_name: process.env.MACHINE_NAME || 'buddhist-healing-server'
  };
}