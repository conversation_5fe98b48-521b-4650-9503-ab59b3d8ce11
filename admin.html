<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>澄源閱讀 - 後台管理系統</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            background: #f8f9fc;
            color: #333;
            line-height: 1.6;
        }

        /* 側邊欄 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 260px;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar.collapsed .sidebar-header {
            padding: 20px 10px;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .sidebar.collapsed .logo {
            font-size: 1.2rem;
        }

        .logo-subtitle {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .sidebar.collapsed .logo-subtitle {
            display: none;
        }

        .sidebar-menu {
            list-style: none;
            padding: 20px 0;
        }

        .menu-item {
            margin-bottom: 5px;
        }

        .menu-link {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }

        .sidebar.collapsed .menu-link {
            padding: 15px;
            justify-content: center;
        }

        .menu-link:hover,
        .menu-link.active {
            background: rgba(255,255,255,0.1);
            border-right: 3px solid #ffd700;
        }

        .menu-icon {
            font-size: 1.1rem;
            margin-right: 12px;
            width: 20px;
            text-align: center;
        }

        .sidebar.collapsed .menu-icon {
            margin-right: 0;
        }

        .menu-text {
            font-weight: 500;
        }

        .sidebar.collapsed .menu-text {
            display: none;
        }

        .toggle-btn {
            position: absolute;
            top: 20px;
            right: -15px;
            background: white;
            color: #667eea;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 1001;
        }

        /* 主內容區 */
        .main-content {
            margin-left: 260px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .sidebar.collapsed + .main-content {
            margin-left: 70px;
        }

        .top-bar {
            background: white;
            padding: 20px 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #333;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .content-area {
            padding: 30px;
        }

        /* 統計卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--card-color, #667eea);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .stat-title {
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            background: var(--card-color, #667eea);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .stat-change {
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stat-change.positive {
            color: #28a745;
        }

        .stat-change.negative {
            color: #dc3545;
        }

        /* 表格樣式 */
        .data-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .table-header {
            padding: 20px 25px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
        }

        .table-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-warning {
            background: #ffc107;
            color: #333;
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-outline {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
        }

        .btn-outline:hover {
            background: #667eea;
            color: white;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            text-align: left;
            padding: 15px 25px;
            border-bottom: 1px solid #eee;
        }

        th {
            background: #f8f9fc;
            font-weight: 600;
            color: #555;
            font-size: 0.9rem;
        }

        td {
            color: #333;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-published {
            background: #d4edda;
            color: #155724;
        }

        .status-draft {
            background: #fff3cd;
            color: #856404;
        }

        .status-archived {
            background: #f8d7da;
            color: #721c24;
        }

        .action-btns {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .action-btn.edit {
            background: #e3f2fd;
            color: #1976d2;
        }

        .action-btn.edit:hover {
            background: #1976d2;
            color: white;
        }

        .action-btn.delete {
            background: #ffebee;
            color: #d32f2f;
        }

        .action-btn.delete:hover {
            background: #d32f2f;
            color: white;
        }

        /* 表單樣式 */
        .form-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-control.textarea {
            resize: vertical;
            min-height: 120px;
        }

        .form-control.editor {
            min-height: 300px;
        }

        select.form-control {
            cursor: pointer;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .sidebar.collapsed + .main-content {
                margin-left: 0;
            }

            .top-bar {
                padding: 15px 20px;
            }

            .content-area {
                padding: 20px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            th, td {
                padding: 10px 15px;
            }
        }

        /* 隱藏元素 */
        .hidden {
            display: none !important;
        }

        /* 載入動畫 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 通知樣式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 9999;
            transform: translateX(400px);
            transition: all 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: #28a745;
        }

        .notification.error {
            background: #dc3545;
        }

        .notification.warning {
            background: #ffc107;
            color: #333;
        }

        /* 圖表區域 */
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .chart-header {
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .chart-subtitle {
            color: #666;
            font-size: 0.9rem;
        }

        .chart-placeholder {
            height: 300px;
            background: linear-gradient(135deg, #f8f9fc 0%, #e9ecef 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <!-- 側邊欄 -->
    <div class="sidebar" id="sidebar">
        <button class="toggle-btn" onclick="toggleSidebar()">
            <i class="fas fa-chevron-left" id="toggle-icon"></i>
        </button>
        
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-lotus"></i>
                <span class="logo-text">澄源閱讀</span>
            </div>
            <div class="logo-subtitle">後台管理系統</div>
        </div>

        <ul class="sidebar-menu">
            <li class="menu-item">
                <a href="#dashboard" class="menu-link active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt menu-icon"></i>
                    <span class="menu-text">儀表板</span>
                </a>
            </li>
            <li class="menu-item">
                <a href="#articles" class="menu-link" data-section="articles">
                    <i class="fas fa-file-alt menu-icon"></i>
                    <span class="menu-text">文章管理</span>
                </a>
            </li>
            <li class="menu-item">
                <a href="#categories" class="menu-link" data-section="categories">
                    <i class="fas fa-tags menu-icon"></i>
                    <span class="menu-text">分類管理</span>
                </a>
            </li>
            <li class="menu-item">
                <a href="#users" class="menu-link" data-section="users">
                    <i class="fas fa-users menu-icon"></i>
                    <span class="menu-text">用戶管理</span>
                </a>
            </li>
            <li class="menu-item">
                <a href="#comments" class="menu-link" data-section="comments">
                    <i class="fas fa-comments menu-icon"></i>
                    <span class="menu-text">評論管理</span>
                </a>
            </li>
            <li class="menu-item">
                <a href="#media" class="menu-link" data-section="media">
                    <i class="fas fa-images menu-icon"></i>
                    <span class="menu-text">媒體庫</span>
                </a>
            </li>
            <li class="menu-item">
                <a href="#analytics" class="menu-link" data-section="analytics">
                    <i class="fas fa-chart-bar menu-icon"></i>
                    <span class="menu-text">數據分析</span>
                </a>
            </li>
            <li class="menu-item">
                <a href="#settings" class="menu-link" data-section="settings">
                    <i class="fas fa-cog menu-icon"></i>
                    <span class="menu-text">系統設置</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- 主內容區 -->
    <div class="main-content">
        <!-- 頂部導航 -->
        <div class="top-bar">
            <h1 class="page-title" id="page-title">儀表板</h1>
            <div class="user-info">
                <span id="current-time"></span>
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <span>管理員</span>
            </div>
        </div>

        <!-- 內容區域 -->
        <div class="content-area" id="content-area">
            <!-- 儀表板內容 -->
            <div id="dashboard-content" class="content-section">
                <!-- 統計卡片 -->
                <div class="stats-grid">
                    <div class="stat-card" style="--card-color: #667eea">
                        <div class="stat-header">
                            <span class="stat-title">總文章數</span>
                            <div class="stat-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="total-articles">256</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+12 本月</span>
                        </div>
                    </div>

                    <div class="stat-card" style="--card-color: #28a745">
                        <div class="stat-header">
                            <span class="stat-title">總瀏覽量</span>
                            <div class="stat-icon">
                                <i class="fas fa-eye"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="total-views">45,672</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+8.5% 本週</span>
                        </div>
                    </div>

                    <div class="stat-card" style="--card-color: #ffc107">
                        <div class="stat-header">
                            <span class="stat-title">活躍用戶</span>
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="active-users">1,234</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+156 本週</span>
                        </div>
                    </div>

                    <div class="stat-card" style="--card-color: #dc3545">
                        <div class="stat-header">
                            <span class="stat-title">待審評論</span>
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="pending-comments">23</div>
                        <div class="stat-change negative">
                            <i class="fas fa-arrow-down"></i>
                            <span>需要處理</span>
                        </div>
                    </div>
                </div>

                <!-- 圖表區域 -->
                <div class="chart-container">
                    <div class="chart-header">
                        <h3 class="chart-title">網站流量趨勢</h3>
                        <p class="chart-subtitle">過去30天的訪問量統計</p>
                    </div>
                    <div class="chart-placeholder">
                        <i class="fas fa-chart-line" style="font-size: 3rem; opacity: 0.3;"></i>
                    </div>
                </div>

                <!-- 最新文章 -->
                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">最新文章</h3>
                        <div class="table-actions">
                            <button class="btn btn-primary" onclick="showSection('articles')">
                                <i class="fas fa-plus"></i>
                                新增文章
                            </button>
                        </div>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>標題</th>
                                <th>分類</th>
                                <th>作者</th>
                                <th>狀態</th>
                                <th>發布時間</th>
                                <th>瀏覽量</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="recent-articles-table">
                            <tr>
                                <td>藏傳佛教中的慈悲修持法門</td>
                                <td>佛學智慧</td>
                                <td>釋智慧</td>
                                <td><span class="status-badge status-published">已發布</span></td>
                                <td>2025-01-12</td>
                                <td>1,245</td>
                                <td>
                                    <div class="action-btns">
                                        <button class="action-btn edit" title="編輯">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn delete" title="刪除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>正念冥想對現代人壓力緩解的應用</td>
                                <td>身心療癒</td>
                                <td>Dr. 陳明</td>
                                <td><span class="status-badge status-published">已發布</span></td>
                                <td>2025-01-11</td>
                                <td>892</td>
                                <td>
                                    <div class="action-btns">
                                        <button class="action-btn edit" title="編輯">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn delete" title="刪除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 文章管理內容 -->
            <div id="articles-content" class="content-section hidden">
                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">文章管理</h3>
                        <div class="table-actions">
                            <button class="btn btn-primary" onclick="showAddArticleForm()">
                                <i class="fas fa-plus"></i>
                                新增文章
                            </button>
                            <button class="btn btn-outline" onclick="exportArticles()">
                                <i class="fas fa-download"></i>
                                匯出
                            </button>
                        </div>
                    </div>
                    
                    <!-- 搜索和篩選 -->
                    <div style="padding: 20px 25px; border-bottom: 1px solid #eee;">
                        <div class="form-row">
                            <div class="form-group">
                                <input type="text" class="form-control" placeholder="搜索文章標題..." id="article-search">
                            </div>
                            <div class="form-group">
                                <select class="form-control" id="category-filter">
                                    <option value="">全部分類</option>
                                    <option value="dharma">佛學智慧</option>
                                    <option value="healing">身心療癒</option>
                                    <option value="research">最新研究</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <select class="form-control" id="status-filter">
                                    <option value="">全部狀態</option>
                                    <option value="published">已發布</option>
                                    <option value="draft">草稿</option>
                                    <option value="archived">封存</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <table>
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="select-all-articles">
                                </th>
                                <th>標題</th>
                                <th>分類</th>
                                <th>作者</th>
                                <th>狀態</th>
                                <th>發布時間</th>
                                <th>瀏覽量</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="articles-table">
                            <!-- 文章列表將由 JavaScript 動態生成 -->
                        </tbody>
                    </table>
                </div>

                <!-- 新增/編輯文章表單 -->
                <div id="article-form-container" class="form-container hidden">
                    <h3 id="article-form-title">新增文章</h3>
                    <form id="article-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">文章標題 *</label>
                                <input type="text" class="form-control" id="article-title" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">文章分類 *</label>
                                <select class="form-control" id="article-category" required>
                                    <option value="">請選擇分類</option>
                                    <option value="dharma">佛學智慧</option>
                                    <option value="healing">身心療癒</option>
                                    <option value="research">最新研究</option>
                                    <option value="sutras">經典解讀</option>
                                    <option value="masters">大師教導</option>
                                    <option value="stories">佛教故事</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">作者</label>
                                <input type="text" class="form-control" id="article-author">
                            </div>
                            <div class="form-group">
                                <label class="form-label">難度等級</label>
                                <select class="form-control" id="article-difficulty">
                                    <option value="beginner">入門</option>
                                    <option value="intermediate">進階</option>
                                    <option value="advanced">高級</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">文章摘要</label>
                            <textarea class="form-control textarea" id="article-excerpt" placeholder="簡要描述文章內容..."></textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">文章內容 *</label>
                            <textarea class="form-control editor" id="article-content" required placeholder="請輸入文章內容..."></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">標籤</label>
                                <input type="text" class="form-control" id="article-tags" placeholder="用逗號分隔多個標籤">
                            </div>
                            <div class="form-group">
                                <label class="form-label">狀態</label>
                                <select class="form-control" id="article-status">
                                    <option value="draft">草稿</option>
                                    <option value="published">發布</option>
                                    <option value="archived">封存</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group" style="margin-top: 30px;">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                保存文章
                            </button>
                            <button type="button" class="btn btn-outline" onclick="hideArticleForm()">
                                <i class="fas fa-times"></i>
                                取消
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 其他內容區域 -->
            <div id="categories-content" class="content-section hidden">
                <h2>分類管理</h2>
                <p>分類管理功能開發中...</p>
            </div>

            <div id="users-content" class="content-section hidden">
                <h2>用戶管理</h2>
                <p>用戶管理功能開發中...</p>
            </div>

            <div id="comments-content" class="content-section hidden">
                <h2>評論管理</h2>
                <p>評論管理功能開發中...</p>
            </div>

            <div id="media-content" class="content-section hidden">
                <h2>媒體庫</h2>
                <p>媒體庫功能開發中...</p>
            </div>

            <div id="analytics-content" class="content-section hidden">
                <h2>數據分析</h2>
                <p>數據分析功能開發中...</p>
            </div>

            <div id="settings-content" class="content-section hidden">
                <h2>系統設置</h2>
                <p>系統設置功能開發中...</p>
            </div>
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notification" class="notification"></div>

    <script>
        // 全局變量
        let currentSection = 'dashboard';
        let isEditing = false;
        let editingArticleId = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeAdmin();
            updateTime();
            setInterval(updateTime, 1000);
        });

        function initializeAdmin() {
            // 綁定側邊欄事件
            document.querySelectorAll('.menu-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const section = this.getAttribute('data-section');
                    showSection(section);
                });
            });

            // 綁定表單事件
            document.getElementById('article-form').addEventListener('submit', handleArticleSubmit);

            // 載入初始數據
            loadDashboardData();
        }

        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-TW');
            document.getElementById('current-time').textContent = timeString;
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const toggleIcon = document.getElementById('toggle-icon');
            
            sidebar.classList.toggle('collapsed');
            
            if (sidebar.classList.contains('collapsed')) {
                toggleIcon.className = 'fas fa-chevron-right';
            } else {
                toggleIcon.className = 'fas fa-chevron-left';
            }
        }

        function showSection(sectionName) {
            // 隱藏所有內容區域
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.add('hidden');
            });

            // 顯示選中的內容區域
            document.getElementById(sectionName + '-content').classList.remove('hidden');

            // 更新側邊欄活動狀態
            document.querySelectorAll('.menu-link').forEach(link => {
                link.classList.remove('active');
            });
            document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

            // 更新頁面標題
            const titles = {
                'dashboard': '儀表板',
                'articles': '文章管理',
                'categories': '分類管理',
                'users': '用戶管理',
                'comments': '評論管理',
                'media': '媒體庫',
                'analytics': '數據分析',
                'settings': '系統設置'
            };
            document.getElementById('page-title').textContent = titles[sectionName];

            currentSection = sectionName;

            // 載入對應數據
            switch(sectionName) {
                case 'articles':
                    loadArticles();
                    break;
                case 'dashboard':
                    loadDashboardData();
                    break;
            }
        }

        function loadDashboardData() {
            // 模擬載入儀表板數據
            animateNumber('total-articles', 256);
            animateNumber('total-views', 45672);
            animateNumber('active-users', 1234);
            animateNumber('pending-comments', 23);
        }

        function animateNumber(elementId, targetValue) {
            const element = document.getElementById(elementId);
            const start = 0;
            const duration = 1000;
            const startTime = performance.now();

            function updateNumber(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const currentValue = Math.floor(start + (targetValue - start) * progress);
                
                element.textContent = currentValue.toLocaleString();

                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                }
            }

            requestAnimationFrame(updateNumber);
        }

        function loadArticles() {
            // 模擬文章數據
            const articles = [
                {
                    id: 1,
                    title: '藏傳佛教中的慈悲修持法門',
                    category: '佛學智慧',
                    author: '釋智慧',
                    status: 'published',
                    publishDate: '2025-01-12',
                    views: 1245
                },
                {
                    id: 2,
                    title: '正念冥想對現代人壓力緩解的應用',
                    category: '身心療癒',
                    author: 'Dr. 陳明',
                    status: 'published',
                    publishDate: '2025-01-11',
                    views: 892
                },
                {
                    id: 3,
                    title: '冥想對大腦神經可塑性的影響研究',
                    category: '最新研究',
                    author: 'Dr. 王研究',
                    status: 'draft',
                    publishDate: '2025-01-10',
                    views: 567
                }
            ];

            renderArticlesTable(articles);
        }

        function renderArticlesTable(articles) {
            const tbody = document.getElementById('articles-table');
            tbody.innerHTML = '';

            articles.forEach(article => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="checkbox" class="article-checkbox" data-id="${article.id}"></td>
                    <td>${article.title}</td>
                    <td>${article.category}</td>
                    <td>${article.author}</td>
                    <td><span class="status-badge status-${article.status}">${getStatusText(article.status)}</span></td>
                    <td>${article.publishDate}</td>
                    <td>${article.views.toLocaleString()}</td>
                    <td>
                        <div class="action-btns">
                            <button class="action-btn edit" onclick="editArticle(${article.id})" title="編輯">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn delete" onclick="deleteArticle(${article.id})" title="刪除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function getStatusText(status) {
            const statusMap = {
                'published': '已發布',
                'draft': '草稿',
                'archived': '封存'
            };
            return statusMap[status] || status;
        }

        function showAddArticleForm() {
            document.getElementById('article-form-container').classList.remove('hidden');
            document.getElementById('article-form-title').textContent = '新增文章';
            document.getElementById('article-form').reset();
            isEditing = false;
            editingArticleId = null;
        }

        function hideArticleForm() {
            document.getElementById('article-form-container').classList.add('hidden');
            isEditing = false;
            editingArticleId = null;
        }

        function editArticle(articleId) {
            // 模擬載入文章數據
            const articleData = {
                title: '藏傳佛教中的慈悲修持法門',
                category: 'dharma',
                author: '釋智慧',
                difficulty: 'beginner',
                excerpt: '慈悲心是藏傳佛教修行的核心...',
                content: '慈悲心的深層意義...',
                tags: '慈悲修持,藏傳佛教,四無量心',
                status: 'published'
            };

            // 填充表單
            document.getElementById('article-title').value = articleData.title;
            document.getElementById('article-category').value = articleData.category;
            document.getElementById('article-author').value = articleData.author;
            document.getElementById('article-difficulty').value = articleData.difficulty;
            document.getElementById('article-excerpt').value = articleData.excerpt;
            document.getElementById('article-content').value = articleData.content;
            document.getElementById('article-tags').value = articleData.tags;
            document.getElementById('article-status').value = articleData.status;

            // 顯示表單
            document.getElementById('article-form-container').classList.remove('hidden');
            document.getElementById('article-form-title').textContent = '編輯文章';
            isEditing = true;
            editingArticleId = articleId;
        }

        function deleteArticle(articleId) {
            if (confirm('確定要刪除這篇文章嗎？此操作無法復原。')) {
                // 模擬刪除操作
                showNotification('文章已刪除', 'success');
                loadArticles(); // 重新載入文章列表
            }
        }

        function handleArticleSubmit(e) {
            e.preventDefault();

            const formData = {
                title: document.getElementById('article-title').value,
                category: document.getElementById('article-category').value,
                author: document.getElementById('article-author').value,
                difficulty: document.getElementById('article-difficulty').value,
                excerpt: document.getElementById('article-excerpt').value,
                content: document.getElementById('article-content').value,
                tags: document.getElementById('article-tags').value,
                status: document.getElementById('article-status').value
            };

            // 簡單驗證
            if (!formData.title || !formData.category || !formData.content) {
                showNotification('請填寫必填欄位', 'error');
                return;
            }

            // 模擬提交
            const action = isEditing ? '更新' : '創建';
            showNotification(`文章${action}成功`, 'success');
            
            hideArticleForm();
            loadArticles(); // 重新載入文章列表
        }

        function exportArticles() {
            showNotification('文章匯出功能開發中...', 'warning');
        }

        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // 響應式處理
        function handleMobileMenu() {
            if (window.innerWidth <= 768) {
                document.getElementById('sidebar').classList.add('mobile-menu');
            } else {
                document.getElementById('sidebar').classList.remove('mobile-menu');
            }
        }

        window.addEventListener('resize', handleMobileMenu);
        handleMobileMenu(); // 初始化時執行
    </script>
</body>
</html>