<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首頁 - 澄源書坊</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.8;
            color: #5a5a5a;
            background: #f8f6f1;
            overflow-x: hidden;
        }

        /* 主橫幅區域 */
        .hero-section {
            background: linear-gradient(135deg, rgba(160, 140, 120, 0.9), rgba(140, 120, 100, 0.9)),
                        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="white" opacity="0.1"/><circle cx="80" cy="60" r="1.5" fill="white" opacity="0.1"/><circle cx="40" cy="80" r="0.8" fill="white" opacity="0.1"/></svg>');
            background-size: cover, 80px 80px;
            color: white;
            padding: 120px 30px;
            text-align: center;
            position: relative;
            min-height: 500px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .hero-content {
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 300;
            margin-bottom: 30px;
            letter-spacing: 8px;
            opacity: 0;
            animation: fadeInUp 1.5s ease 0.5s forwards;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            font-weight: 300;
            line-height: 2;
            opacity: 0.95;
            margin-bottom: 40px;
            opacity: 0;
            animation: fadeInUp 1.5s ease 1s forwards;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 主要內容區 */
        .main-content {
            max-width: 1200px;
            margin: -60px auto 0;
            padding: 0 30px;
            position: relative;
            z-index: 3;
        }

        /* 功能卡片區域 */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-bottom: 80px;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 50px 40px;
            text-align: center;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInCard 0.8s ease forwards;
        }

        .feature-card:nth-child(1) { animation-delay: 0.2s; }
        .feature-card:nth-child(2) { animation-delay: 0.4s; }
        .feature-card:nth-child(3) { animation-delay: 0.6s; }
        .feature-card:nth-child(4) { animation-delay: 0.8s; }

        @keyframes fadeInCard {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #c9a876, #b8966d);
            transform: scaleX(0);
            transition: transform 0.4s ease;
        }

        .feature-card:hover::before {
            transform: scaleX(1);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            font-size: 3rem;
            color: #c9a876;
            margin-bottom: 25px;
            transition: all 0.4s ease;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1);
            color: #b8966d;
        }

        .feature-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2c2c2c;
            margin-bottom: 15px;
            letter-spacing: 2px;
        }

        .feature-description {
            font-size: 1rem;
            color: #666;
            line-height: 1.8;
            margin-bottom: 25px;
        }

        .feature-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #c9a876;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.95rem;
        }

        .feature-link:hover {
            color: #b8966d;
            transform: translateX(5px);
        }

        .feature-link i {
            font-size: 0.8rem;
        }

        /* 精選內容區 */
        .featured-section {
            background: white;
            border-radius: 20px;
            padding: 60px 50px;
            margin-bottom: 80px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
        }

        .section-header {
            text-align: center;
            margin-bottom: 50px;
        }

        .section-title {
            font-size: 2.2rem;
            font-weight: 300;
            color: #2c2c2c;
            margin-bottom: 15px;
            letter-spacing: 3px;
        }

        .section-subtitle {
            font-size: 1.1rem;
            color: #888;
            font-weight: 300;
        }

        .articles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
        }

        .article-card {
            background: #fafafa;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.4s ease;
            cursor: pointer;
            border: 1px solid #f0f0f0;
        }

        .article-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
            border-color: #c9a876;
        }

        .article-header {
            padding: 30px 25px 20px;
        }

        .article-category {
            display: inline-block;
            background: #c9a876;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-bottom: 15px;
        }

        .article-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c2c2c;
            margin-bottom: 12px;
            line-height: 1.5;
        }

        .article-excerpt {
            font-size: 0.95rem;
            color: #666;
            line-height: 1.7;
            margin-bottom: 20px;
        }

        .article-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.85rem;
            color: #999;
            padding: 0 25px 25px;
        }

        /* 底部行動呼籲 */
        .cta-section {
            background: linear-gradient(135deg, #2c2c2c 0%, #4a4a4a 100%);
            color: white;
            padding: 60px 50px;
            text-align: center;
            border-radius: 20px;
            margin-bottom: 40px;
        }

        .cta-title {
            font-size: 2rem;
            font-weight: 300;
            margin-bottom: 20px;
            letter-spacing: 2px;
        }

        .cta-description {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 30px;
            line-height: 1.8;
        }

        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .cta-button {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: #c9a876;
            color: white;
            padding: 15px 30px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 2px solid #c9a876;
        }

        .cta-button:hover {
            background: transparent;
            transform: translateY(-2px);
        }

        .cta-button.secondary {
            background: transparent;
            border-color: white;
            color: white;
        }

        .cta-button.secondary:hover {
            background: white;
            color: #2c2c2c;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
                letter-spacing: 4px;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .main-content {
                padding: 0 20px;
                margin-top: -40px;
            }

            .feature-card,
            .featured-section,
            .cta-section {
                padding: 40px 30px;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .articles-grid {
                grid-template-columns: 1fr;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
        }

        @media (max-width: 480px) {
            .hero-section {
                padding: 80px 20px;
            }

            .hero-title {
                font-size: 2rem;
                letter-spacing: 2px;
            }

            .section-title {
                font-size: 1.8rem;
                letter-spacing: 1px;
            }
        }

        /* 載入動畫 */
        .fade-in {
            opacity: 0;
            animation: fadeIn 1s ease forwards;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- 主橫幅 -->
    <section class="hero-section">
        <div class="hero-content">
            <h1 class="hero-title">智慧如海·典籍為舟</h1>
            <p class="hero-subtitle">
                在這裡，讓我們一同探索東方智慧的寶藏，傳承千年的典籍智慧，開啟<br>
                心靈的新視界。
            </p>
        </div>
    </section>

    <!-- 主要內容 -->
    <main class="main-content">
        <!-- 功能特色 -->
        <section class="features-grid">
            <div class="feature-card" data-page="dharma">
                <div class="feature-icon">
                    <i class="fas fa-dharmachakra"></i>
                </div>
                <h3 class="feature-title">佛教智學</h3>
                <p class="feature-description">
                    探索古老智慧與現代意識的究竟結合，傳承千年的佛陀智慧。
                </p>
                <a href="#" class="feature-link">
                    進入書閣 <i class="fas fa-arrow-right"></i>
                </a>
            </div>

            <div class="feature-card" data-page="healing">
                <div class="feature-icon">
                    <i class="fas fa-spa"></i>
                </div>
                <h3 class="feature-title">西藏佛教</h3>
                <p class="feature-description">
                    深入了解藏傳佛教的修行法要，學習雪域高原的慈悲智慧。
                </p>
                <a href="#" class="feature-link">
                    進入書閣 <i class="fas fa-arrow-right"></i>
                </a>
            </div>

            <div class="feature-card" data-page="research">
                <div class="feature-icon">
                    <i class="fas fa-book-open"></i>
                </div>
                <h3 class="feature-title">藏書閱讀</h3>
                <p class="feature-description">
                    豐富的佛典藏書，為您提供完整的修學資料之門。
                </p>
                <a href="#" class="feature-link">
                    進入書閣 <i class="fas fa-arrow-right"></i>
                </a>
            </div>

            <div class="feature-card" data-page="search">
                <div class="feature-icon">
                    <i class="fas fa-bookmark"></i>
                </div>
                <h3 class="feature-title">其他典籍</h3>
                <p class="feature-description">
                    廣博精深的其他領域典籍，滿足您求知探究的願心。
                </p>
                <a href="#" class="feature-link">
                    進入書閣 <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </section>

        <!-- 精選內容 -->
        <section class="featured-section fade-in">
            <div class="section-header">
                <h2 class="section-title">精選典籍</h2>
                <p class="section-subtitle">精心挑選的智慧典籍，開啟您的修學之路</p>
            </div>
            
            <div class="articles-grid">
                <article class="article-card" data-article="1">
                    <div class="article-header">
                        <span class="article-category">佛教智學</span>
                        <h3 class="article-title">藏傳佛教中的慈悲修持法門</h3>
                        <p class="article-excerpt">
                            慈悲心是藏傳佛教修行的核心，也是成就菩薩道的根本。本文將深入探討如何在日常生活中培養真正的慈悲心...
                        </p>
                    </div>
                    <div class="article-meta">
                        <span><i class="fas fa-user"></i> 釋智慧</span>
                        <span><i class="fas fa-calendar"></i> 2025-01-10</span>
                    </div>
                </article>

                <article class="article-card" data-article="2">
                    <div class="article-header">
                        <span class="article-category">西藏佛教</span>
                        <h3 class="article-title">正念冥想的修行次第</h3>
                        <p class="article-excerpt">
                            結合藏傳佛教的修行傳統，探討正念冥想如何有效緩解現代生活中的各種壓力，獲得內心的平靜...
                        </p>
                    </div>
                    <div class="article-meta">
                        <span><i class="fas fa-user"></i> 法師明覺</span>
                        <span><i class="fas fa-calendar"></i> 2025-01-08</span>
                    </div>
                </article>

                <article class="article-card" data-article="3">
                    <div class="article-header">
                        <span class="article-category">藏書閱讀</span>
                        <h3 class="article-title">佛典選讀：金剛經的智慧</h3>
                        <p class="article-excerpt">
                            《金剛經》是般若部的代表作之一，蘊含了深刻的空性智慧。讓我們一起深入這部經典的核心教義...
                        </p>
                    </div>
                    <div class="article-meta">
                        <span><i class="fas fa-user"></i> 學者慧空</span>
                        <span><i class="fas fa-calendar"></i> 2025-01-05</span>
                    </div>
                </article>
            </div>
        </section>

        <!-- 行動呼籲 -->
        <section class="cta-section fade-in">
            <h2 class="cta-title">開始您的智慧之旅</h2>
            <p class="cta-description">
                加入我們的學習社群，與志同道合的朋友一起探索古老智慧的現代意義
            </p>
            <div class="cta-buttons">
                <a href="#" class="cta-button" data-page="dharma">
                    <i class="fas fa-book"></i>
                    開始閱讀
                </a>
                <a href="#" class="cta-button secondary" data-page="search">
                    <i class="fas fa-search"></i>
                    搜索典籍
                </a>
            </div>
        </section>
    </main>

    <script>
        // 頁面載入動畫
        document.addEventListener('DOMContentLoaded', function() {
            // 添加載入完成的動畫
            setTimeout(() => {
                document.querySelectorAll('.fade-in').forEach((element, index) => {
                    setTimeout(() => {
                        element.style.opacity = '1';
                    }, index * 200);
                });
            }, 500);

            // 平滑滾動效果
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.fade-in').forEach(el => {
                observer.observe(el);
            });
        });

        // 點擊導航
        document.addEventListener('click', function(e) {
            const element = e.target.closest('[data-page]');
            if (element) {
                e.preventDefault();
                const pageName = element.getAttribute('data-page');
                
                // 發送導航消息給父頁面
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'navigate',
                        page: pageName
                    }, '*');
                }
            }

            // 文章點擊
            const articleElement = e.target.closest('[data-article]');
            if (articleElement) {
                e.preventDefault();
                const articleId = articleElement.getAttribute('data-article');
                
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'navigate',
                        page: 'article',
                        articleId: articleId
                    }, '*');
                }
            }
        });

        // 卡片懸停效果增強
        document.querySelectorAll('.feature-card, .article-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // 背景粒子效果（可選）
        function createParticles() {
            const hero = document.querySelector('.hero-section');
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.style.cssText = `
                    position: absolute;
                    width: 2px;
                    height: 2px;
                    background: rgba(255,255,255,0.3);
                    border-radius: 50%;
                    left: ${Math.random() * 100}%;
                    top: ${Math.random() * 100}%;
                    animation: float ${5 + Math.random() * 10}s infinite;
                `;
                hero.appendChild(particle);
            }
        }

        // 初始化粒子效果
        setTimeout(createParticles, 1000);
    </script>
</body>
</html>