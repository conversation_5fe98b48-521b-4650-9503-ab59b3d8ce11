const mongoose = require('mongoose');

const courseSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    required: true,
    maxlength: 2000
  },
  category: {
    type: String,
    required: true,
    enum: ['基礎佛學', '進階修持', '療癒課程', '禪修指導', '經文研讀', '生活應用', '特殊課程']
  },
  difficulty: {
    type: String,
    enum: ['beginner', 'intermediate', 'advanced'],
    required: true
  },
  duration: {
    type: Number, // 分鐘
    required: true,
    min: 1
  },
  instructor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  content: {
    modules: [{
      title: {
        type: String,
        required: true
      },
      description: String,
      videos: [String], // 視頻文件URLs
      documents: [String], // 文檔URLs
      assignments: [{
        title: String,
        description: String,
        type: {
          type: String,
          enum: ['quiz', 'essay', 'practice', 'reflection']
        },
        questions: [mongoose.Schema.Types.Mixed],
        timeLimit: Number, // 分鐘
        passingScore: Number
      }],
      order: {
        type: Number,
        default: 0
      }
    }]
  },
  prerequisites: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course'
  }],
  tags: [String],
  pricing: {
    free: {
      type: Boolean,
      default: false
    },
    membershipRequired: {
      type: String,
      enum: ['basic', 'premium', 'vip'],
      default: 'basic'
    },
    price: {
      type: Number,
      default: 0,
      min: 0
    },
    currency: {
      type: String,
      default: 'TWD'
    }
  },
  statistics: {
    enrollments: {
      type: Number,
      default: 0
    },
    completions: {
      type: Number,
      default: 0
    },
    averageRating: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    reviews: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Review'
    }],
    totalRatings: {
      type: Number,
      default: 0
    }
  },
  schedule: {
    startDate: Date,
    endDate: Date,
    sessions: [{
      date: Date,
      startTime: String,
      endTime: String,
      location: String,
      type: {
        type: String,
        enum: ['online', 'offline', 'hybrid']
      }
    }]
  },
  resources: {
    materials: [String], // 學習材料URLs
    references: [String], // 參考資料
    downloads: [String] // 可下載資源
  },
  assessment: {
    hasQuiz: {
      type: Boolean,
      default: false
    },
    hasAssignment: {
      type: Boolean,
      default: false
    },
    hasCertificate: {
      type: Boolean,
      default: false
    },
    passingScore: {
      type: Number,
      default: 70
    }
  },
  settings: {
    allowDiscussion: {
      type: Boolean,
      default: true
    },
    allowRating: {
      type: Boolean,
      default: true
    },
    autoEnroll: {
      type: Boolean,
      default: false
    },
    maxStudents: {
      type: Number,
      default: 0 // 0 = 無限制
    }
  },
  status: {
    type: String,
    enum: ['draft', 'published', 'archived', 'suspended'],
    default: 'draft'
  },
  featured: {
    type: Boolean,
    default: false
  },
  thumbnail: String, // 課程縮圖URL
  created: {
    type: Date,
    default: Date.now
  },
  updated: {
    type: Date,
    default: Date.now
  },
  publishedAt: Date,
  archivedAt: Date
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 虛擬字段：完成率
courseSchema.virtual('completionRate').get(function() {
  if (this.statistics.enrollments === 0) return 0;
  return Math.round((this.statistics.completions / this.statistics.enrollments) * 100);
});

// 虛擬字段：總時長
courseSchema.virtual('totalDuration').get(function() {
  return this.content.modules.reduce((total, module) => {
    return total + (module.duration || 0);
  }, 0);
});

// 索引
courseSchema.index({ title: 'text', description: 'text', tags: 'text' });
courseSchema.index({ category: 1 });
courseSchema.index({ difficulty: 1 });
courseSchema.index({ status: 1 });
courseSchema.index({ instructor: 1 });
courseSchema.index({ 'pricing.membershipRequired': 1 });
courseSchema.index({ created: -1 });
courseSchema.index({ 'statistics.enrollments': -1 });
courseSchema.index({ 'statistics.averageRating': -1 });

// 更新時間的中間件
courseSchema.pre('save', function(next) {
  this.updated = new Date();
  next();
});

// 發布狀態變更時設置發布時間
courseSchema.pre('save', function(next) {
  if (this.isModified('status') && this.status === 'published' && !this.publishedAt) {
    this.publishedAt = new Date();
  }
  next();
});

// 靜態方法：按類別獲取課程
courseSchema.statics.findByCategory = function(category, limit = 10) {
  return this.find({ 
    category: category, 
    status: 'published' 
  })
  .populate('instructor', 'profile.firstName profile.lastName')
  .limit(limit)
  .sort({ created: -1 });
};

// 靜態方法：獲取熱門課程
courseSchema.statics.findPopular = function(limit = 10) {
  return this.find({ status: 'published' })
    .populate('instructor', 'profile.firstName profile.lastName')
    .sort({ 'statistics.enrollments': -1, 'statistics.averageRating': -1 })
    .limit(limit);
};

// 實例方法：檢查用戶是否可以報名
courseSchema.methods.canEnroll = function(user) {
  // 檢查會員權限
  const membershipLevels = { basic: 1, premium: 2, vip: 3 };
  const userLevel = membershipLevels[user.membership.plan];
  const requiredLevel = membershipLevels[this.pricing.membershipRequired];
  
  if (userLevel < requiredLevel) {
    return { allowed: false, reason: 'insufficient_membership' };
  }
  
  // 檢查是否已報名
  if (user.progress.currentCourses.includes(this._id) || 
      user.progress.coursesCompleted.includes(this._id)) {
    return { allowed: false, reason: 'already_enrolled' };
  }
  
  // 檢查人數限制
  if (this.settings.maxStudents > 0 && 
      this.statistics.enrollments >= this.settings.maxStudents) {
    return { allowed: false, reason: 'course_full' };
  }
  
  return { allowed: true };
};

// 實例方法：更新統計數據
courseSchema.methods.updateStatistics = function() {
  // 這裡應該從實際的註冊和評價數據計算
  // 現在只是示例
  return this.save();
};

module.exports = mongoose.model('Course', courseSchema);