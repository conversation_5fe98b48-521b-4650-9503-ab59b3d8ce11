# ================================
# 🐳 佛教身心療癒網站 - Docker 配置
# ================================

# 使用官方 Node.js 16 Alpine 映像作為基礎
FROM node:16-alpine AS base

# 設置工作目錄
WORKDIR /app

# 安裝必要的系統依賴
RUN apk add --no-cache \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# ================================
# 📦 依賴安裝階段
# ================================
FROM base AS dependencies

# 複製 package files
COPY package*.json ./

# 安裝所有依賴 (包括 devDependencies)
RUN npm ci --include=dev

# ================================
# 🏗️ 建構階段
# ================================
FROM dependencies AS build

# 複製源代碼
COPY . .

# 執行建構腳本 (如果有的話)
RUN npm run build 2>/dev/null || echo "No build script found"

# 執行測試
RUN npm test 2>/dev/null || echo "No tests found"

# 清理開發依賴
RUN npm ci --only=production && npm cache clean --force

# ================================
# 🚀 生產階段
# ================================
FROM base AS production

# 設置生產環境
ENV NODE_ENV=production

# 創建非 root 用戶
RUN addgroup -g 1001 -S nodejs && \
    adduser -S buddhist-healing -u 1001

# 創建必要的目錄
RUN mkdir -p /app/logs /app/uploads /app/temp && \
    chown -R buddhist-healing:nodejs /app

# 從建構階段複製應用文件
COPY --from=build --chown=buddhist-healing:nodejs /app/node_modules ./node_modules
COPY --from=build --chown=buddhist-healing:nodejs /app/package*.json ./
COPY --from=build --chown=buddhist-healing:nodejs /app/app.js ./
COPY --from=build --chown=buddhist-healing:nodejs /app/models ./models
COPY --from=build --chown=buddhist-healing:nodejs /app/routes ./routes
COPY --from=build --chown=buddhist-healing:nodejs /app/middleware ./middleware
COPY --from=build --chown=buddhist-healing:nodejs /app/utils ./utils
COPY --from=build --chown=buddhist-healing:nodejs /app/*.html ./

# 複製模板文件 (如果存在)
COPY --from=build --chown=buddhist-healing:nodejs /app/templates ./templates/ 2>/dev/null || :

# 複製配置文件 (如果存在)
COPY --from=build --chown=buddhist-healing:nodejs /app/config ./config/ 2>/dev/null || :

# 切換到非 root 用戶
USER buddhist-healing

# 暴露端口
EXPOSE 3000

# 健康檢查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# 設置啟動命令
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "app.js"]

# ================================
# 🏷️ 元數據標籤
# ================================
LABEL maintainer="<EMAIL>"
LABEL version="1.0.0"
LABEL description="佛教身心療癒網站後端服務"
LABEL org.opencontainers.image.title="Buddhist Healing Website"
LABEL org.opencontainers.image.description="A comprehensive platform for Buddhist mind-body healing education"
LABEL org.opencontainers.image.authors="Buddhist Healing Team"
LABEL org.opencontainers.image.source="https://github.com/your-org/buddhist-healing-website"
LABEL org.opencontainers.image.documentation="https://github.com/your-org/buddhist-healing-website/blob/main/README.md"