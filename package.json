{"name": "chengyuan-reading", "version": "1.0.0", "description": "澄源閱讀 - 佛學與身心療癒智慧分享平台", "main": "index.html", "scripts": {"dev": "wrangler pages dev . --d1=DB=chengyuan-reading-db --r2=R2_BUCKET=chengyuan-images", "dev:api": "wrangler dev functions/articles.js --local", "deploy": "wrangler pages deploy . --project-name=chengyuan-reading", "deploy:api": "wrangler deploy functions/articles.js", "db:create": "wrangler d1 create chengyuan-reading-db", "db:migrate": "wrangler d1 execute chengyuan-reading-db --file=database/schema.sql", "db:seed": "wrangler d1 execute chengyuan-reading-db --file=database/seed.sql", "db:backup": "wrangler d1 export chengyuan-reading-db --output=database/backup.sql", "r2:create": "wrangler r2 bucket create chengyuan-images", "r2:list": "wrangler r2 bucket list", "build": "echo 'Building static site...' && npm run optimize", "optimize": "npm run optimize:images && npm run optimize:css && npm run optimize:js", "optimize:images": "echo 'Optimizing images...'", "optimize:css": "echo 'Optimizing CSS...'", "optimize:js": "echo 'Optimizing JavaScript...'", "test": "echo 'Running tests...'", "lint": "echo 'Linting code...'", "preview": "wrangler pages dev . --compatibility-date=2024-12-01", "logs": "wrangler tail chengyuan-reading", "logs:api": "wrangler tail chengyuan-reading-api"}, "keywords": ["buddhism", "meditation", "healing", "mindfulness", "tibetan-buddhism", "cloudflare", "static-site"], "author": "澄源閱讀團隊", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/chengyuan-reading/website.git"}, "homepage": "https://chengyuan-reading.com", "devDependencies": {"wrangler": "^3.22.0"}, "engines": {"node": ">=18.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}