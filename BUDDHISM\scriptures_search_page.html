<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>經文搜尋 - 佛教身心療癒網站</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2C3E50;
            --secondary-color: #F39C12;
            --accent-color: #E8F4F8;
            --text-color: #2C3E50;
            --light-bg: #F8F9FA;
            --success-color: #27AE60;
            --info-color: #3498DB;
        }

        body {
            font-family: 'Noto Sans CJK TC', 'Microsoft JhengHei', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            padding: 120px 0 80px;
            position: relative;
            overflow: hidden;
        }

        .navbar-custom {
            background: rgba(44, 62, 80, 0.95);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .nav-link {
            color: white !important;
            font-weight: 500;
            margin: 0 10px;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--secondary-color) !important;
        }

        .search-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-top: -60px;
            position: relative;
            z-index: 10;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .search-box {
            position: relative;
            margin-bottom: 30px;
        }

        .search-input {
            border: 2px solid #ddd;
            border-radius: 25px;
            padding: 15px 60px 15px 25px;
            font-size: 1.1rem;
            width: 100%;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(243, 156, 18, 0.25);
            outline: none;
        }

        .search-btn {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--secondary-color);
            border: none;
            border-radius: 20px;
            width: 50px;
            height: 40px;
            color: white;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            background: #e67e22;
            transform: translateY(-50%) scale(1.05);
        }

        .filter-section {
            background: var(--light-bg);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .filter-btn {
            background: white;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            padding: 8px 20px;
            border-radius: 20px;
            margin: 5px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            cursor: pointer;
        }

        .filter-btn.active,
        .filter-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .scripture-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid var(--secondary-color);
        }

        .scripture-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .scripture-title {
            color: var(--primary-color);
            font-weight: bold;
            font-size: 1.2rem;
            margin-bottom: 10px;
        }

        .scripture-meta {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            font-size: 0.9rem;
            color: #666;
        }

        .scripture-meta span {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .scripture-content {
            background: var(--light-bg);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            font-style: italic;
            border-left: 3px solid var(--info-color);
        }

        .scripture-tags {
            margin-top: 15px;
        }

        .tag {
            background: var(--accent-color);
            color: var(--primary-color);
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            margin-right: 8px;
            margin-bottom: 5px;
            display: inline-block;
        }

        .quick-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .category-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .category-card:hover {
            transform: translateY(-5px);
            border-color: var(--secondary-color);
        }

        .category-icon {
            font-size: 2.5rem;
            color: var(--secondary-color);
            margin-bottom: 15px;
        }

        .results-summary {
            background: var(--info-color);
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            margin-bottom: 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pagination-custom {
            display: flex;
            justify-content: center;
            margin-top: 40px;
        }

        .pagination-custom .page-link {
            border: none;
            color: var(--primary-color);
            margin: 0 5px;
            border-radius: 8px;
            padding: 10px 15px;
            transition: all 0.3s ease;
        }

        .pagination-custom .page-link:hover,
        .pagination-custom .page-item.active .page-link {
            background: var(--secondary-color);
            color: white;
            transform: translateY(-2px);
        }

        .advanced-search {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            display: none;
        }

        .advanced-search.show {
            display: block;
            animation: slideDown 0.5s ease;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-control:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(243, 156, 18, 0.25);
        }

        @media (max-width: 768px) {
            .page-header {
                padding: 100px 0 60px;
            }
            
            .search-container {
                margin-top: -40px;
                padding: 25px;
            }
            
            .scripture-meta {
                flex-direction: column;
                gap: 10px;
            }
            
            .filter-btn {
                padding: 6px 15px;
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html" style="color: var(--secondary-color);">
                <i class="fas fa-lotus me-2"></i>佛教身心療癒
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">主頁</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">關於我們</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            課程
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses-goals.html">課程目標</a></li>
                            <li><a class="dropdown-item" href="courses-weekly.html">每週主題</a></li>
                            <li><a class="dropdown-item" href="courses-methods.html">進行方式</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown">
                            經文選讀
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="scriptures-search.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="scriptures-methods.html">療癒方法</a></li>
                            <li><a class="dropdown-item" href="scriptures-theory.html">療癒理論</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            身心療癒研究
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="research-search.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="research-theory.html">療癒理論</a></li>
                            <li><a class="dropdown-item" href="research-methods.html">療癒方法</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            藏傳佛教
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="tibetan-theory.html">藏醫理論</a></li>
                            <li><a class="dropdown-item" href="tibetan-practice.html">藏醫實習</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            海外實習
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="overseas-members.html">成員</a></li>
                            <li><a class="dropdown-item" href="overseas-research.html">研究成果</a></li>
                            <li><a class="dropdown-item" href="overseas-future.html">未來計畫</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="workshop.html">工作坊</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="membership.html">加入會員</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 頁面標題 -->
    <section class="page-header">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-4">經文搜尋</h1>
                    <p class="lead">探索佛教經典中的療癒智慧，找到適合您的修習指導</p>
                </div>
            </div>
        </div>
    </section>

    <main>
        <!-- 搜尋容器 -->
        <section class="py-5">
            <div class="container">
                <div class="search-container">
                    <div class="search-box">
                        <input type="text" class="search-input" placeholder="請輸入關鍵字搜尋經文內容..." id="searchInput">
                        <button class="search-btn" onclick="performSearch()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    
                    <div class="text-center">
                        <button class="btn btn-outline-secondary" onclick="toggleAdvancedSearch()">
                            <i class="fas fa-cog me-2"></i>進階搜尋
                        </button>
                    </div>

                    <!-- 進階搜尋 -->
                    <div class="advanced-search" id="advancedSearch">
                        <h5 class="fw-bold mb-3">進階搜尋選項</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">經典類別</label>
                                    <select class="form-control">
                                        <option value="">全部類別</option>
                                        <option value="sutra">經典</option>
                                        <option value="vinaya">律典</option>
                                        <option value="abhidharma">論典</option>
                                        <option value="zen">禪宗</option>
                                        <option value="pure-land">淨土</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">療癒主題</label>
                                    <select class="form-control">
                                        <option value="">全部主題</option>
                                        <option value="stress">壓力緩解</option>
                                        <option value="anxiety">焦慮調節</option>
                                        <option value="depression">憂鬱療癒</option>
                                        <option value="mindfulness">正念修習</option>
                                        <option value="compassion">慈悲培養</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">難度等級</label>
                                    <select class="form-control">
                                        <option value="">全部等級</option>
                                        <option value="beginner">初級</option>
                                        <option value="intermediate">中級</option>
                                        <option value="advanced">高級</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">語言版本</label>
                                    <select class="form-control">
                                        <option value="">全部語言</option>
                                        <option value="chinese">中文</option>
                                        <option value="pali">巴利文</option>
                                        <option value="sanskrit">梵文</option>
                                        <option value="tibetan">藏文</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="text-center">
                            <button class="btn btn-primary" onclick="performAdvancedSearch()">執行進階搜尋</button>
                            <button class="btn btn-outline-secondary ms-2" onclick="resetSearch()">重置</button>
                        </div>
                    </div>
                </div>

                <!-- 快速分類 -->
                <div class="quick-categories">
                    <div class="category-card" onclick="quickSearch('心經')">
                        <div class="category-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <h6 class="fw-bold">般若心經</h6>
                        <p class="small text-muted mb-0">最受歡迎的經典</p>
                    </div>
                    <div class="category-card" onclick="quickSearch('金剛經')">
                        <div class="category-icon">
                            <i class="fas fa-gem"></i>
                        </div>
                        <h6 class="fw-bold">金剛經</h6>
                        <p class="small text-muted mb-0">智慧與覺悟</p>
                    </div>
                    <div class="category-card" onclick="quickSearch('地藏經')">
                        <div class="category-icon">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <h6 class="fw-bold">地藏經</h6>
                        <p class="small text-muted mb-0">慈悲與願力</p>
                    </div>
                    <div class="category-card" onclick="quickSearch('藥師經')">
                        <div class="category-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <h6 class="fw-bold">藥師經</h6>
                        <p class="small text-muted mb-0">身心療癒</p>
                    </div>
                    <div class="category-card" onclick="quickSearch('觀音')">
                        <div class="category-icon">
                            <i class="fas fa-praying-hands"></i>
                        </div>
                        <h6 class="fw-bold">觀音法門</h6>
                        <p class="small text-muted mb-0">慈悲救度</p>
                    </div>
                    <div class="category-card" onclick="quickSearch('禪修')">
                        <div class="category-icon">
                            <i class="fas fa-meditation"></i>
                        </div>
                        <h6 class="fw-bold">禪修指導</h6>
                        <p class="small text-muted mb-0">靜心修習</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 搜尋結果 -->
        <section class="py-3" id="searchResults" style="display: none;">
            <div class="container">
                <!-- 篩選器 -->
                <div class="filter-section">
                    <h6 class="fw-bold mb-3">篩選結果</h6>
                    <div class="d-flex flex-wrap">
                        <button class="filter-btn active" data-filter="all">全部</button>
                        <button class="filter-btn" data-filter="sutra">經典</button>
                        <button class="filter-btn" data-filter="meditation">冥想</button>
                        <button class="filter-btn" data-filter="healing">療癒</button>
                        <button class="filter-btn" data-filter="wisdom">智慧</button>
                        <button class="filter-btn" data-filter="compassion">慈悲</button>
                    </div>
                </div>

                <!-- 結果摘要 -->
                <div class="results-summary">
                    <span>找到 <strong id="resultCount">42</strong> 筆相關經文</span>
                    <select class="form-select w-auto" style="background: rgba(255,255,255,0.2); border: none; color: white;">
                        <option>按相關性排序</option>
                        <option>按時間排序</option>
                        <option>按難度排序</option>
                        <option>按熱門度排序</option>
                    </select>
                </div>

                <!-- 搜尋結果列表 -->
                <div id="resultsList">
                    <!-- 結果項目 1 -->
                    <div class="scripture-card">
                        <div class="scripture-title">般若波羅蜜多心經</div>
                        <div class="scripture-meta">
                            <span><i class="fas fa-book me-1"></i>般若部</span>
                            <span><i class="fas fa-user me-1"></i>玄奘法師 譯</span>
                            <span><i class="fas fa-star me-1"></i>4.9</span>
                            <span><i class="fas fa-eye me-1"></i>2,345 閱讀</span>
                        </div>
                        <div class="scripture-content">
                            觀自在菩薩，行深般若波羅蜜多時，照見五蘊皆空，度一切苦厄。舍利子，色不異空，空不異色，色即是空，空即是色...
                        </div>
                        <p class="text-muted">心經是般若經典的精髓，教導我們透過智慧觀照，了悟諸法皆空的道理，從而解脫煩惱，獲得內心平靜。適合各種程度的修習者。</p>
                        <div class="scripture-tags">
                            <span class="tag">智慧</span>
                            <span class="tag">般若</span>
                            <span class="tag">解脫</span>
                            <span class="tag">初級</span>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-primary btn-sm me-2">閱讀全文</button>
                            <button class="btn btn-outline-secondary btn-sm me-2">收藏</button>
                            <button class="btn btn-outline-info btn-sm">分享</button>
                        </div>
                    </div>

                    <!-- 結果項目 2 -->
                    <div class="scripture-card">
                        <div class="scripture-title">藥師琉璃光如來本願功德經</div>
                        <div class="scripture-meta">
                            <span><i class="fas fa-book me-1"></i>大乘經典</span>
                            <span><i class="fas fa-user me-1"></i>玄奘法師 譯</span>
                            <span><i class="fas fa-star me-1"></i>4.8</span>
                            <span><i class="fas fa-eye me-1"></i>1,892 閱讀</span>
                        </div>
                        <div class="scripture-content">
                            爾時，曼殊室利法王子承佛威神，從座而起，偏袒一肩，右膝著地，向薄伽梵曲躬合掌，白言：「世尊！惟願演說如是相類諸佛名號...」
                        </div>
                        <p class="text-muted">藥師經專門講述藥師佛的十二大願，特別適合身心療癒的修習，能夠幫助消除疾病、延長壽命、增長智慧。</p>
                        <div class="scripture-tags">
                            <span class="tag">療癒</span>
                            <span class="tag">健康</span>
                            <span class="tag">藥師佛</span>
                            <span class="tag">中級</span>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-primary btn-sm me-2">閱讀全文</button>
                            <button class="btn btn-outline-secondary btn-sm me-2">收藏</button>
                            <button class="btn btn-outline-info btn-sm">分享</button>
                        </div>
                    </div>

                    <!-- 結果項目 3 -->
                    <div class="scripture-card">
                        <div class="scripture-title">觀世音菩薩普門品</div>
                        <div class="scripture-meta">
                            <span><i class="fas fa-book me-1"></i>法華經</span>
                            <span><i class="fas fa-user me-1"></i>鳩摩羅什 譯</span>
                            <span><i class="fas fa-star me-1"></i>4.9</span>
                            <span><i class="fas fa-eye me-1"></i>3,127 閱讀</span>
                        </div>
                        <div class="scripture-content">
                            爾時無盡意菩薩即從座起，偏袒右肩，合掌向佛，而作是言：「世尊！觀世音菩薩以何因緣名觀世音？」...
                        </div>
                        <p class="text-muted">普門品詳細介紹觀世音菩薩的慈悲功德，教導我們如何培養慈悲心，在困難時刻獲得菩薩的加持與保護。</p>
                        <div class="scripture-tags">
                            <span class="tag">慈悲</span>
                            <span class="tag">觀音</span>
                            <span class="tag">救度</span>
                            <span class="tag">初級</span>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-primary btn-sm me-2">閱讀全文</button>
                            <button class="btn btn-outline-secondary btn-sm me-2">收藏</button>
                            <button class="btn btn-outline-info btn-sm">分享</button>
                        </div>
                    </div>

                    <!-- 結果項目 4 -->
                    <div class="scripture-card">
                        <div class="scripture-title">金剛般若波羅蜜經</div>
                        <div class="scripture-meta">
                            <span><i class="fas fa-book me-1"></i>般若部</span>
                            <span><i class="fas fa-user me-1"></i>鳩摩羅什 譯</span>
                            <span><i class="fas fa-star me-1"></i>4.7</span>
                            <span><i class="fas fa-eye me-1"></i>1,654 閱讀</span>
                        </div>
                        <div class="scripture-content">
                            如是我聞：一時，佛在舍衛國祇樹給孤獨園，與大比丘眾千二百五十人俱...
                        </div>
                        <p class="text-muted">金剛經是般若經典的重要代表，深入闡述了空性智慧，幫助修行者破除執著，獲得究竟的解脫。適合有一定基礎的修習者。</p>
                        <div class="scripture-tags">
                            <span class="tag">般若</span>
                            <span class="tag">空性</span>
                            <span class="tag">解脫</span>
                            <span class="tag">高級</span>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-primary btn-sm me-2">閱讀全文</button>
                            <button class="btn btn-outline-secondary btn-sm me-2">收藏</button>
                            <button class="btn btn-outline-info btn-sm">分享</button>
                        </div>
                    </div>
                </div>

                <!-- 分頁 -->
                <nav class="pagination-custom">
                    <ul class="pagination">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1">上一頁</a>
                        </li>
                        <li class="page-item active">
                            <a class="page-link" href="#">1</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">2</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">3</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">...</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">8</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">下一頁</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </section>

        <!-- 推薦經文 -->
        <section class="py-5" style="background: var(--light-bg);">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h3 class="text-center fw-bold mb-5">推薦經文</h3>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="category-card h-100">
                            <div class="category-icon">
                                <i class="fas fa-spa"></i>
                            </div>
                            <h6 class="fw-bold">壓力緩解經文</h6>
                            <p class="small text-muted">適合現代人快節奏生活的舒緩經典</p>
                            <button class="btn btn-outline-primary btn-sm">查看更多</button>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="category-card h-100">
                            <div class="category-icon">
                                <i class="fas fa-brain"></i>
                            </div>
                            <h6 class="fw-bold">智慧開發經文</h6>
                            <p class="small text-muted">提升洞察力與理解力的經典選集</p>
                            <button class="btn btn-outline-primary btn-sm">查看更多</button>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="category-card h-100">
                            <div class="category-icon">
                                <i class="fas fa-hands-helping"></i>
                            </div>
                            <h6 class="fw-bold">慈悲培養經文</h6>
                            <p class="small text-muted">增進愛心與同理心的修習指導</p>
                            <button class="btn btn-outline-primary btn-sm">查看更多</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // 導航欄滾動效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar-custom');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // 切換進階搜尋
        function toggleAdvancedSearch() {
            const advancedSearch = document.getElementById('advancedSearch');
            advancedSearch.classList.toggle('show');
        }

        // 執行搜尋
        function performSearch() {
            const searchInput = document.getElementById('searchInput').value;
            if (searchInput.trim() !== '') {
                showSearchResults();
                updateResultCount(42);
            }
        }

        // 快速搜尋
        function quickSearch(keyword) {
            document.getElementById('searchInput').value = keyword;
            performSearch();
        }

        // 顯示搜尋結果
        function showSearchResults() {
            const resultsSection = document.getElementById('searchResults');
            resultsSection.style.display = 'block';
            resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }

        // 更新結果數量
        function updateResultCount(count) {
            document.getElementById('resultCount').textContent = count;
        }

        // 執行進階搜尋
        function performAdvancedSearch() {
            showSearchResults();
            updateResultCount(15);
            toggleAdvancedSearch();
        }

        // 重置搜尋
        function resetSearch() {
            document.getElementById('searchInput').value = '';
            document.querySelectorAll('select').forEach(select => select.selectedIndex = 0);
            document.getElementById('searchResults').style.display = 'none';
        }

        // 篩選功能
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                const filter = this.getAttribute('data-filter');
                filterResults(filter);
            });
        });

        function filterResults(filter) {
            const cards = document.querySelectorAll('.scripture-card');
            cards.forEach(card => {
                if (filter === 'all') {
                    card.style.display = 'block';
                } else {
                    const tags = card.querySelectorAll('.tag');
                    let hasTag = false;
                    tags.forEach(tag => {
                        if (tag.textContent.toLowerCase().includes(filter)) {
                            hasTag = true;
                        }
                    });
                    card.style.display = hasTag ? 'block' : 'none';
                }
            });
        }

        // 搜尋框回車事件
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // 卡片動畫
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        // 為各種元素添加動畫
        document.addEventListener('DOMContentLoaded', function() {
            const animatedElements = document.querySelectorAll('.category-card, .scripture-card');
            
            animatedElements.forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = `all 0.6s ease ${index * 0.1}s`;
                observer.observe(el);
            });
        });

        // 收藏功能
        document.addEventListener('click', function(e) {
            if (e.target.textContent === '收藏') {
                e.target.textContent = '已收藏';
                e.target.classList.remove('btn-outline-secondary');
                e.target.classList.add('btn-secondary');
            }
        });
    </script>
</body>
</html>