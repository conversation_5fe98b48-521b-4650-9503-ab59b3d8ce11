# 佛教身心療癒網站 - Docker 完整部署配置

# ========== docker-compose.yml ==========
version: '3.8'

services:
  # Node.js API 服務
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: buddhist-healing-api
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      PORT: 3000
      MONGODB_URI: mongodb://mongo-primary:27017,mongo-secondary:27017/buddhist_healing?replicaSet=rs0
      REDIS_URL: redis://redis-master:6379
      ELASTICSEARCH_URL: http://elasticsearch:9200
      JWT_SECRET: ${JWT_SECRET}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY}
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      SMTP_HOST: ${SMTP_HOST}
      SMTP_PORT: ${SMTP_PORT}
      SMTP_USER: ${SMTP_USER}
      SMTP_PASS: ${SMTP_PASS}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_REGION: ${AWS_REGION}
      AWS_S3_BUCKET: ${AWS_S3_BUCKET}
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/public/uploads
    depends_on:
      - mongo-primary
      - redis-master
      - elasticsearch
    networks:
      - buddhist-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: "0.5"
        reservations:
          memory: 512M
          cpus: "0.25"

  # MongoDB 主節點
  mongo-primary:
    image: mongo:6.0
    container_name: mongo-primary
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USER}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
      MONGO_INITDB_DATABASE: buddhist_healing
      MONGO_REPLICA_SET_NAME: rs0
    volumes:
      - mongo-primary-data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
      - ./scripts/mongo-replica-init.sh:/docker-entrypoint-initdb.d/mongo-replica-init.sh:ro
    networks:
      - buddhist-network
    command: ["--replSet", "rs0", "--bind_ip_all", "--keyFile", "/opt/keyfile"]
    secrets:
      - mongo-keyfile

  # MongoDB 副節點
  mongo-secondary:
    image: mongo:6.0
    container_name: mongo-secondary
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USER}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
    volumes:
      - mongo-secondary-data:/data/db
    networks:
      - buddhist-network
    command: ["--replSet", "rs0", "--bind_ip_all", "--keyFile", "/opt/keyfile"]
    depends_on:
      - mongo-primary
    secrets:
      - mongo-keyfile

  # Redis 主節點
  redis-master:
    image: redis:7.0-alpine
    container_name: redis-master
    restart: unless-stopped
    ports:
      - "6379:6379"
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    volumes:
      - redis-master-data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - buddhist-network
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass ${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 3s
      retries: 5

  # Redis 從節點
  redis-replica:
    image: redis:7.0-alpine
    container_name: redis-replica
    restart: unless-stopped
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    volumes:
      - redis-replica-data:/data
    networks:
      - buddhist-network
    command: redis-server --slaveof redis-master 6379 --requirepass ${REDIS_PASSWORD} --masterauth ${REDIS_PASSWORD}
    depends_on:
      - redis-master

  # Elasticsearch
  elasticsearch:
    image: elasticsearch:8.9.0
    container_name: elasticsearch
    restart: unless-stopped
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      discovery.type: single-node
      cluster.name: buddhist-healing-cluster
      node.name: elasticsearch
      xpack.security.enabled: false
      xpack.security.enrollment.enabled: false
      ES_JAVA_OPTS: -Xms1g -Xmx1g
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
      - ./config/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml
    networks:
      - buddhist-network
    ulimits:
      memlock:
        soft: -1
        hard: -1
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/nginx/sites-enabled:/etc/nginx/sites-enabled:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - app
    networks:
      - buddhist-network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus 監控
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - buddhist-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'

  # Grafana 儀表板
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_ADMIN_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - buddhist-network
    depends_on:
      - prometheus

  # 日誌收集 (Filebeat)
  filebeat:
    image: elastic/filebeat:8.9.0
    container_name: filebeat
    restart: unless-stopped
    user: root
    volumes:
      - ./config/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - ./logs:/var/log/app:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - buddhist-network
    depends_on:
      - elasticsearch

  # 備份服務
  backup:
    image: alpine:latest
    container_name: backup-service
    restart: "no"
    volumes:
      - mongo-primary-data:/backup/mongo
      - redis-master-data:/backup/redis
      - ./scripts/backup.sh:/backup.sh:ro
      - ./backups:/backups
    networks:
      - buddhist-network
    command: crond -f -d 8
    environment:
      BACKUP_SCHEDULE: "0 2 * * *"  # 每天凌晨2點備份

# 網絡配置
networks:
  buddhist-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 數據卷
volumes:
  mongo-primary-data:
    driver: local
  mongo-secondary-data:
    driver: local
  redis-master-data:
    driver: local
  redis-replica-data:
    driver: local
  elasticsearch-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

# 密鑰管理
secrets:
  mongo-keyfile:
    file: ./secrets/mongo-keyfile

# ========== Dockerfile ==========
# 多階段構建 Dockerfile

# 開發階段
FROM node:18-alpine AS development
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
EXPOSE 3000
CMD ["npm", "run", "dev"]

# 構建階段
FROM node:18-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# 生產階段
FROM node:18-alpine AS production
WORKDIR /app

# 創建非特權用戶
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# 安裝必要的系統包
RUN apk add --no-cache curl dumb-init

# 複製應用程式
COPY --from=build /app/node_modules ./node_modules
COPY . .

# 創建日誌目錄
RUN mkdir -p logs uploads && \
    chown -R nodejs:nodejs /app

# 切換到非特權用戶
USER nodejs

# 健康檢查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# 暴露端口
EXPOSE 3000

# 啟動應用
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "src/server.js"]

# ========== .dockerignore ==========
node_modules
npm-debug.log
.git
.gitignore
README.md
.env
.env.local
.env.production
.env.test
coverage
.nyc_output
logs/*
!logs/.gitkeep
uploads/*
!uploads/.gitkeep
backups
.docker
Dockerfile*
docker-compose*

# ========== Nginx 配置 ==========
# config/nginx.conf
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日誌格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                   '$status $body_bytes_sent "$http_referer" '
                   '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    # 基本設置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Gzip 壓縮
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 速率限制
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=upload:10m rate=2r/m;

    # 上游服務器
    upstream api_backend {
        least_conn;
        server app:3000 max_fails=3 fail_timeout=30s;
    }

    # HTTP 重定向到 HTTPS
    server {
        listen 80;
        server_name _;
        return 301 https://$host$request_uri;
    }

    # HTTPS 主配置
    server {
        listen 443 ssl http2;
        server_name localhost api.buddhisthealing.com;

        # SSL 配置
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # 安全頭部
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # API 路由
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://api_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 超時設置
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
            
            # 緩存靜態內容
            location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
            }
        }

        # 認證路由特殊限制
        location /api/auth/ {
            limit_req zone=auth burst=5 nodelay;
            
            proxy_pass http://api_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 文件上傳
        location /api/upload/ {
            limit_req zone=upload burst=3 nodelay;
            client_max_body_size 100M;
            
            proxy_pass http://api_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 上傳進度
            proxy_request_buffering off;
        }

        # WebSocket 支援
        location /socket.io/ {
            proxy_pass http://api_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 靜態文件服務
        location /uploads/ {
            alias /app/public/uploads/;
            expires 1y;
            add_header Cache-Control "public, immutable";
            
            # 安全設置
            location ~* \.(php|pl|py|jsp|asp|sh|cgi)$ {
                deny all;
            }
        }

        # 健康檢查
        location /health {
            proxy_pass http://api_backend;
            access_log off;
        }

        # 監控端點（僅內部訪問）
        location /metrics {
            allow **********/16;  # 只允許 Docker 網絡
            deny all;
            proxy_pass http://api_backend;
        }
    }
}

# ========== Redis 配置 ==========
# config/redis.conf
# 基本配置
bind 0.0.0.0
port 6379
protected-mode yes
tcp-backlog 511
timeout 0
tcp-keepalive 300

# 數據持久化
save 900 1
save 300 10
save 60 10000
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# 日誌
loglevel notice
logfile ""

# 記憶體管理
maxmemory 512mb
maxmemory-policy allkeys-lru

# 安全
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""

# ========== Prometheus 配置 ==========
# config/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  # Node.js 應用程式
  - job_name: 'buddhist-healing-api'
    static_configs:
      - targets: ['app:3000']
    metrics_path: /metrics
    scrape_interval: 5s

  # MongoDB
  - job_name: 'mongodb'
    static_configs:
      - targets: ['mongo-primary:27017']

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-master:6379']

  # Nginx
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']

  # Elasticsearch
  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch:9200']

# ========== 部署腳本 ==========
# scripts/deploy.sh
#!/bin/bash
set -e

echo "🚀 開始部署佛教身心療癒網站..."

# 檢查環境變數
if [ ! -f .env ]; then
    echo "❌ 錯誤：找不到 .env 文件"
    exit 1
fi

# 載入環境變數
source .env

# 檢查 Docker 和 Docker Compose
if ! command -v docker &> /dev/null; then
    echo "❌ 錯誤：Docker 未安裝"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ 錯誤：Docker Compose 未安裝"
    exit 1
fi

# 創建必要的目錄
echo "📁 創建目錄結構..."
mkdir -p logs/{app,nginx} uploads backups ssl secrets

# 生成 MongoDB 密鑰文件
echo "🔐 生成 MongoDB 密鑰..."
openssl rand -base64 756 > secrets/mongo-keyfile
chmod 400 secrets/mongo-keyfile

# 拉取最新鏡像
echo "📥 拉取 Docker 鏡像..."
docker-compose pull

# 構建應用程式鏡像
echo "🔨 構建應用程式..."
docker-compose build app

# 停止現有服務
echo "⏹️ 停止現有服務..."
docker-compose down

# 啟動服務
echo "▶️ 啟動服務..."
docker-compose up -d

# 等待服務啟動
echo "⏳ 等待服務啟動..."
sleep 30

# 初始化 MongoDB 副本集
echo "🗄️ 初始化 MongoDB..."
docker-compose exec mongo-primary mongosh --eval "
rs.initiate({
  _id: 'rs0',
  members: [
    { _id: 0, host: 'mongo-primary:27017', priority: 1 },
    { _id: 1, host: 'mongo-secondary:27017', priority: 0.5 }
  ]
})
"

# 檢查服務健康狀態
echo "🏥 檢查服務健康狀態..."
services=("app" "mongo-primary" "redis-master" "elasticsearch" "nginx")

for service in "${services[@]}"; do
    if docker-compose ps | grep -q "$service.*Up"; then
        echo "✅ $service 服務正常運行"
    else
        echo "❌ $service 服務啟動失敗"
        docker-compose logs $service
        exit 1
    fi
done

# 顯示服務端點
echo "🌐 服務端點："
echo "  API: https://localhost/api"
echo "  健康檢查: https://localhost/health"
echo "  Grafana: http://localhost:3001"
echo "  Prometheus: http://localhost:9090"
echo "  Elasticsearch: http://localhost:9200"

echo "🎉 部署完成！"

# ========== 備份腳本 ==========
# scripts/backup.sh
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"

echo "📂 開始備份 - $DATE"

# MongoDB 備份
echo "🗄️ 備份 MongoDB..."
docker-compose exec -T mongo-primary mongodump \
    --host mongo-primary:27017 \
    --db buddhist_healing \
    --out /tmp/backup_$DATE

docker-compose exec -T mongo-primary tar -czf /tmp/mongodb_$DATE.tar.gz /tmp/backup_$DATE
docker cp $(docker-compose ps -q mongo-primary):/tmp/mongodb_$DATE.tar.gz $BACKUP_DIR/

# Redis 備份
echo "💾 備份 Redis..."
docker-compose exec -T redis-master redis-cli --rdb /tmp/redis_$DATE.rdb
docker cp $(docker-compose ps -q redis-master):/tmp/redis_$DATE.rdb $BACKUP_DIR/

# 應用程式日誌備份
echo "📝 備份日誌..."
tar -czf $BACKUP_DIR/logs_$DATE.tar.gz logs/

# 清理舊備份（保留30天）
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
find $BACKUP_DIR -name "*.rdb" -mtime +30 -delete

echo "✅ 備份完成: $BACKUP_DIR"

# ========== 監控腳本 ==========
# scripts/monitor.sh
#!/bin/bash

# 服務健康檢查
check_service_health() {
    local service=$1
    local url=$2
    
    if curl -f -s "$url" > /dev/null; then
        echo "✅ $service 健康"
        return 0
    else
        echo "❌ $service 不健康"
        return 1
    fi
}

echo "🏥 檢查服務健康狀態..."

# 檢查各服務
check_service_health "API" "http://localhost:3000/health"
check_service_health "Elasticsearch" "http://localhost:9200/_cluster/health"
check_service_health "Grafana" "http://localhost:3001/api/health"

# 檢查 Redis
if docker-compose exec -T redis-master redis-cli ping | grep -q "PONG"; then
    echo "✅ Redis 健康"
else
    echo "❌ Redis 不健康"
fi

# 檢查 MongoDB
if docker-compose exec -T mongo-primary mongosh --eval "db.runCommand('ping')" | grep -q "ok"; then
    echo "✅ MongoDB 健康"
else
    echo "❌ MongoDB 不健康"
fi

# 檢查磁碟空間
df -h | grep -E "/(|var|home)" | awk '{print $5 " " $6}' | while read output; do
    usage=$(echo $output | awk '{print $1}' | sed 's/%//g')
    partition=$(echo $output | awk '{print $2}')
    
    if [ $usage -ge 90 ]; then
        echo "⚠️ 警告：$partition 磁碟使用率 $usage%"
    fi
done

echo "✅ 監控檢查完成"