<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>傳承體系 - 佛學智慧</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.6;
            color: #333;
            background: transparent;
            padding: 20px;
        }

        /* 分類介紹區 */
        .category-intro {
            background: linear-gradient(135deg, #ffecd2, #fcb69f);
            color: #333;
            padding: 40px 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .category-intro::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.2) 1px, transparent 1px);
            background-size: 30px 30px;
            animation: float 20s linear infinite;
        }

        @keyframes float {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .intro-content {
            position: relative;
            z-index: 2;
        }

        .intro-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #8B4513;
            animation: breathe 3s infinite;
        }

        @keyframes breathe {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .intro-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #8B4513;
        }

        .intro-description {
            font-size: 1.1rem;
            opacity: 0.8;
            line-height: 1.8;
            color: #A0522D;
        }

        /* 傳承體系 */
        .tradition-systems {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .tradition-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 6px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border-top: 5px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .tradition-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--tradition-color), var(--tradition-color-light));
            transition: height 0.3s ease;
        }

        .tradition-card.gelug {
            --tradition-color: #FFD700;
            --tradition-color-light: #FFF8DC;
        }

        .tradition-card.kagyu {
            --tradition-color: #4169E1;
            --tradition-color-light: #E6F3FF;
        }

        .tradition-card.nyingma {
            --tradition-color: #DC143C;
            --tradition-color-light: #FFE4E1;
        }

        .tradition-card.sakya {
            --tradition-color: #800080;
            --tradition-color-light: #F8E6FF;
        }

        .tradition-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.15);
        }

        .tradition-card:hover::before {
            height: 15px;
        }

        .tradition-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .tradition-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            background: var(--tradition-color);
        }

        .tradition-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
        }

        .tradition-subtitle {
            font-size: 0.9rem;
            color: #666;
            opacity: 0.8;
        }

        .tradition-description {
            color: #555;
            line-height: 1.7;
            margin-bottom: 20px;
        }

        .tradition-features {
            list-style: none;
            padding: 0;
        }

        .tradition-features li {
            padding: 8px 0;
            color: #666;
            position: relative;
            padding-left: 25px;
        }

        .tradition-features li::before {
            content: '◆';
            position: absolute;
            left: 0;
            color: var(--tradition-color);
            font-weight: bold;
        }

        /* 精選文章 */
        .featured-articles {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .articles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .article-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid #e9ecef;
        }

        .article-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-color: #ffecd2;
        }

        .article-icon {
            color: #fcb69f;
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .article-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .article-excerpt {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .article-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: #999;
        }

        /* 傳承特色 */
        .tradition-features-section {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid #e1bee7;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .feature-item {
            background: white;
            border-radius: 10px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ffecd2, #fcb69f);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            color: #8B4513;
            font-size: 1.8rem;
        }

        .feature-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .feature-description {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.6;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            .category-intro {
                padding: 30px 20px;
            }

            .intro-title {
                font-size: 1.5rem;
            }

            .tradition-systems,
            .articles-grid,
            .features-grid {
                grid-template-columns: 1fr;
            }

            .tradition-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- 分類介紹 -->
    <div class="category-intro">
        <div class="intro-content">
            <div class="intro-icon">
                <i class="fas fa-tree"></i>
            </div>
            <h1 class="intro-title">傳承體系</h1>
            <p class="intro-description">
                佛教經過兩千五百多年的發展，形成了豐富多元的傳承體系。每個傳承都有其獨特的修行方法、
                教學風格和文化特色，如同不同的河流最終匯入解脫的大海。
            </p>
        </div>
    </div>

    <!-- 藏傳佛教四大教派 -->
    <div class="tradition-systems">
        <div class="tradition-card gelug" data-tradition="gelug">
            <div class="tradition-header">
                <div class="tradition-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div>
                    <h3 class="tradition-title">格魯派</h3>
                    <p class="tradition-subtitle">黃教 · 宗喀巴大師創立</p>
                </div>
            </div>
            <p class="tradition-description">
                格魯派強調聞思修並重，建立了完整的學院制度。以《菩提道次第廣論》為核心教學體系，
                注重邏輯辯證和漸次修行。
            </p>
            <ul class="tradition-features">
                <li>嚴格的戒律和學院制度</li>
                <li>系統的教理學習體系</li>
                <li>中觀應成派的哲學觀點</li>
                <li>達賴喇嘛和班禪喇嘛轉世系統</li>
            </ul>
        </div>

        <div class="tradition-card kagyu" data-tradition="kagyu">
            <div class="tradition-header">
                <div class="tradition-icon">
                    <i class="fas fa-mountain"></i>
                </div>
                <div>
                    <h3 class="tradition-title">噶舉派</h3>
                    <p class="tradition-subtitle">白教 · 密勒日巴傳承</p>
                </div>
            </div>
            <p class="tradition-description">
                噶舉派以實修為主，強調上師與弟子間的直接傳承。密勒日巴的苦行精神和即身成佛的修行方法
                是其特色，重視大手印和那洛六法的修持。
            </p>
            <ul class="tradition-features">
                <li>重視實修和親近上師</li>
                <li>大手印禪修傳統</li>
                <li>那洛六法密續修持</li>
                <li>噶瑪巴轉世制度</li>
            </ul>
        </div>

        <div class="tradition-card nyingma" data-tradition="nyingma">
            <div class="tradition-header">
                <div class="tradition-icon">
                    <i class="fas fa-fire"></i>
                </div>
                <div>
                    <h3 class="tradition-title">寧瑪派</h3>
                    <p class="tradition-subtitle">紅教 · 蓮花生大士傳承</p>
                </div>
            </div>
            <p class="tradition-description">
                寧瑪派是藏傳佛教最古老的教派，保持了印度密教的原始傳統。以大圓滿法為最高修持，
                重視伏藏經典和虹身成就。
            </p>
            <ul class="tradition-features">
                <li>最古老的藏傳佛教傳承</li>
                <li>大圓滿法的修持傳統</li>
                <li>豐富的伏藏經典傳統</li>
                <li>虹身成就的修行目標</li>
            </ul>
        </div>

        <div class="tradition-card sakya" data-tradition="sakya">
            <div class="tradition-header">
                <div class="tradition-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <div>
                    <h3 class="tradition-title">薩迦派</h3>
                    <p class="tradition-subtitle">花教 · 昆氏家族傳承</p>
                </div>
            </div>
            <p class="tradition-description">
                薩迦派以學術研究和密續修行並重，擁有豐富的經典註釋傳統。道果法是其核心修持，
                結合了顯密圓融的修行體系。
            </p>
            <ul class="tradition-features">
                <li>學術研究與密續並重</li>
                <li>道果法的核心修持</li>
                <li>昆氏家族世襲傳承</li>
                <li>豐富的經典註釋傳統</li>
            </ul>
        </div>
    </div>

    <!-- 精選文章 -->
    <div class="featured-articles">
        <h2 class="section-title">
            <i class="fas fa-star"></i>
            傳承體系精選
        </h2>
        <div class="articles-grid" id="tradition-articles">
            <!-- 文章將通過 JavaScript 動態生成 -->
        </div>
    </div>

    <!-- 傳承特色 -->
    <div class="tradition-features-section">
        <h2 class="section-title">
            <i class="fas fa-gem"></i>
            傳承修行特色
        </h2>
        <div class="features-grid">
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-book"></i>
                </div>
                <h3 class="feature-title">經典傳承</h3>
                <p class="feature-description">
                    各傳承都有其核心經典和註釋體系，形成獨特的教學方法。
                </p>
            </div>
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-user-graduate"></i>
                </div>
                <h3 class="feature-title">師承制度</h3>
                <p class="feature-description">
                    重視上師與弟子的傳承關係，確保法脈的清淨傳遞。
                </p>
            </div>
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-meditation"></i>
                </div>
                <h3 class="feature-title">修行方法</h3>
                <p class="feature-description">
                    每個傳承發展出獨特的禪修技巧和修行次第。
                </p>
            </div>
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-globe"></i>
                </div>
                <h3 class="feature-title">文化融合</h3>
                <p class="feature-description">
                    佛教與各地文化融合，形成豐富多元的表現形式。
                </p>
            </div>
        </div>
    </div>

    <script>
        // 傳承體系相關文章數據
        const traditionArticles = [
            {
                id: 18,
                title: "藏傳佛教的傳承體系",
                excerpt: "藏傳佛教分為四大教派：格魯派、噶舉派、寧瑪派和薩迦派。每個教派都有其獨特的修行方法、教學體系和文化特色，形成了豐富多元的傳承體系。",
                difficulty: "intermediate",
                date: "2024-12-12",
                views: 2134,
                likes: 189,
                icon: "fas fa-tree"
            },
            {
                id: 24,
                title: "格魯派的教學體系",
                excerpt: "格魯派以宗喀巴大師的《菩提道次第廣論》為核心，建立了完整的學院制度。重視聞思修並重，強調漸次修行的重要性。",
                difficulty: "intermediate",
                date: "2024-11-15",
                views: 1876,
                likes: 143,
                icon: "fas fa-graduation-cap"
            },
            {
                id: 25,
                title: "寧瑪派的大圓滿法",
                excerpt: "寧瑪派是藏傳佛教最古老的教派，以大圓滿法為最高修持。文章介紹大圓滿法的理論基礎和修行方法，以及虹身成就的傳說。",
                difficulty: "advanced",
                date: "2024-10-20",
                views: 1543,
                likes: 128,
                icon: "fas fa-fire"
            }
        ];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderArticles();
            initializeNavigation();
        });

        // 渲染文章
        function renderArticles() {
            const container = document.getElementById('tradition-articles');
            
            container.innerHTML = traditionArticles.map(article => `
                <div class="article-card" data-article-id="${article.id}">
                    <div class="article-icon">
                        <i class="${article.icon}"></i>
                    </div>
                    <h3 class="article-title">${article.title}</h3>
                    <p class="article-excerpt">${article.excerpt}</p>
                    <div class="article-meta">
                        <span>${article.difficulty === 'beginner' ? '入門' : article.difficulty === 'intermediate' ? '進階' : '高級'}</span>
                        <span>${article.date}</span>
                    </div>
                </div>
            `).join('');
        }

        // 初始化導航
        function initializeNavigation() {
            // 傳承卡片點擊
            document.querySelectorAll('.tradition-card').forEach(card => {
                card.addEventListener('click', function() {
                    const tradition = this.getAttribute('data-tradition');
                    
                    if (window.parent !== window) {
                        window.parent.postMessage({
                            type: 'filter',
                            filterType: 'tradition',
                            value: tradition
                        }, '*');
                    }
                });
            });

            // 文章點擊
            document.addEventListener('click', function(e) {
                const articleElement = e.target.closest('[data-article-id]');
                if (articleElement) {
                    const articleId = articleElement.getAttribute('data-article-id');
                    
                    if (window.parent !== window) {
                        window.parent.postMessage({
                            type: 'article_view',
                            articleId: articleId
                        }, '*');
                    }
                }
            });
        }
    </script>
</body>
</html>