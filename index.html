<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>澄源閱讀 - 佛學與身心療癒智慧</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 加載動畫 -->
    <div id="loader" class="loader">
        <div class="lotus-loader">
            <div class="lotus-petal"></div>
            <div class="lotus-petal"></div>
            <div class="lotus-petal"></div>
            <div class="lotus-petal"></div>
            <div class="lotus-petal"></div>
            <div class="lotus-petal"></div>
        </div>
        <p>澄源閱讀</p>
    </div>

    <!-- 頂部導航欄 -->
    <header class="header" id="header">
        <nav class="navbar">
            <div class="nav-brand">
                <i class="fas fa-lotus"></i>
                <span>澄源閱讀</span>
            </div>
            
            <div class="nav-menu" id="nav-menu">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="javascript:void(0)" class="nav-link" data-page="home">
                            <i class="fas fa-home"></i>
                            <span>首頁</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="javascript:void(0)" class="nav-link" data-page="dharma">
                            <i class="fas fa-dharmachakra"></i>
                            <span>佛學智慧</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="javascript:void(0)" class="nav-link" data-page="healing">
                            <i class="fas fa-spa"></i>
                            <span>身心療癒</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="javascript:void(0)" class="nav-link" data-page="research">
                            <i class="fas fa-microscope"></i>
                            <span>最新研究</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="javascript:void(0)" class="nav-link" data-page="search">
                            <i class="fas fa-search"></i>
                            <span>搜索</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="javascript:void(0)" class="nav-link" data-page="favorites">
                            <i class="fas fa-heart"></i>
                            <span>收藏</span>
                        </a>
                    </li>
                </ul>
            </div>

            <div class="nav-controls">
                <button class="theme-toggle" id="theme-toggle" title="切換主題">
                    <i class="fas fa-moon"></i>
                </button>
                <button class="nav-toggle" id="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </nav>
    </header>

    <!-- 側邊欄（移動端） -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="user-details">
                    <h3>歡迎訪問</h3>
                    <p>澄源閱讀</p>
                </div>
            </div>
        </div>
        
        <nav class="sidebar-nav">
            <ul class="sidebar-menu">
                <li class="menu-item">
                    <a href="javascript:void(0)" class="menu-link" data-page="home">
                        <i class="fas fa-home"></i>
                        <span>首頁</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="javascript:void(0)" class="menu-link" data-page="dharma">
                        <i class="fas fa-dharmachakra"></i>
                        <span>佛學智慧</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="javascript:void(0)" class="menu-link" data-page="healing">
                        <i class="fas fa-spa"></i>
                        <span>身心療癒</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="javascript:void(0)" class="menu-link" data-page="research">
                        <i class="fas fa-microscope"></i>
                        <span>最新研究</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="javascript:void(0)" class="menu-link" data-page="search">
                        <i class="fas fa-search"></i>
                        <span>搜索</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="javascript:void(0)" class="menu-link" data-page="favorites">
                        <i class="fas fa-heart"></i>
                        <span>收藏</span>
                    </a>
                </li>
            </ul>
        </nav>

        <div class="sidebar-footer">
            <button class="logout-btn" id="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                <span>登出</span>
            </button>
        </div>
    </aside>

    <!-- 主要內容區域 -->
    <main class="main-content">
        <!-- iframe 容器 -->
        <div class="content-container">
            <iframe 
                id="content-frame" 
                class="content-frame"
                src="pages/login.html"
                frameborder="0"
                scrolling="auto">
            </iframe>
        </div>

        <!-- 返回頂部按鈕 -->
        <button class="back-to-top" id="back-to-top">
            <i class="fas fa-arrow-up"></i>
        </button>
    </main>

    <!-- 遮罩層 -->
    <div class="overlay" id="overlay"></div>

    <!-- 通知系統 -->
    <div class="notification-container" id="notification-container"></div>

    <!-- JavaScript -->
    <script src="js/utils.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/main.js"></script>

    <script>
        // 初始化應用
        document.addEventListener('DOMContentLoaded', function() {
            // 檢查登錄狀態
            const isLoggedIn = localStorage.getItem('chengyuan_auth') === 'true';
            
            if (isLoggedIn) {
                // 已登錄，跳轉到首頁
                loadPage('home');
            } else {
                // 未登錄，顯示登錄頁面
                loadPage('login');
            }

            // 初始化主題
            initTheme();
            
            // 隱藏加載動畫
            setTimeout(() => {
                document.getElementById('loader').style.display = 'none';
            }, 1500);
        });

        // 頁面載入函數
        function loadPage(pageName) {
            console.log('Loading page:', pageName); // 調試信息
            const iframe = document.getElementById('content-frame');
            const newSrc = `pages/${pageName}.html`;
            
            // 直接設置新的 src，確保頁面載入
            iframe.src = newSrc;
            
            // 更新活躍狀態
            updateActiveNav(pageName);
            
            // 移動端關閉側邊欄
            closeSidebar();
            
            // 記錄當前頁面
            sessionStorage.setItem('current_page', pageName);
        }

        // 更新導航活躍狀態
        function updateActiveNav(pageName) {
            // 清除所有活躍狀態
            document.querySelectorAll('.nav-link, .menu-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // 設置當前頁面活躍狀態
            document.querySelectorAll(`[data-page="${pageName}"]`).forEach(link => {
                link.classList.add('active');
            });
        }

        // 初始化主題
        function initTheme() {
            const savedTheme = localStorage.getItem('chengyuan_theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);
            
            const themeIcon = document.querySelector('#theme-toggle i');
            themeIcon.className = savedTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        // 主題切換
        document.getElementById('theme-toggle').addEventListener('click', function() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('chengyuan_theme', newTheme);
            
            const icon = this.querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            
            showNotification(`已切換到${newTheme === 'dark' ? '深色' : '淺色'}模式`);
        });

        // 導航點擊事件
        document.addEventListener('click', function(e) {
            if (e.target.closest('[data-page]')) {
                e.preventDefault();
                const pageName = e.target.closest('[data-page]').getAttribute('data-page');
                loadPage(pageName);
            }
        });

        // 移動端導航
        document.getElementById('nav-toggle').addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('active');
            document.getElementById('overlay').classList.toggle('active');
        });

        // 關閉側邊欄
        function closeSidebar() {
            document.getElementById('sidebar').classList.remove('active');
            document.getElementById('overlay').classList.remove('active');
        }

        // 遮罩點擊關閉側邊欄
        document.getElementById('overlay').addEventListener('click', closeSidebar);

        // 登出功能
        document.getElementById('logout-btn').addEventListener('click', function() {
            localStorage.removeItem('chengyuan_auth');
            loadPage('login');
            showNotification('已成功登出');
        });

        // 返回頂部
        document.getElementById('back-to-top').addEventListener('click', function() {
            const iframe = document.getElementById('content-frame');
            if (iframe.contentWindow) {
                iframe.contentWindow.scrollTo({ top: 0, behavior: 'smooth' });
            }
        });

        // 通知系統
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notification-container');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <i class="fas fa-info-circle"></i>
                <span>${message}</span>
                <button class="notification-close">
                    <i class="fas fa-times"></i>
                </button>
            `;
            
            container.appendChild(notification);
            
            // 自動關閉
            setTimeout(() => {
                notification.remove();
            }, 3000);
            
            // 手動關閉
            notification.querySelector('.notification-close').addEventListener('click', () => {
                notification.remove();
            });
        }

        // iframe 載入完成事件
        document.getElementById('content-frame').addEventListener('load', function() {
            // 可以在這裡添加 iframe 載入完成後的處理邏輯
        });

        // 監聽來自 iframe 的消息
        window.addEventListener('message', function(event) {
            console.log('Received message:', event.data); // 調試信息
            
            if (event.data.type === 'login_success') {
                console.log('Login success received, navigating to home');
                localStorage.setItem('chengyuan_auth', 'true');
                setTimeout(() => {
                    loadPage('home');
                    showNotification('登錄成功，歡迎使用澄源閱讀！', 'success');
                }, 500);
            } else if (event.data.type === 'navigate') {
                loadPage(event.data.page);
            } else if (event.data.type === 'notification') {
                showNotification(event.data.message, event.data.level || 'info');
            }
        });
    </script>
</body>
</html>