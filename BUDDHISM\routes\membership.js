const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const authenticate = require('../middleware/auth');

// 驗證錯誤處理中間件
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// 會員方案定義
const membershipPlans = {
  basic: {
    name: '基礎會員',
    price: 0,
    duration: 365, // 天數
    features: [
      '基礎佛學課程',
      '經文閱讀',
      '基礎冥想指導',
      '社群討論'
    ],
    limitations: {
      coursesPerMonth: 3,
      workshopsPerMonth: 1,
      downloadLimit: 10
    }
  },
  premium: {
    name: '高級會員',
    price: 2988,
    duration: 365,
    features: [
      '所有基礎功能',
      '進階佛學課程',
      '個人化學習計劃',
      '專業冥想指導',
      '線上工作坊',
      '學習進度追蹤',
      '證書下載'
    ],
    limitations: {
      coursesPerMonth: 15,
      workshopsPerMonth: 5,
      downloadLimit: 50
    }
  },
  vip: {
    name: 'VIP會員',
    price: 5988,
    duration: 365,
    features: [
      '所有高級功能',
      'VIP專屬課程',
      '一對一指導預約',
      '海外研修機會',
      '專屬客服支援',
      '優先活動報名',
      '無限資源下載'
    ],
    limitations: {
      coursesPerMonth: -1, // 無限制
      workshopsPerMonth: -1,
      downloadLimit: -1
    }
  }
};

// 獲取所有會員方案
router.get('/plans', async (req, res) => {
  try {
    res.json({
      message: 'Membership plans retrieved successfully',
      plans: membershipPlans
    });
  } catch (error) {
    console.error('Get plans error:', error);
    res.status(500).json({ error: 'Failed to fetch membership plans' });
  }
});

// 獲取當前會員狀態
router.get('/status', authenticate, async (req, res) => {
  try {
    const user = await User.findById(req.user._id).select('membership');
    
    const currentPlan = membershipPlans[user.membership.plan];
    const daysRemaining = user.membership.endDate 
      ? Math.max(0, Math.ceil((new Date(user.membership.endDate) - new Date()) / (1000 * 60 * 60 * 24)))
      : null;

    res.json({
      message: 'Membership status retrieved successfully',
      membership: {
        current: {
          plan: user.membership.plan,
          status: user.membership.status,
          startDate: user.membership.startDate,
          endDate: user.membership.endDate,
          autoRenewal: user.membership.autoRenewal,
          daysRemaining: daysRemaining
        },
        planDetails: currentPlan,
        canUpgrade: user.membership.plan !== 'vip',
        canDowngrade: user.membership.plan !== 'basic'
      }
    });
  } catch (error) {
    console.error('Get membership status error:', error);
    res.status(500).json({ error: 'Failed to fetch membership status' });
  }
});

// 訂閱會員方案
router.post('/subscribe', authenticate, [
  body('plan').isIn(['basic', 'premium', 'vip']).withMessage('Invalid membership plan'),
  body('paymentMethod').optional().isIn(['card', 'alipay', 'wechat']).withMessage('Invalid payment method'),
  body('autoRenewal').optional().isBoolean().withMessage('Auto renewal must be boolean')
], handleValidationErrors, async (req, res) => {
  try {
    const { plan, paymentMethod = 'card', autoRenewal = false } = req.body;
    const userId = req.user._id;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // 檢查是否已有相同或更高級的方案
    const planHierarchy = { basic: 1, premium: 2, vip: 3 };
    const currentPlanLevel = planHierarchy[user.membership.plan];
    const newPlanLevel = planHierarchy[plan];

    if (currentPlanLevel >= newPlanLevel && user.membership.status === 'active') {
      return res.status(400).json({ 
        error: 'Cannot downgrade to a lower plan while current plan is active' 
      });
    }

    const selectedPlan = membershipPlans[plan];
    const startDate = new Date();
    const endDate = new Date(startDate.getTime() + (selectedPlan.duration * 24 * 60 * 60 * 1000));

    // 模擬付款處理（實際應整合支付服務）
    if (selectedPlan.price > 0) {
      // 這裡應該整合實際的支付處理邏輯
      console.log(`Processing payment: ${selectedPlan.price} TWD via ${paymentMethod}`);
    }

    // 更新會員狀態
    const updateData = {
      'membership.plan': plan,
      'membership.status': 'active',
      'membership.startDate': startDate,
      'membership.endDate': endDate,
      'membership.autoRenewal': autoRenewal,
      'updated': new Date()
    };

    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { $set: updateData },
      { new: true }
    ).select('membership');

    res.json({
      message: `Successfully subscribed to ${selectedPlan.name}`,
      membership: updatedUser.membership,
      planDetails: selectedPlan,
      transaction: {
        amount: selectedPlan.price,
        currency: 'TWD',
        paymentMethod: paymentMethod,
        timestamp: startDate
      }
    });
  } catch (error) {
    console.error('Subscribe error:', error);
    res.status(500).json({ error: 'Failed to subscribe to membership plan' });
  }
});

// 升級會員方案
router.put('/upgrade', authenticate, [
  body('newPlan').isIn(['premium', 'vip']).withMessage('Invalid upgrade plan'),
  body('paymentMethod').optional().isIn(['card', 'alipay', 'wechat']).withMessage('Invalid payment method')
], handleValidationErrors, async (req, res) => {
  try {
    const { newPlan, paymentMethod = 'card' } = req.body;
    const userId = req.user._id;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // 驗證升級邏輯
    const planHierarchy = { basic: 1, premium: 2, vip: 3 };
    const currentPlanLevel = planHierarchy[user.membership.plan];
    const newPlanLevel = planHierarchy[newPlan];

    if (newPlanLevel <= currentPlanLevel) {
      return res.status(400).json({ error: 'Can only upgrade to a higher plan' });
    }

    if (user.membership.status !== 'active') {
      return res.status(400).json({ error: 'Current membership must be active to upgrade' });
    }

    const newPlanDetails = membershipPlans[newPlan];
    const currentPlanDetails = membershipPlans[user.membership.plan];

    // 計算升級費用（按比例計算）
    const remainingDays = Math.max(0, Math.ceil((new Date(user.membership.endDate) - new Date()) / (1000 * 60 * 60 * 24)));
    const dailyRate = newPlanDetails.price / newPlanDetails.duration;
    const upgradeCost = Math.round(dailyRate * remainingDays);

    // 模擬付款處理
    if (upgradeCost > 0) {
      console.log(`Processing upgrade payment: ${upgradeCost} TWD via ${paymentMethod}`);
    }

    // 更新會員狀態
    const updateData = {
      'membership.plan': newPlan,
      'updated': new Date()
    };

    // 如果是從基礎版升級，設定新的結束日期
    if (user.membership.plan === 'basic') {
      updateData['membership.endDate'] = new Date(Date.now() + (newPlanDetails.duration * 24 * 60 * 60 * 1000));
    }

    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { $set: updateData },
      { new: true }
    ).select('membership');

    res.json({
      message: `Successfully upgraded to ${newPlanDetails.name}`,
      membership: updatedUser.membership,
      planDetails: newPlanDetails,
      transaction: {
        type: 'upgrade',
        amount: upgradeCost,
        currency: 'TWD',
        paymentMethod: paymentMethod,
        timestamp: new Date()
      }
    });
  } catch (error) {
    console.error('Upgrade error:', error);
    res.status(500).json({ error: 'Failed to upgrade membership plan' });
  }
});

// 取消會員自動續費
router.put('/cancel-renewal', authenticate, async (req, res) => {
  try {
    const userId = req.user._id;

    const user = await User.findByIdAndUpdate(
      userId,
      {
        $set: {
          'membership.autoRenewal': false,
          'updated': new Date()
        }
      },
      { new: true }
    ).select('membership');

    res.json({
      message: 'Auto renewal cancelled successfully',
      membership: user.membership
    });
  } catch (error) {
    console.error('Cancel renewal error:', error);
    res.status(500).json({ error: 'Failed to cancel auto renewal' });
  }
});

// 恢復會員自動續費
router.put('/enable-renewal', authenticate, async (req, res) => {
  try {
    const userId = req.user._id;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    if (user.membership.status !== 'active') {
      return res.status(400).json({ error: 'Membership must be active to enable auto renewal' });
    }

    const updatedUser = await User.findByIdAndUpdate(
      userId,
      {
        $set: {
          'membership.autoRenewal': true,
          'updated': new Date()
        }
      },
      { new: true }
    ).select('membership');

    res.json({
      message: 'Auto renewal enabled successfully',
      membership: updatedUser.membership
    });
  } catch (error) {
    console.error('Enable renewal error:', error);
    res.status(500).json({ error: 'Failed to enable auto renewal' });
  }
});

// 取消會員方案（立即生效）
router.delete('/cancel', authenticate, [
  body('reason').optional().isLength({ max: 500 }).withMessage('Reason must be less than 500 characters'),
  body('confirmCancel').equals('CANCEL').withMessage('Must confirm cancellation by typing CANCEL')
], handleValidationErrors, async (req, res) => {
  try {
    const { reason } = req.body;
    const userId = req.user._id;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    if (user.membership.plan === 'basic') {
      return res.status(400).json({ error: 'Cannot cancel basic membership' });
    }

    // 計算退款金額（如果適用）
    const remainingDays = Math.max(0, Math.ceil((new Date(user.membership.endDate) - new Date()) / (1000 * 60 * 60 * 24)));
    const currentPlan = membershipPlans[user.membership.plan];
    const dailyRate = currentPlan.price / currentPlan.duration;
    const refundAmount = Math.round(dailyRate * remainingDays);

    // 降級到基礎會員
    const updateData = {
      'membership.plan': 'basic',
      'membership.status': 'active',
      'membership.autoRenewal': false,
      'membership.endDate': null,
      'updated': new Date()
    };

    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { $set: updateData },
      { new: true }
    ).select('membership');

    // 記錄取消原因
    console.log(`Membership cancelled for user ${userId}: ${reason || 'No reason provided'}`);

    res.json({
      message: 'Membership cancelled successfully, downgraded to basic plan',
      membership: updatedUser.membership,
      refund: {
        amount: refundAmount,
        currency: 'TWD',
        processingTime: '3-5 business days'
      }
    });
  } catch (error) {
    console.error('Cancel membership error:', error);
    res.status(500).json({ error: 'Failed to cancel membership' });
  }
});

// 獲取會員使用統計
router.get('/usage', authenticate, async (req, res) => {
  try {
    const user = await User.findById(req.user._id)
      .populate('progress.coursesCompleted', 'category duration')
      .populate('progress.currentCourses', 'category duration');

    const currentPlan = membershipPlans[user.membership.plan];
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    // 模擬當月使用統計（實際應從活動記錄計算）
    const monthlyUsage = {
      coursesAccessed: user.progress.currentCourses.length,
      workshopsAttended: 0, // 需要從工作坊記錄計算
      downloadsUsed: 5, // 需要從下載記錄計算
      studyTimeThisMonth: Math.floor(user.progress.totalStudyTime * 0.3) // 假設30%是本月
    };

    // 計算使用率
    const usageRates = {
      courses: currentPlan.limitations.coursesPerMonth > 0 
        ? Math.round((monthlyUsage.coursesAccessed / currentPlan.limitations.coursesPerMonth) * 100)
        : 0,
      workshops: currentPlan.limitations.workshopsPerMonth > 0 
        ? Math.round((monthlyUsage.workshopsAttended / currentPlan.limitations.workshopsPerMonth) * 100)
        : 0,
      downloads: currentPlan.limitations.downloadLimit > 0 
        ? Math.round((monthlyUsage.downloadsUsed / currentPlan.limitations.downloadLimit) * 100)
        : 0
    };

    res.json({
      message: 'Usage statistics retrieved successfully',
      usage: {
        current: monthlyUsage,
        limits: currentPlan.limitations,
        usageRates: usageRates,
        period: {
          month: currentMonth + 1,
          year: currentYear
        }
      },
      recommendations: usageRates.courses > 80 || usageRates.workshops > 80 
        ? ['Consider upgrading to access more resources']
        : []
    });
  } catch (error) {
    console.error('Get usage error:', error);
    res.status(500).json({ error: 'Failed to fetch usage statistics' });
  }
});

// 獲取會員福利
router.get('/benefits', authenticate, async (req, res) => {
  try {
    const user = await User.findById(req.user._id).select('membership');
    const currentPlan = membershipPlans[user.membership.plan];

    const benefits = {
      current: currentPlan.features,
      upcoming: [],
      special: []
    };

    // 根據會員等級添加特殊福利
    if (user.membership.plan === 'premium') {
      benefits.special.push('生日月份額外課程額度');
      benefits.upcoming.push('季度專屬研習營');
    } else if (user.membership.plan === 'vip') {
      benefits.special.push('年度海外參訪團優先報名');
      benefits.special.push('專屬客服快速通道');
      benefits.upcoming.push('大師一對一指導機會');
    }

    res.json({
      message: 'Membership benefits retrieved successfully',
      benefits: benefits,
      plan: {
        name: currentPlan.name,
        level: user.membership.plan
      }
    });
  } catch (error) {
    console.error('Get benefits error:', error);
    res.status(500).json({ error: 'Failed to fetch membership benefits' });
  }
});

module.exports = router;