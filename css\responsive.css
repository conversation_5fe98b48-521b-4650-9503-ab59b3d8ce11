/* 澄源閱讀 - 響應式樣式文件 */

/* ===== 斷點定義 ===== */
/*
- xs: 0px - 575.98px (超小設備)
- sm: 576px - 767.98px (小設備)
- md: 768px - 991.98px (中等設備)
- lg: 992px - 1199.98px (大設備)
- xl: 1200px - 1399.98px (超大設備)
- xxl: 1400px+ (超超大設備)
*/

/* ===== 容器響應式 ===== */
.container {
  width: 100%;
  padding-right: var(--spacing-md);
  padding-left: var(--spacing-md);
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

@media (min-width: 1400px) {
  .container {
    max-width: 1320px;
  }
}

.container-fluid {
  width: 100%;
  padding-right: var(--spacing-md);
  padding-left: var(--spacing-md);
  margin-right: auto;
  margin-left: auto;
}

/* ===== 網格系統 ===== */
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: calc(var(--spacing-md) * -0.5);
  margin-left: calc(var(--spacing-md) * -0.5);
}

.col {
  flex-basis: 0;
  flex-grow: 1;
  max-width: 100%;
  padding-right: calc(var(--spacing-md) * 0.5);
  padding-left: calc(var(--spacing-md) * 0.5);
}

/* 自動列寬 */
.col-auto {
  flex: 0 0 auto;
  width: auto;
  max-width: 100%;
}

/* 等寬列 */
.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* 小設備響應式 (≥576px) */
@media (min-width: 576px) {
  .col-sm { flex-basis: 0; flex-grow: 1; max-width: 100%; }
  .col-sm-auto { flex: 0 0 auto; width: auto; max-width: 100%; }
  .col-sm-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .col-sm-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .col-sm-3 { flex: 0 0 25%; max-width: 25%; }
  .col-sm-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-sm-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
  .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
  .col-sm-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
  .col-sm-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .col-sm-9 { flex: 0 0 75%; max-width: 75%; }
  .col-sm-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
  .col-sm-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
  .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
}

/* 中等設備響應式 (≥768px) */
@media (min-width: 768px) {
  .col-md { flex-basis: 0; flex-grow: 1; max-width: 100%; }
  .col-md-auto { flex: 0 0 auto; width: auto; max-width: 100%; }
  .col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .col-md-3 { flex: 0 0 25%; max-width: 25%; }
  .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-md-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
  .col-md-6 { flex: 0 0 50%; max-width: 50%; }
  .col-md-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
  .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .col-md-9 { flex: 0 0 75%; max-width: 75%; }
  .col-md-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
  .col-md-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
  .col-md-12 { flex: 0 0 100%; max-width: 100%; }
}

/* 大設備響應式 (≥992px) */
@media (min-width: 992px) {
  .col-lg { flex-basis: 0; flex-grow: 1; max-width: 100%; }
  .col-lg-auto { flex: 0 0 auto; width: auto; max-width: 100%; }
  .col-lg-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .col-lg-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .col-lg-3 { flex: 0 0 25%; max-width: 25%; }
  .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-lg-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
  .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
  .col-lg-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
  .col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .col-lg-9 { flex: 0 0 75%; max-width: 75%; }
  .col-lg-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
  .col-lg-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
  .col-lg-12 { flex: 0 0 100%; max-width: 100%; }
}

/* 超大設備響應式 (≥1200px) */
@media (min-width: 1200px) {
  .col-xl { flex-basis: 0; flex-grow: 1; max-width: 100%; }
  .col-xl-auto { flex: 0 0 auto; width: auto; max-width: 100%; }
  .col-xl-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .col-xl-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .col-xl-3 { flex: 0 0 25%; max-width: 25%; }
  .col-xl-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-xl-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
  .col-xl-6 { flex: 0 0 50%; max-width: 50%; }
  .col-xl-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
  .col-xl-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .col-xl-9 { flex: 0 0 75%; max-width: 75%; }
  .col-xl-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
  .col-xl-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
  .col-xl-12 { flex: 0 0 100%; max-width: 100%; }
}

/* ===== 顯示/隱藏工具類 ===== */
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-table { display: table !important; }
.d-table-row { display: table-row !important; }
.d-table-cell { display: table-cell !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

@media (min-width: 576px) {
  .d-sm-none { display: none !important; }
  .d-sm-inline { display: inline !important; }
  .d-sm-inline-block { display: inline-block !important; }
  .d-sm-block { display: block !important; }
  .d-sm-table { display: table !important; }
  .d-sm-table-row { display: table-row !important; }
  .d-sm-table-cell { display: table-cell !important; }
  .d-sm-flex { display: flex !important; }
  .d-sm-inline-flex { display: inline-flex !important; }
}

@media (min-width: 768px) {
  .d-md-none { display: none !important; }
  .d-md-inline { display: inline !important; }
  .d-md-inline-block { display: inline-block !important; }
  .d-md-block { display: block !important; }
  .d-md-table { display: table !important; }
  .d-md-table-row { display: table-row !important; }
  .d-md-table-cell { display: table-cell !important; }
  .d-md-flex { display: flex !important; }
  .d-md-inline-flex { display: inline-flex !important; }
}

@media (min-width: 992px) {
  .d-lg-none { display: none !important; }
  .d-lg-inline { display: inline !important; }
  .d-lg-inline-block { display: inline-block !important; }
  .d-lg-block { display: block !important; }
  .d-lg-table { display: table !important; }
  .d-lg-table-row { display: table-row !important; }
  .d-lg-table-cell { display: table-cell !important; }
  .d-lg-flex { display: flex !important; }
  .d-lg-inline-flex { display: inline-flex !important; }
}

@media (min-width: 1200px) {
  .d-xl-none { display: none !important; }
  .d-xl-inline { display: inline !important; }
  .d-xl-inline-block { display: inline-block !important; }
  .d-xl-block { display: block !important; }
  .d-xl-table { display: table !important; }
  .d-xl-table-row { display: table-row !important; }
  .d-xl-table-cell { display: table-cell !important; }
  .d-xl-flex { display: flex !important; }
  .d-xl-inline-flex { display: inline-flex !important; }
}

/* ===== Flex 工具類 ===== */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-row-reverse { flex-direction: row-reverse !important; }
.flex-column-reverse { flex-direction: column-reverse !important; }

.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.justify-content-evenly { justify-content: space-evenly !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.align-content-start { align-content: flex-start !important; }
.align-content-end { align-content: flex-end !important; }
.align-content-center { align-content: center !important; }
.align-content-between { align-content: space-between !important; }
.align-content-around { align-content: space-around !important; }
.align-content-stretch { align-content: stretch !important; }

.align-self-auto { align-self: auto !important; }
.align-self-start { align-self: flex-start !important; }
.align-self-end { align-self: flex-end !important; }
.align-self-center { align-self: center !important; }
.align-self-baseline { align-self: baseline !important; }
.align-self-stretch { align-self: stretch !important; }

.flex-fill { flex: 1 1 auto !important; }
.flex-grow-0 { flex-grow: 0 !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.flex-shrink-0 { flex-shrink: 0 !important; }
.flex-shrink-1 { flex-shrink: 1 !important; }

/* ===== 文字對齊 ===== */
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }
.text-center { text-align: center !important; }
.text-justify { text-align: justify !important; }

@media (min-width: 576px) {
  .text-sm-left { text-align: left !important; }
  .text-sm-right { text-align: right !important; }
  .text-sm-center { text-align: center !important; }
  .text-sm-justify { text-align: justify !important; }
}

@media (min-width: 768px) {
  .text-md-left { text-align: left !important; }
  .text-md-right { text-align: right !important; }
  .text-md-center { text-align: center !important; }
  .text-md-justify { text-align: justify !important; }
}

@media (min-width: 992px) {
  .text-lg-left { text-align: left !important; }
  .text-lg-right { text-align: right !important; }
  .text-lg-center { text-align: center !important; }
  .text-lg-justify { text-align: justify !important; }
}

@media (min-width: 1200px) {
  .text-xl-left { text-align: left !important; }
  .text-xl-right { text-align: right !important; }
  .text-xl-center { text-align: center !important; }
  .text-xl-justify { text-align: justify !important; }
}

/* ===== 字體大小響應式 ===== */
.text-xs { font-size: var(--font-size-xs) !important; }
.text-sm { font-size: var(--font-size-sm) !important; }
.text-base { font-size: var(--font-size-base) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-xl { font-size: var(--font-size-xl) !important; }
.text-2xl { font-size: var(--font-size-2xl) !important; }
.text-3xl { font-size: var(--font-size-3xl) !important; }
.text-4xl { font-size: var(--font-size-4xl) !important; }

@media (max-width: 767.98px) {
  .text-sm-responsive { font-size: var(--font-size-sm) !important; }
  .text-base-responsive { font-size: var(--font-size-sm) !important; }
  .text-lg-responsive { font-size: var(--font-size-base) !important; }
  .text-xl-responsive { font-size: var(--font-size-lg) !important; }
  .text-2xl-responsive { font-size: var(--font-size-xl) !important; }
  .text-3xl-responsive { font-size: var(--font-size-2xl) !important; }
  .text-4xl-responsive { font-size: var(--font-size-3xl) !important; }
}

/* ===== 間距響應式 ===== */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-xs) !important; }
.m-2 { margin: var(--spacing-sm) !important; }
.m-3 { margin: var(--spacing-md) !important; }
.m-4 { margin: var(--spacing-lg) !important; }
.m-5 { margin: var(--spacing-xl) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--spacing-xs) !important; }
.mt-2 { margin-top: var(--spacing-sm) !important; }
.mt-3 { margin-top: var(--spacing-md) !important; }
.mt-4 { margin-top: var(--spacing-lg) !important; }
.mt-5 { margin-top: var(--spacing-xl) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }
.mb-5 { margin-bottom: var(--spacing-xl) !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: var(--spacing-xs) !important; }
.ml-2 { margin-left: var(--spacing-sm) !important; }
.ml-3 { margin-left: var(--spacing-md) !important; }
.ml-4 { margin-left: var(--spacing-lg) !important; }
.ml-5 { margin-left: var(--spacing-xl) !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: var(--spacing-xs) !important; }
.mr-2 { margin-right: var(--spacing-sm) !important; }
.mr-3 { margin-right: var(--spacing-md) !important; }
.mr-4 { margin-right: var(--spacing-lg) !important; }
.mr-5 { margin-right: var(--spacing-xl) !important; }

.mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.mx-1 { margin-left: var(--spacing-xs) !important; margin-right: var(--spacing-xs) !important; }
.mx-2 { margin-left: var(--spacing-sm) !important; margin-right: var(--spacing-sm) !important; }
.mx-3 { margin-left: var(--spacing-md) !important; margin-right: var(--spacing-md) !important; }
.mx-4 { margin-left: var(--spacing-lg) !important; margin-right: var(--spacing-lg) !important; }
.mx-5 { margin-left: var(--spacing-xl) !important; margin-right: var(--spacing-xl) !important; }

.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.my-1 { margin-top: var(--spacing-xs) !important; margin-bottom: var(--spacing-xs) !important; }
.my-2 { margin-top: var(--spacing-sm) !important; margin-bottom: var(--spacing-sm) !important; }
.my-3 { margin-top: var(--spacing-md) !important; margin-bottom: var(--spacing-md) !important; }
.my-4 { margin-top: var(--spacing-lg) !important; margin-bottom: var(--spacing-lg) !important; }
.my-5 { margin-top: var(--spacing-xl) !important; margin-bottom: var(--spacing-xl) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }
.p-5 { padding: var(--spacing-xl) !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: var(--spacing-xs) !important; }
.pt-2 { padding-top: var(--spacing-sm) !important; }
.pt-3 { padding-top: var(--spacing-md) !important; }
.pt-4 { padding-top: var(--spacing-lg) !important; }
.pt-5 { padding-top: var(--spacing-xl) !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: var(--spacing-xs) !important; }
.pb-2 { padding-bottom: var(--spacing-sm) !important; }
.pb-3 { padding-bottom: var(--spacing-md) !important; }
.pb-4 { padding-bottom: var(--spacing-lg) !important; }
.pb-5 { padding-bottom: var(--spacing-xl) !important; }

.pl-0 { padding-left: 0 !important; }
.pl-1 { padding-left: var(--spacing-xs) !important; }
.pl-2 { padding-left: var(--spacing-sm) !important; }
.pl-3 { padding-left: var(--spacing-md) !important; }
.pl-4 { padding-left: var(--spacing-lg) !important; }
.pl-5 { padding-left: var(--spacing-xl) !important; }

.pr-0 { padding-right: 0 !important; }
.pr-1 { padding-right: var(--spacing-xs) !important; }
.pr-2 { padding-right: var(--spacing-sm) !important; }
.pr-3 { padding-right: var(--spacing-md) !important; }
.pr-4 { padding-right: var(--spacing-lg) !important; }
.pr-5 { padding-right: var(--spacing-xl) !important; }

.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.px-1 { padding-left: var(--spacing-xs) !important; padding-right: var(--spacing-xs) !important; }
.px-2 { padding-left: var(--spacing-sm) !important; padding-right: var(--spacing-sm) !important; }
.px-3 { padding-left: var(--spacing-md) !important; padding-right: var(--spacing-md) !important; }
.px-4 { padding-left: var(--spacing-lg) !important; padding-right: var(--spacing-lg) !important; }
.px-5 { padding-left: var(--spacing-xl) !important; padding-right: var(--spacing-xl) !important; }

.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.py-1 { padding-top: var(--spacing-xs) !important; padding-bottom: var(--spacing-xs) !important; }
.py-2 { padding-top: var(--spacing-sm) !important; padding-bottom: var(--spacing-sm) !important; }
.py-3 { padding-top: var(--spacing-md) !important; padding-bottom: var(--spacing-md) !important; }
.py-4 { padding-top: var(--spacing-lg) !important; padding-bottom: var(--spacing-lg) !important; }
.py-5 { padding-top: var(--spacing-xl) !important; padding-bottom: var(--spacing-xl) !important; }

/* ===== 響應式間距 ===== */
@media (min-width: 576px) {
  .m-sm-0 { margin: 0 !important; }
  .m-sm-1 { margin: var(--spacing-xs) !important; }
  .m-sm-2 { margin: var(--spacing-sm) !important; }
  .m-sm-3 { margin: var(--spacing-md) !important; }
  .m-sm-4 { margin: var(--spacing-lg) !important; }
  .m-sm-5 { margin: var(--spacing-xl) !important; }
  
  .p-sm-0 { padding: 0 !important; }
  .p-sm-1 { padding: var(--spacing-xs) !important; }
  .p-sm-2 { padding: var(--spacing-sm) !important; }
  .p-sm-3 { padding: var(--spacing-md) !important; }
  .p-sm-4 { padding: var(--spacing-lg) !important; }
  .p-sm-5 { padding: var(--spacing-xl) !important; }
}

@media (min-width: 768px) {
  .m-md-0 { margin: 0 !important; }
  .m-md-1 { margin: var(--spacing-xs) !important; }
  .m-md-2 { margin: var(--spacing-sm) !important; }
  .m-md-3 { margin: var(--spacing-md) !important; }
  .m-md-4 { margin: var(--spacing-lg) !important; }
  .m-md-5 { margin: var(--spacing-xl) !important; }
  
  .p-md-0 { padding: 0 !important; }
  .p-md-1 { padding: var(--spacing-xs) !important; }
  .p-md-2 { padding: var(--spacing-sm) !important; }
  .p-md-3 { padding: var(--spacing-md) !important; }
  .p-md-4 { padding: var(--spacing-lg) !important; }
  .p-md-5 { padding: var(--spacing-xl) !important; }
}

@media (min-width: 992px) {
  .m-lg-0 { margin: 0 !important; }
  .m-lg-1 { margin: var(--spacing-xs) !important; }
  .m-lg-2 { margin: var(--spacing-sm) !important; }
  .m-lg-3 { margin: var(--spacing-md) !important; }
  .m-lg-4 { margin: var(--spacing-lg) !important; }
  .m-lg-5 { margin: var(--spacing-xl) !important; }
  
  .p-lg-0 { padding: 0 !important; }
  .p-lg-1 { padding: var(--spacing-xs) !important; }
  .p-lg-2 { padding: var(--spacing-sm) !important; }
  .p-lg-3 { padding: var(--spacing-md) !important; }
  .p-lg-4 { padding: var(--spacing-lg) !important; }
  .p-lg-5 { padding: var(--spacing-xl) !important; }
}

@media (min-width: 1200px) {
  .m-xl-0 { margin: 0 !important; }
  .m-xl-1 { margin: var(--spacing-xs) !important; }
  .m-xl-2 { margin: var(--spacing-sm) !important; }
  .m-xl-3 { margin: var(--spacing-md) !important; }
  .m-xl-4 { margin: var(--spacing-lg) !important; }
  .m-xl-5 { margin: var(--spacing-xl) !important; }
  
  .p-xl-0 { padding: 0 !important; }
  .p-xl-1 { padding: var(--spacing-xs) !important; }
  .p-xl-2 { padding: var(--spacing-sm) !important; }
  .p-xl-3 { padding: var(--spacing-md) !important; }
  .p-xl-4 { padding: var(--spacing-lg) !important; }
  .p-xl-5 { padding: var(--spacing-xl) !important; }
}

/* ===== 特殊斷點處理 ===== */

/* 極小設備 (手機豎屏, <576px) */
@media (max-width: 575.98px) {
  .container {
    padding-right: var(--spacing-sm);
    padding-left: var(--spacing-sm);
  }
  
  .navbar {
    padding: var(--spacing-sm);
  }
  
  .modal-dialog {
    margin: var(--spacing-sm);
  }
  
  .btn {
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
  }
  
  .card {
    margin-bottom: var(--spacing-md);
  }
  
  .card-body {
    padding: var(--spacing-md);
  }
}

/* 小設備 (手機橫屏, ≥576px and <768px) */
@media (min-width: 576px) and (max-width: 767.98px) {
  .navbar-expand-sm .navbar-nav {
    flex-direction: row;
  }
  
  .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: var(--spacing-sm);
    padding-left: var(--spacing-sm);
  }
}

/* 中等設備 (平板, ≥768px and <992px) */
@media (min-width: 768px) and (max-width: 991.98px) {
  .navbar-expand-md .navbar-nav {
    flex-direction: row;
  }
  
  .navbar-expand-md .navbar-nav .nav-link {
    padding-right: var(--spacing-md);
    padding-left: var(--spacing-md);
  }
}

/* 大設備 (桌面, ≥992px and <1200px) */
@media (min-width: 992px) and (max-width: 1199.98px) {
  .navbar-expand-lg .navbar-nav {
    flex-direction: row;
  }
  
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: var(--spacing-lg);
    padding-left: var(--spacing-lg);
  }
}

/* 超大設備 (大桌面, ≥1200px) */
@media (min-width: 1200px) {
  .navbar-expand-xl .navbar-nav {
    flex-direction: row;
  }
  
  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: var(--spacing-lg);
    padding-left: var(--spacing-lg);
  }
}

/* ===== 高度相關響應式 ===== */
.vh-100 { height: 100vh !important; }
.min-vh-100 { min-height: 100vh !important; }

/* 矮設備優化 */
@media (max-height: 600px) {
  .modal-dialog {
    margin: var(--spacing-sm) auto;
  }
  
  .modal-body {
    max-height: 60vh;
    overflow-y: auto;
  }
  
  .sidebar {
    overflow-y: auto;
  }
}

/* ===== 觸控設備優化 ===== */
@media (pointer: coarse) {
  .btn,
  .nav-link,
  .dropdown-item,
  .page-link {
    min-height: 44px;
    min-width: 44px;
  }
  
  .form-control {
    min-height: 44px;
  }
  
  .nav-toggle {
    min-height: 44px;
    min-width: 44px;
  }
}

/* ===== 懸停設備優化 ===== */
@media (hover: hover) {
  .card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
  }
  
  .btn:hover {
    transform: translateY(-1px);
  }
}

/* ===== 定向響應式 ===== */
@media (orientation: landscape) and (max-height: 500px) {
  .navbar {
    padding: var(--spacing-xs) var(--spacing-md);
  }
  
  .main-content {
    margin-top: 60px;
  }
  
  .content-container {
    height: calc(100vh - 60px);
  }
}

/* ===== 深色模式響應式 ===== */
@media (prefers-color-scheme: dark) {
  .card {
    background-color: var(--gray-800);
    border-color: var(--gray-700);
  }
  
  .modal-content {
    background-color: var(--gray-800);
    border-color: var(--gray-700);
  }
  
  .dropdown-menu {
    background-color: var(--gray-800);
    border-color: var(--gray-700);
  }
}