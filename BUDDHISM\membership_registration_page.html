<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加入會員 - 佛教身心療癒網站</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2C3E50;
            --secondary-color: #F39C12;
            --accent-color: #E8F4F8;
            --text-color: #2C3E50;
            --light-bg: #F8F9FA;
            --success-color: #27AE60;
            --warning-color: #E67E22;
            --info-color: #3498DB;
            --danger-color: #E74C3C;
        }

        body {
            font-family: 'Noto Sans CJK TC', 'Microsoft JhengHei', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
            background: linear-gradient(135deg, var(--light-bg) 0%, #e8f5ff 100%);
        }

        .navbar {
            background-color: var(--primary-color) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand, .nav-link {
            color: white !important;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--secondary-color) !important;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            padding: 80px 0 60px;
            margin-bottom: 0;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50 10 C70 30, 70 70, 50 90 C30 70, 30 30, 50 10 Z" fill="rgba(255,255,255,0.1)"/></svg>') center/80px repeat;
            opacity: 0.3;
        }

        .breadcrumb {
            background: rgba(255,255,255,0.1);
            border-radius: 25px;
            padding: 10px 20px;
            margin-bottom: 30px;
        }

        .breadcrumb-item a {
            color: var(--secondary-color);
            text-decoration: none;
        }

        .breadcrumb-item.active {
            color: white;
        }

        .membership-container {
            max-width: 1200px;
            margin: -50px auto 0;
            position: relative;
            z-index: 2;
        }

        .membership-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .membership-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
        }

        .plan-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border: 3px solid transparent;
        }

        .plan-card.recommended {
            border-color: var(--secondary-color);
            transform: scale(1.05);
        }

        .plan-card.recommended::before {
            content: '推薦';
            position: absolute;
            top: 20px;
            right: -30px;
            background: var(--secondary-color);
            color: white;
            padding: 5px 40px;
            font-size: 0.8rem;
            font-weight: 600;
            transform: rotate(45deg);
        }

        .plan-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .plan-name {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .plan-price {
            font-size: 3rem;
            font-weight: 700;
            color: var(--secondary-color);
            margin-bottom: 5px;
        }

        .plan-price .currency {
            font-size: 1.5rem;
            vertical-align: top;
        }

        .plan-period {
            color: #666;
            font-size: 1rem;
        }

        .plan-features {
            list-style: none;
            padding: 0;
            margin-bottom: 30px;
        }

        .plan-features li {
            padding: 12px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }

        .plan-features li:last-child {
            border-bottom: none;
        }

        .feature-icon {
            color: var(--success-color);
            margin-right: 15px;
            font-size: 1.1rem;
        }

        .feature-icon.not-included {
            color: #ccc;
        }

        .btn-select-plan {
            width: 100%;
            padding: 15px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-basic {
            background: linear-gradient(135deg, #95A5A6 0%, #7F8C8D 100%);
            color: white;
        }

        .btn-premium {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #E67E22 100%);
            color: white;
        }

        .btn-vip {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
        }

        .btn-select-plan:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .registration-form {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            margin-top: 30px;
        }

        .form-section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--accent-color);
        }

        .form-floating > .form-control {
            border-radius: 15px;
            border: 2px solid var(--accent-color);
            transition: border-color 0.3s ease;
        }

        .form-floating > .form-control:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(243, 156, 18, 0.25);
        }

        .form-floating > label {
            color: #666;
        }

        .benefits-section {
            background: linear-gradient(135deg, var(--accent-color) 0%, #f0f8ff 100%);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .benefit-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .benefit-icon {
            width: 50px;
            height: 50px;
            background: var(--secondary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            margin-right: 20px;
        }

        .payment-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .payment-method {
            background: white;
            border: 2px solid var(--accent-color);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .payment-method:hover,
        .payment-method.selected {
            border-color: var(--secondary-color);
            background: var(--accent-color);
        }

        .payment-icon {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .terms-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid var(--info-color);
        }

        .btn-submit {
            background: linear-gradient(135deg, var(--success-color) 0%, #229954 100%);
            border: none;
            color: white;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 1.2rem;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-submit:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(39, 174, 96, 0.3);
            color: white;
        }

        .progress-steps {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
        }

        .step {
            display: flex;
            align-items: center;
            margin: 0 20px;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ddd;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 10px;
        }

        .step.active .step-number {
            background: var(--secondary-color);
        }

        .step.completed .step-number {
            background: var(--success-color);
        }

        .footer {
            background-color: var(--primary-color);
            color: white;
            padding: 40px 0;
            margin-top: 80px;
        }

        .testimonial-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            border-left: 5px solid var(--secondary-color);
        }

        .testimonial-text {
            font-style: italic;
            margin-bottom: 15px;
            color: #555;
        }

        .testimonial-author {
            font-weight: 600;
            color: var(--primary-color);
        }

        .security-badge {
            display: flex;
            align-items: center;
            background: var(--success-color);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            margin: 20px 0;
        }

        .security-badge i {
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html">
                <i class="fas fa-lotus me-2"></i>佛教身心療癒
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">主頁</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about_page.html">關於我們</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">課程</a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses_goals_page.html">課程目標</a></li>
                            <li><a class="dropdown-item" href="courses_weekly_page.html">每週主題</a></li>
                            <li><a class="dropdown-item" href="courses_methods_page.html">進行方式</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">經文選讀</a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="scriptures_search_page.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="scriptures_methods_page.html">療癒方法</a></li>
                            <li><a class="dropdown-item" href="scriptures_theory_page.html">療癒理論</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">身心療癒研究</a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="research_search_page.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="research-theory.html">療癒理論</a></li>
                            <li><a class="dropdown-item" href="research-methods.html">療癒方法</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">藏傳佛教專題</a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="tibetan_theory_page.html">藏醫理論</a></li>
                            <li><a class="dropdown-item" href="tibetan_practice_page.html">藏醫實習</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">海外實習體驗</a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="overseas_members_page.html">實習成員</a></li>
                            <li><a class="dropdown-item" href="overseas_research_page.html">研究成果</a></li>
                            <li><a class="dropdown-item" href="overseas_future_plan_page.html">未來計畫</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="workshop_healing_page.html">工作坊</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="membership_registration_page.html">加入會員</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 頁面標題 -->
    <div class="page-header">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.html">主頁</a></li>
                    <li class="breadcrumb-item active">加入會員</li>
                </ol>
            </nav>
            <div class="text-center">
                <h1 class="display-4 fw-bold mb-3">
                    <i class="fas fa-users me-3"></i>加入會員
                </h1>
                <p class="lead mb-0">開始您的身心療癒學習之旅，享受專屬會員權益</p>
            </div>
        </div>
    </div>

    <!-- 主要內容 -->
    <div class="container">
        <div class="membership-container">
            
            <!-- 進度指示 -->
            <div class="progress-steps">
                <div class="step active">
                    <div class="step-number">1</div>
                    <span>選擇方案</span>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <span>填寫資料</span>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <span>完成付款</span>
                </div>
            </div>

            <!-- 會員權益介紹 -->
            <div class="benefits-section">
                <h2 class="text-center fw-bold mb-4">
                    <i class="fas fa-gem me-2"></i>會員專屬權益
                </h2>
                <div class="row">
                    <div class="col-md-6">
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fas fa-book-open"></i>
                            </div>
                            <div>
                                <h6 class="fw-bold mb-1">專屬課程內容</h6>
                                <p class="mb-0 text-muted">深度學習資料與進階療癒技法</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div>
                                <h6 class="fw-bold mb-1">社群交流</h6>
                                <p class="mb-0 text-muted">與志同道合的夥伴互動學習</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div>
                                <h6 class="fw-bold mb-1">工作坊優先</h6>
                                <p class="mb-0 text-muted">優先報名與會員專屬價格</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fas fa-headset"></i>
                            </div>
                            <div>
                                <h6 class="fw-bold mb-1">專業諮詢</h6>
                                <p class="mb-0 text-muted">一對一學習指導與建議</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 會員方案 -->
            <div class="row mb-5">
                <div class="col-lg-4 mb-4">
                    <div class="plan-card">
                        <div class="plan-header">
                            <h3 class="plan-name">基礎會員</h3>
                            <div class="plan-price">
                                <span class="currency">NT$</span>299
                            </div>
                            <div class="plan-period">每月</div>
                        </div>
                        <ul class="plan-features">
                            <li><i class="fas fa-check feature-icon"></i>基礎課程內容</li>
                            <li><i class="fas fa-check feature-icon"></i>經文資料庫</li>
                            <li><i class="fas fa-check feature-icon"></i>社群討論</li>
                            <li><i class="fas fa-check feature-icon"></i>月度通訊</li>
                            <li><i class="fas fa-times feature-icon not-included"></i>進階課程</li>
                            <li><i class="fas fa-times feature-icon not-included"></i>一對一諮詢</li>
                            <li><i class="fas fa-times feature-icon not-included"></i>海外實習</li>
                        </ul>
                        <button class="btn btn-select-plan btn-basic" data-plan="basic">選擇基礎方案</button>
                    </div>
                </div>

                <div class="col-lg-4 mb-4">
                    <div class="plan-card recommended">
                        <div class="plan-header">
                            <h3 class="plan-name">進階會員</h3>
                            <div class="plan-price">
                                <span class="currency">NT$</span>599
                            </div>
                            <div class="plan-period">每月</div>
                        </div>
                        <ul class="plan-features">
                            <li><i class="fas fa-check feature-icon"></i>所有基礎功能</li>
                            <li><i class="fas fa-check feature-icon"></i>進階課程內容</li>
                            <li><i class="fas fa-check feature-icon"></i>工作坊優惠</li>
                            <li><i class="fas fa-check feature-icon"></i>學習進度追蹤</li>
                            <li><i class="fas fa-check feature-icon"></i>線上諮詢</li>
                            <li><i class="fas fa-times feature-icon not-included"></i>一對一指導</li>
                            <li><i class="fas fa-times feature-icon not-included"></i>海外實習</li>
                        </ul>
                        <button class="btn btn-select-plan btn-premium" data-plan="premium">選擇進階方案</button>
                    </div>
                </div>

                <div class="col-lg-4 mb-4">
                    <div class="plan-card">
                        <div class="plan-header">
                            <h3 class="plan-name">VIP會員</h3>
                            <div class="plan-price">
                                <span class="currency">NT$</span>999
                            </div>
                            <div class="plan-period">每月</div>
                        </div>
                        <ul class="plan-features">
                            <li><i class="fas fa-check feature-icon"></i>所有進階功能</li>
                            <li><i class="fas fa-check feature-icon"></i>VIP專屬課程</li>
                            <li><i class="fas fa-check feature-icon"></i>一對一指導</li>
                            <li><i class="fas fa-check feature-icon"></i>海外實習優先</li>
                            <li><i class="fas fa-check feature-icon"></i>專屬客服</li>
                            <li><i class="fas fa-check feature-icon"></i>證書認證</li>
                            <li><i class="fas fa-check feature-icon"></i>終身學習</li>
                        </ul>
                        <button class="btn btn-select-plan btn-vip" data-plan="vip">選擇VIP方案</button>
                    </div>
                </div>
            </div>

            <!-- 註冊表單 -->
            <div class="registration-form" id="registrationForm" style="display: none;">
                <h3 class="text-center fw-bold mb-4">
                    <i class="fas fa-user-plus me-2"></i>建立您的帳戶
                </h3>

                <form id="membershipForm">
                    <!-- 個人資料 -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-user me-2"></i>個人資料
                        </h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="lastName" required>
                                    <label for="lastName">姓氏</label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="firstName" required>
                                    <label for="firstName">名字</label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    <input type="email" class="form-control" id="email" required>
                                    <label for="email">電子郵件</label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    <input type="tel" class="form-control" id="phone" required>
                                    <label for="phone">手機號碼</label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    <input type="date" class="form-control" id="birthDate">
                                    <label for="birthDate">生日</label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    <select class="form-select" id="gender">
                                        <option value="">請選擇</option>
                                        <option value="male">男性</option>
                                        <option value="female">女性</option>
                                        <option value="other">其他</option>
                                        <option value="prefer_not_to_say">不願透露</option>
                                    </select>
                                    <label for="gender">性別</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 帳戶設定 -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-lock me-2"></i>帳戶設定
                        </h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    <input type="password" class="form-control" id="password" required>
                                    <label for="password">密碼</label>
                                </div>
                                <small class="text-muted">密碼至少8個字符，包含字母和數字</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    <input type="password" class="form-control" id="confirmPassword" required>
                                    <label for="confirmPassword">確認密碼</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 興趣偏好 -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-heart me-2"></i>學習興趣
                        </h4>
                        <div class="row">
                            <div class="col-md-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="interest1">
                                    <label class="form-check-label" for="interest1">正念冥想</label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="interest2">
                                    <label class="form-check-label" for="interest2">藏醫學</label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="interest3">
                                    <label class="form-check-label" for="interest3">經典研讀</label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="interest4">
                                    <label class="form-check-label" for="interest4">聲音療癒</label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="interest5">
                                    <label class="form-check-label" for="interest5">身心療癒</label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="interest6">
                                    <label class="form-check-label" for="interest6">海外實習</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 付款方式 -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-credit-card me-2"></i>付款方式
                        </h4>
                        <div class="payment-methods">
                            <div class="payment-method" data-method="credit">
                                <div class="payment-icon">
                                    <i class="fas fa-credit-card"></i>
                                </div>
                                <div>信用卡</div>
                            </div>
                            <div class="payment-method" data-method="atm">
                                <div class="payment-icon">
                                    <i class="fas fa-university"></i>
                                </div>
                                <div>ATM轉帳</div>
                            </div>
                            <div class="payment-method" data-method="linepay">
                                <div class="payment-icon">
                                    <i class="fab fa-line"></i>
                                </div>
                                <div>LINE Pay</div>
                            </div>
                            <div class="payment-method" data-method="applepay">
                                <div class="payment-icon">
                                    <i class="fab fa-apple"></i>
                                </div>
                                <div>Apple Pay</div>
                            </div>
                        </div>
                    </div>

                    <!-- 安全保證 -->
                    <div class="security-badge">
                        <i class="fas fa-shield-alt"></i>
                        SSL加密保護，您的個人資料絕對安全
                    </div>

                    <!-- 條款同意 -->
                    <div class="terms-section">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                            <label class="form-check-label" for="agreeTerms">
                                我已閱讀並同意 <a href="#" class="text-decoration-none">服務條款</a> 和 <a href="#" class="text-decoration-none">隱私政策</a>
                            </label>
                        </div>
                        <div class="form-check mt-2">
                            <input class="form-check-input" type="checkbox" id="agreeNewsletter">
                            <label class="form-check-label" for="agreeNewsletter">
                                我同意接收學習資訊和活動通知
                            </label>
                        </div>
                    </div>

                    <!-- 提交按鈕 -->
                    <div class="text-center">
                        <button type="submit" class="btn btn-submit">
                            <i class="fas fa-check me-2"></i>完成註冊並付款
                        </button>
                    </div>
                </form>
            </div>

            <!-- 會員推薦 -->
            <div class="row mt-5">
                <div class="col-12">
                    <h3 class="text-center fw-bold mb-4">
                        <i class="fas fa-quote-left me-2"></i>會員推薦
                    </h3>
                </div>
                <div class="col-md-4">
                    <div class="testimonial-card">
                        <div class="testimonial-text">
                            "加入會員後，我的冥想練習有了質的飛躍，專業的指導讓我受益良多。"
                        </div>
                        <div class="testimonial-author">- 王小明，進階會員</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="testimonial-card">
                        <div class="testimonial-text">
                            "VIP會員的一對一指導非常值得，老師的建議對我的學習幫助很大。"
                        </div>
                        <div class="testimonial-author">- 李美華，VIP會員</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="testimonial-card">
                        <div class="testimonial-text">
                            "社群中的學習氛圍很好，大家互相支持，一起成長。"
                        </div>
                        <div class="testimonial-author">- 張志強，基礎會員</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 頁尾 -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <h5><i class="fas fa-lotus me-2"></i>佛教身心療癒網站</h5>
                    <p class="mb-0">致力於推廣佛教身心療癒智慧，提供專業的學習與實踐平台。</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <p class="mb-0">&copy; 2024 佛教身心療癒網站. 版權所有.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedPlan = null;

        // 方案選擇
        document.querySelectorAll('.btn-select-plan').forEach(btn => {
            btn.addEventListener('click', function() {
                selectedPlan = this.getAttribute('data-plan');
                
                // 顯示註冊表單
                document.getElementById('registrationForm').style.display = 'block';
                
                // 更新進度步驟
                const steps = document.querySelectorAll('.step');
                steps[0].classList.add('completed');
                steps[1].classList.add('active');
                
                // 滾動到表單
                document.getElementById('registrationForm').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // 更新表單標題顯示選中的方案
                const planNames = {
                    'basic': '基礎會員',
                    'premium': '進階會員',
                    'vip': 'VIP會員'
                };
                
                const formTitle = document.querySelector('#registrationForm h3');
                formTitle.innerHTML = `<i class="fas fa-user-plus me-2"></i>註冊 ${planNames[selectedPlan]}`;
            });
        });

        // 付款方式選擇
        document.querySelectorAll('.payment-method').forEach(method => {
            method.addEventListener('click', function() {
                // 移除所有選中狀態
                document.querySelectorAll('.payment-method').forEach(m => {
                    m.classList.remove('selected');
                });
                
                // 添加選中狀態
                this.classList.add('selected');
            });
        });

        // 密碼確認驗證
        document.getElementById('confirmPassword').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('密碼不匹配');
            } else {
                this.setCustomValidity('');
            }
        });

        // 表單提交處理
        document.getElementById('membershipForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 檢查是否選擇了付款方式
            const selectedPayment = document.querySelector('.payment-method.selected');
            if (!selectedPayment) {
                alert('請選擇付款方式');
                return;
            }
            
            // 檢查條款同意
            const agreeTerms = document.getElementById('agreeTerms').checked;
            if (!agreeTerms) {
                alert('請同意服務條款和隱私政策');
                return;
            }
            
            // 更新進度步驟
            const steps = document.querySelectorAll('.step');
            steps[1].classList.add('completed');
            steps[2].classList.add('active');
            
            // 模擬付款處理
            alert(`正在處理您的${selectedPlan === 'basic' ? '基礎' : selectedPlan === 'premium' ? '進階' : 'VIP'}會員註冊...`);
            
            // 這裡可以添加實際的表單提交邏輯
            setTimeout(() => {
                alert('恭喜！您已成功加入會員。歡迎來到佛教身心療癒社群！');
                // 可以重定向到會員歡迎頁面
                // window.location.href = 'welcome.html';
            }, 2000);
        });

        // 表單驗證增強
        document.querySelectorAll('.form-control, .form-select').forEach(input => {
            input.addEventListener('blur', function() {
                if (this.checkValidity()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            });
        });

        // 平滑滾動增強
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>