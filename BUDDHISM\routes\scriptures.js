const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const Scripture = require('../models/Scripture');
const User = require('../models/User');
const authenticate = require('../middleware/auth');

// 驗證錯誤處理中間件
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// 搜尋經文
router.get('/search', async (req, res) => {
  try {
    const {
      q: query,
      category,
      subcategory,
      difficulty,
      language = 'zh-TW',
      author,
      period,
      healing,
      page = 1,
      limit = 20,
      sort = 'relevance'
    } = req.query;

    const skip = (page - 1) * limit;
    const searchQuery = {};

    // 文本搜尋
    if (query) {
      searchQuery.$or = [
        { title: { $regex: query, $options: 'i' } },
        { originalTitle: { $regex: query, $options: 'i' } },
        { 'content.text': { $regex: query, $options: 'i' } },
        { 'content.translation': { $regex: query, $options: 'i' } },
        { 'content.commentary': { $regex: query, $options: 'i' } },
        { tags: { $in: [new RegExp(query, 'i')] } },
        { searchKeywords: { $in: [new RegExp(query, 'i')] } },
        { 'healing.applications': { $in: [new RegExp(query, 'i')] } },
        { 'healing.techniques': { $in: [new RegExp(query, 'i')] } }
      ];
    }

    // 分類篩選
    if (category) {
      searchQuery.category = { $regex: category, $options: 'i' };
    }
    if (subcategory) {
      searchQuery.subcategory = { $regex: subcategory, $options: 'i' };
    }
    if (difficulty) {
      searchQuery.difficulty = difficulty;
    }
    if (language) {
      searchQuery['metadata.language'] = language;
    }
    if (author) {
      searchQuery['metadata.author'] = { $regex: author, $options: 'i' };
    }
    if (period) {
      searchQuery['metadata.period'] = { $regex: period, $options: 'i' };
    }

    // 療癒應用篩選
    if (healing) {
      const healingArray = healing.split(',');
      searchQuery['healing.applications'] = { $in: healingArray.map(h => new RegExp(h, 'i')) };
    }

    // 排序邏輯
    const sortOptions = {
      'relevance': query ? { score: { $meta: 'textScore' } } : { popularity: -1, created: -1 },
      'newest': { created: -1 },
      'oldest': { created: 1 },
      'popular': { popularity: -1 },
      'title': { title: 1 },
      'reading_time': { readingTime: 1 },
      'difficulty': { difficulty: 1 }
    };

    // 決定返回的字段（基於查詢類型）
    let selectFields = 'title originalTitle category subcategory difficulty readingTime popularity tags metadata.author metadata.period healing.applications created';
    
    // 如果是詳細查詢，返回更多信息
    if (limit <= 10) {
      selectFields += ' content.translation content.commentary';
    }

    const scriptures = await Scripture.find(searchQuery)
      .sort(sortOptions[sort] || sortOptions.relevance)
      .skip(skip)
      .limit(parseInt(limit))
      .select(selectFields);

    const total = await Scripture.countDocuments(searchQuery);

    // 獲取搜尋統計
    const stats = await Scripture.aggregate([
      { $match: searchQuery },
      {
        $group: {
          _id: null,
          categories: { $addToSet: '$category' },
          difficulties: { $addToSet: '$difficulty' },
          authors: { $addToSet: '$metadata.author' },
          periods: { $addToSet: '$metadata.period' },
          healingApplications: { $addToSet: '$healing.applications' },
          avgReadingTime: { $avg: '$readingTime' },
          totalPopularity: { $sum: '$popularity' }
        }
      }
    ]);

    res.json({
      message: 'Scripture search completed successfully',
      query: {
        text: query,
        filters: { category, subcategory, difficulty, language, author, period, healing }
      },
      scriptures,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      },
      statistics: stats[0] || {},
      suggestions: query ? await generateSearchSuggestions(query) : []
    });
  } catch (error) {
    console.error('Scripture search error:', error);
    res.status(500).json({ error: 'Failed to search scriptures' });
  }
});

// 生成搜尋建議的輔助函數
async function generateSearchSuggestions(query) {
  try {
    const suggestions = await Scripture.aggregate([
      {
        $match: {
          $or: [
            { tags: { $regex: query, $options: 'i' } },
            { searchKeywords: { $regex: query, $options: 'i' } }
          ]
        }
      },
      { $unwind: '$tags' },
      { $match: { tags: { $regex: query, $options: 'i' } } },
      { $group: { _id: '$tags', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 5 }
    ]);

    return suggestions.map(s => s._id);
  } catch (error) {
    return [];
  }
}

// 獲取特定經文詳情
router.get('/:id', async (req, res) => {
  try {
    const scriptureId = req.params.id;
    
    const scripture = await Scripture.findById(scriptureId);
    
    if (!scripture) {
      return res.status(404).json({ error: 'Scripture not found' });
    }

    // 更新瀏覽次數
    await Scripture.findByIdAndUpdate(scriptureId, {
      $inc: { popularity: 1 }
    });

    // 獲取相關經文推薦
    const relatedScriptures = await Scripture.find({
      _id: { $ne: scriptureId },
      $or: [
        { category: scripture.category },
        { subcategory: scripture.subcategory },
        { tags: { $in: scripture.tags } },
        { 'healing.applications': { $in: scripture.healing.applications } }
      ]
    })
      .limit(6)
      .select('title category difficulty readingTime popularity')
      .sort({ popularity: -1 });

    // 檢查用戶收藏狀態（如果已認證）
    let isBookmarked = false;
    if (req.headers.authorization) {
      try {
        const token = req.headers.authorization.replace('Bearer ', '');
        const jwt = require('jsonwebtoken');
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
        const user = await User.findById(decoded.sub);
        
        if (user && user.bookmarks && user.bookmarks.scriptures) {
          isBookmarked = user.bookmarks.scriptures.includes(scriptureId);
        }
      } catch (err) {
        // 忽略token錯誤
      }
    }

    res.json({
      message: 'Scripture details retrieved successfully',
      scripture,
      relatedScriptures,
      userInteraction: {
        isBookmarked
      }
    });
  } catch (error) {
    console.error('Get scripture details error:', error);
    res.status(500).json({ error: 'Failed to fetch scripture details' });
  }
});

// 收藏經文
router.post('/:id/bookmark', authenticate, async (req, res) => {
  try {
    const scriptureId = req.params.id;
    const userId = req.user._id;

    // 檢查經文是否存在
    const scripture = await Scripture.findById(scriptureId);
    if (!scripture) {
      return res.status(404).json({ error: 'Scripture not found' });
    }

    // 檢查是否已收藏
    const user = await User.findById(userId);
    const bookmarks = user.bookmarks || { scriptures: [] };
    
    if (bookmarks.scriptures && bookmarks.scriptures.includes(scriptureId)) {
      return res.status(400).json({ error: 'Scripture already bookmarked' });
    }

    // 添加收藏
    await User.findByIdAndUpdate(userId, {
      $addToSet: { 'bookmarks.scriptures': scriptureId },
      $set: { updated: new Date() }
    });

    res.json({
      message: 'Scripture bookmarked successfully',
      scriptureId: scriptureId,
      title: scripture.title,
      bookmarkedAt: new Date()
    });
  } catch (error) {
    console.error('Bookmark scripture error:', error);
    res.status(500).json({ error: 'Failed to bookmark scripture' });
  }
});

// 取消收藏經文
router.delete('/:id/bookmark', authenticate, async (req, res) => {
  try {
    const scriptureId = req.params.id;
    const userId = req.user._id;

    // 移除收藏
    await User.findByIdAndUpdate(userId, {
      $pull: { 'bookmarks.scriptures': scriptureId },
      $set: { updated: new Date() }
    });

    res.json({
      message: 'Scripture bookmark removed successfully',
      scriptureId: scriptureId
    });
  } catch (error) {
    console.error('Remove bookmark error:', error);
    res.status(500).json({ error: 'Failed to remove bookmark' });
  }
});

// 獲取用戶收藏的經文
router.get('/my/bookmarks', authenticate, async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const skip = (page - 1) * limit;
    const userId = req.user._id;

    const user = await User.findById(userId)
      .populate({
        path: 'bookmarks.scriptures',
        select: 'title category difficulty readingTime popularity created',
        options: {
          skip: skip,
          limit: parseInt(limit),
          sort: { created: -1 }
        }
      });

    const bookmarkedScriptures = user.bookmarks?.scriptures || [];

    res.json({
      message: 'Bookmarked scriptures retrieved successfully',
      scriptures: bookmarkedScriptures,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: bookmarkedScriptures.length
      }
    });
  } catch (error) {
    console.error('Get bookmarks error:', error);
    res.status(500).json({ error: 'Failed to fetch bookmarked scriptures' });
  }
});

// 獲取經文分類
router.get('/categories/list', async (req, res) => {
  try {
    const categories = await Scripture.aggregate([
      {
        $group: {
          _id: {
            category: '$category',
            subcategory: '$subcategory'
          },
          count: { $sum: 1 },
          avgDifficulty: { $avg: { $cond: [
            { $eq: ['$difficulty', 'beginner'] }, 1,
            { $cond: [{ $eq: ['$difficulty', 'intermediate'] }, 2, 3] }
          ]}},
          avgReadingTime: { $avg: '$readingTime' }
        }
      },
      {
        $group: {
          _id: '$_id.category',
          subcategories: {
            $push: {
              name: '$_id.subcategory',
              count: '$count',
              avgDifficulty: '$avgDifficulty',
              avgReadingTime: '$avgReadingTime'
            }
          },
          totalCount: { $sum: '$count' }
        }
      },
      { $sort: { totalCount: -1 } }
    ]);

    // 獲取熱門標籤
    const popularTags = await Scripture.aggregate([
      { $unwind: '$tags' },
      { $group: { _id: '$tags', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 20 }
    ]);

    // 獲取療癒應用分類
    const healingApplications = await Scripture.aggregate([
      { $unwind: '$healing.applications' },
      { $group: { _id: '$healing.applications', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 15 }
    ]);

    res.json({
      message: 'Scripture categories retrieved successfully',
      categories: categories,
      popularTags: popularTags.map(tag => tag._id),
      healingApplications: healingApplications.map(app => app._id),
      metadata: {
        totalCategories: categories.length,
        totalTags: popularTags.length,
        totalHealingApps: healingApplications.length
      }
    });
  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({ error: 'Failed to fetch categories' });
  }
});

// 高級搜尋功能
router.post('/search/advanced', [
  body('keywords').optional().isArray().withMessage('Keywords must be an array'),
  body('categories').optional().isArray().withMessage('Categories must be an array'),
  body('difficulties').optional().isArray().withMessage('Difficulties must be an array'),
  body('healingTypes').optional().isArray().withMessage('Healing types must be an array'),
  body('readingTimeRange').optional().isObject().withMessage('Reading time range must be an object'),
  body('dateRange').optional().isObject().withMessage('Date range must be an object')
], handleValidationErrors, async (req, res) => {
  try {
    const {
      keywords = [],
      categories = [],
      difficulties = [],
      healingTypes = [],
      readingTimeRange = {},
      dateRange = {},
      page = 1,
      limit = 20,
      sort = 'relevance'
    } = req.body;

    const skip = (page - 1) * limit;
    const query = {};

    // 關鍵詞搜尋
    if (keywords.length > 0) {
      query.$and = keywords.map(keyword => ({
        $or: [
          { title: { $regex: keyword, $options: 'i' } },
          { 'content.text': { $regex: keyword, $options: 'i' } },
          { 'content.translation': { $regex: keyword, $options: 'i' } },
          { tags: { $in: [new RegExp(keyword, 'i')] } }
        ]
      }));
    }

    // 分類篩選
    if (categories.length > 0) {
      query.category = { $in: categories };
    }

    // 難度篩選
    if (difficulties.length > 0) {
      query.difficulty = { $in: difficulties };
    }

    // 療癒類型篩選
    if (healingTypes.length > 0) {
      query['healing.applications'] = { $in: healingTypes };
    }

    // 閱讀時間範圍
    if (readingTimeRange.min !== undefined || readingTimeRange.max !== undefined) {
      query.readingTime = {};
      if (readingTimeRange.min !== undefined) query.readingTime.$gte = readingTimeRange.min;
      if (readingTimeRange.max !== undefined) query.readingTime.$lte = readingTimeRange.max;
    }

    // 日期範圍
    if (dateRange.start || dateRange.end) {
      query.created = {};
      if (dateRange.start) query.created.$gte = new Date(dateRange.start);
      if (dateRange.end) query.created.$lte = new Date(dateRange.end);
    }

    // 排序選項
    const sortOptions = {
      'relevance': { popularity: -1, created: -1 },
      'newest': { created: -1 },
      'oldest': { created: 1 },
      'popular': { popularity: -1 },
      'reading_time_asc': { readingTime: 1 },
      'reading_time_desc': { readingTime: -1 },
      'title': { title: 1 }
    };

    const scriptures = await Scripture.find(query)
      .sort(sortOptions[sort] || sortOptions.relevance)
      .skip(skip)
      .limit(parseInt(limit))
      .select('title category subcategory difficulty readingTime popularity tags healing.applications metadata created');

    const total = await Scripture.countDocuments(query);

    res.json({
      message: 'Advanced search completed successfully',
      searchCriteria: {
        keywords,
        categories,
        difficulties,
        healingTypes,
        readingTimeRange,
        dateRange
      },
      scriptures,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Advanced search error:', error);
    res.status(500).json({ error: 'Failed to perform advanced search' });
  }
});

// 獲取經文學習統計
router.get('/stats/learning', authenticate, async (req, res) => {
  try {
    const userId = req.user._id;
    const user = await User.findById(userId);

    // 獲取用戶收藏的經文統計
    const bookmarkedScriptures = await Scripture.find({
      _id: { $in: user.bookmarks?.scriptures || [] }
    });

    // 分析統計
    const stats = {
      totalBookmarked: bookmarkedScriptures.length,
      categoryDistribution: {},
      difficultyDistribution: {},
      healingApplications: {},
      averageReadingTime: 0,
      readingProgress: {
        completed: 0, // 需要實際的閱讀記錄
        inProgress: bookmarkedScriptures.length,
        totalReadingTime: 0 // 需要實際的閱讀時間記錄
      }
    };

    // 計算分布統計
    bookmarkedScriptures.forEach(scripture => {
      // 分類分布
      stats.categoryDistribution[scripture.category] = 
        (stats.categoryDistribution[scripture.category] || 0) + 1;
      
      // 難度分布
      stats.difficultyDistribution[scripture.difficulty] = 
        (stats.difficultyDistribution[scripture.difficulty] || 0) + 1;
      
      // 療癒應用統計
      scripture.healing.applications.forEach(app => {
        stats.healingApplications[app] = (stats.healingApplications[app] || 0) + 1;
      });
    });

    // 計算平均閱讀時間
    if (bookmarkedScriptures.length > 0) {
      stats.averageReadingTime = Math.round(
        bookmarkedScriptures.reduce((sum, s) => sum + s.readingTime, 0) / bookmarkedScriptures.length
      );
    }

    res.json({
      message: 'Learning statistics retrieved successfully',
      statistics: stats,
      recommendations: generateLearningRecommendations(stats)
    });
  } catch (error) {
    console.error('Get learning stats error:', error);
    res.status(500).json({ error: 'Failed to fetch learning statistics' });
  }
});

// 生成學習建議
function generateLearningRecommendations(stats) {
  const recommendations = [];

  // 基於收藏數量的建議
  if (stats.totalBookmarked < 5) {
    recommendations.push('建議多收藏一些經文來豐富您的學習內容');
  }

  // 基於難度分布的建議
  const difficulties = Object.keys(stats.difficultyDistribution);
  if (difficulties.length === 1) {
    if (difficulties[0] === 'beginner') {
      recommendations.push('可以嘗試一些中級或高級難度的經文');
    } else if (difficulties[0] === 'advanced') {
      recommendations.push('建議也學習一些基礎經文來鞏固基礎');
    }
  }

  // 基於分類分布的建議
  const categories = Object.keys(stats.categoryDistribution);
  if (categories.length < 3) {
    recommendations.push('建議擴展學習更多不同類別的經文');
  }

  return recommendations;
}

module.exports = router;