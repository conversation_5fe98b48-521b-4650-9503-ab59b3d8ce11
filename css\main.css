/* 澄源閱讀 - 主樣式文件 */

/* ===== CSS 變量定義 ===== */
:root {
  /* 主要顏色 */
  --primary-color: #667eea;
  --primary-light: #8fa4f3;
  --primary-dark: #4c63d2;
  --secondary-color: #764ba2;
  --secondary-light: #9575cd;
  --secondary-dark: #5e3370;
  
  /* 中性顏色 */
  --white: #ffffff;
  --black: #000000;
  --gray-50: #f8f9fc;
  --gray-100: #f1f3f4;
  --gray-200: #e1e5e9;
  --gray-300: #c4cdd5;
  --gray-400: #919eab;
  --gray-500: #637381;
  --gray-600: #454f5b;
  --gray-700: #212b36;
  --gray-800: #161c24;
  --gray-900: #0c1017;
  
  /* 狀態顏色 */
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --info-color: #2196f3;
  
  /* 漸變 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  --gradient-light: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary-light) 100%);
  --gradient-dark: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-dark) 100%);
  
  /* 陰影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.1);
  
  /* 邊框圓角 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 50%;
  
  /* 間距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  --spacing-3xl: 64px;
  
  /* 字體 */
  --font-family: 'Noto Sans TC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  
  /* 字重 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  
  /* 過渡 */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* ===== 深色模式變量 ===== */
[data-theme="dark"] {
  --white: #1a1a1a;
  --black: #ffffff;
  --gray-50: #161c24;
  --gray-100: #212b36;
  --gray-200: #454f5b;
  --gray-300: #637381;
  --gray-400: #919eab;
  --gray-500: #c4cdd5;
  --gray-600: #e1e5e9;
  --gray-700: #f1f3f4;
  --gray-800: #f8f9fc;
  --gray-900: #ffffff;
}

/* ===== 基礎重置 ===== */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--gray-700);
  background-color: var(--gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* ===== 布局組件 ===== */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
  background: var(--white);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  text-decoration: none;
}

.nav-brand i {
  font-size: var(--font-size-2xl);
}

.nav-menu {
  display: flex;
  align-items: center;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: var(--spacing-lg);
}

.nav-item {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--gray-600);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
  font-weight: var(--font-weight-medium);
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
  background: rgba(102, 126, 234, 0.1);
}

.nav-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  background: var(--gray-100);
  color: var(--gray-600);
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.theme-toggle:hover {
  background: var(--gray-200);
  color: var(--primary-color);
}

.nav-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-sm);
}

.nav-toggle span {
  width: 24px;
  height: 2px;
  background: var(--gray-600);
  border-radius: 1px;
  transition: all var(--transition-normal);
}

/* ===== 側邊欄 ===== */
.sidebar {
  position: fixed;
  top: 0;
  left: -300px;
  width: 300px;
  height: 100vh;
  background: var(--white);
  box-shadow: var(--shadow-xl);
  z-index: var(--z-modal);
  transition: left var(--transition-normal);
  overflow-y: auto;
}

.sidebar.active {
  left: 0;
}

.sidebar-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-full);
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: var(--font-size-xl);
}

.user-details h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-700);
  margin-bottom: var(--spacing-xs);
}

.user-details p {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
}

.sidebar-nav {
  padding: var(--spacing-lg) 0;
}

.sidebar-menu {
  list-style: none;
}

.menu-item {
  margin-bottom: var(--spacing-xs);
}

.menu-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--gray-600);
  text-decoration: none;
  transition: all var(--transition-normal);
}

.menu-link:hover,
.menu-link.active {
  color: var(--primary-color);
  background: rgba(102, 126, 234, 0.1);
}

.menu-link i {
  font-size: var(--font-size-lg);
  width: 20px;
  text-align: center;
}

.sidebar-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  width: 100%;
  padding: var(--spacing-md);
  background: none;
  border: 1px solid var(--gray-200);
  color: var(--gray-600);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.logout-btn:hover {
  background: var(--error-color);
  color: var(--white);
  border-color: var(--error-color);
}

/* ===== 主要內容區域 ===== */
.main-content {
  margin-top: 80px;
  min-height: calc(100vh - 80px);
  position: relative;
}

.content-container {
  width: 100%;
  height: calc(100vh - 80px);
  position: relative;
}

.content-frame {
  width: 100%;
  height: 100%;
  border: none;
  background: var(--white);
}

/* ===== 返回頂部按鈕 ===== */
.back-to-top {
  position: fixed;
  bottom: var(--spacing-xl);
  right: var(--spacing-xl);
  width: 50px;
  height: 50px;
  background: var(--gradient-primary);
  color: var(--white);
  border: none;
  border-radius: var(--radius-full);
  cursor: pointer;
  z-index: var(--z-fixed);
  transition: all var(--transition-normal);
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
}

.back-to-top.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.back-to-top:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

/* ===== 遮罩層 ===== */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal-backdrop);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.overlay.active {
  opacity: 1;
  visibility: visible;
}

/* ===== 通知系統 ===== */
.notification-container {
  position: fixed;
  top: 100px;
  right: var(--spacing-lg);
  z-index: var(--z-tooltip);
  max-width: 400px;
}

.notification {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border-left: 4px solid var(--info-color);
  animation: slideInRight 0.3s ease-out, fadeOut 0.3s ease-out 2.7s;
}

.notification.success {
  border-left-color: var(--success-color);
}

.notification.warning {
  border-left-color: var(--warning-color);
}

.notification.error {
  border-left-color: var(--error-color);
}

.notification i {
  color: var(--info-color);
  font-size: var(--font-size-lg);
}

.notification.success i {
  color: var(--success-color);
}

.notification.warning i {
  color: var(--warning-color);
}

.notification.error i {
  color: var(--error-color);
}

.notification span {
  flex: 1;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

.notification-close {
  background: none;
  border: none;
  color: var(--gray-400);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: color var(--transition-fast);
}

.notification-close:hover {
  color: var(--gray-600);
}

/* ===== 載入動畫 ===== */
.loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--white);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.lotus-loader {
  position: relative;
  width: 80px;
  height: 80px;
  margin-bottom: var(--spacing-lg);
}

.lotus-petal {
  position: absolute;
  width: 20px;
  height: 20px;
  background: var(--gradient-primary);
  border-radius: 50% 0;
  transform-origin: 10px 10px;
  animation: lotus-spin 2s linear infinite;
}

.lotus-petal:nth-child(1) { transform: rotate(0deg) translate(30px) rotate(-0deg); }
.lotus-petal:nth-child(2) { transform: rotate(60deg) translate(30px) rotate(-60deg); animation-delay: 0.1s; }
.lotus-petal:nth-child(3) { transform: rotate(120deg) translate(30px) rotate(-120deg); animation-delay: 0.2s; }
.lotus-petal:nth-child(4) { transform: rotate(180deg) translate(30px) rotate(-180deg); animation-delay: 0.3s; }
.lotus-petal:nth-child(5) { transform: rotate(240deg) translate(30px) rotate(-240deg); animation-delay: 0.4s; }
.lotus-petal:nth-child(6) { transform: rotate(300deg) translate(30px) rotate(-300deg); animation-delay: 0.5s; }

.loader p {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
  animation: fade-in-out 2s ease-in-out infinite;
}

/* ===== 動畫定義 ===== */
@keyframes lotus-spin {
  0% { opacity: 1; transform: rotate(0deg) translate(30px) rotate(0deg) scale(1); }
  50% { opacity: 0.5; transform: rotate(180deg) translate(30px) rotate(-180deg) scale(0.8); }
  100% { opacity: 1; transform: rotate(360deg) translate(30px) rotate(-360deg) scale(1); }
}

@keyframes fade-in-out {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* ===== 響應式設計 ===== */
@media (max-width: 768px) {
  .navbar {
    padding: var(--spacing-md);
  }
  
  .nav-menu {
    display: none;
  }
  
  .nav-toggle {
    display: flex;
  }
  
  .main-content {
    margin-top: 70px;
  }
  
  .content-container {
    height: calc(100vh - 70px);
  }
  
  .notification-container {
    right: var(--spacing-md);
    left: var(--spacing-md);
    max-width: none;
  }
  
  .back-to-top {
    bottom: var(--spacing-lg);
    right: var(--spacing-lg);
    width: 45px;
    height: 45px;
  }
}

@media (max-width: 480px) {
  .navbar {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .nav-brand {
    font-size: var(--font-size-lg);
  }
  
  .nav-brand i {
    font-size: var(--font-size-xl);
  }
  
  .theme-toggle {
    width: 35px;
    height: 35px;
  }
  
  .sidebar {
    width: 280px;
    left: -280px;
  }
  
  .notification {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .notification span {
    font-size: var(--font-size-xs);
  }
}

/* ===== 無障礙支持 ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  html {
    scroll-behavior: auto;
  }
}

/* ===== 高對比度模式 ===== */
@media (prefers-contrast: high) {
  :root {
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.3);
  }
  
  .nav-link:hover,
  .nav-link.active,
  .menu-link:hover,
  .menu-link.active {
    outline: 2px solid var(--primary-color);
  }
}

/* ===== 打印樣式 ===== */
@media print {
  .header,
  .sidebar,
  .nav-toggle,
  .theme-toggle,
  .back-to-top,
  .overlay,
  .notification-container,
  .loader {
    display: none !important;
  }
  
  .main-content {
    margin-top: 0;
  }
  
  .content-container {
    height: auto;
  }
  
  body {
    background: white;
    color: black;
  }
}