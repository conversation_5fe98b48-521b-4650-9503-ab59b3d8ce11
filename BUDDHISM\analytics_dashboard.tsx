import React, { useState, useEffect } from 'react';
import { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { Calendar, Users, BookOpen, Search, TrendingUp, Activity, Award, Eye } from 'lucide-react';

// 模擬 API 調用
const useAnalyticsData = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 模擬 API 延遲
    setTimeout(() => {
      setData({
        overview: {
          totalUsers: 15420,
          activeUsers: 3240,
          totalCourses: 156,
          totalScriptures: 892,
          totalViews: 45670,
          avgSessionTime: 24.5
        },
        userGrowth: [
          { date: '2024-01', users: 1200, newUsers: 120 },
          { date: '2024-02', users: 1450, newUsers: 250 },
          { date: '2024-03', users: 1780, newUsers: 330 },
          { date: '2024-04', users: 2100, newUsers: 320 },
          { date: '2024-05', users: 2560, newUsers: 460 },
          { date: '2024-06', users: 3240, newUsers: 680 }
        ],
        courseStats: [
          { category: '冥想', enrollments: 2340, completions: 1980 },
          { category: '經文研讀', enrollments: 1890, completions: 1520 },
          { category: '藏醫學', enrollments: 1560, completions: 1200 },
          { category: '正念療癒', enrollments: 2100, completions: 1750 },
          { category: '哲學思辨', enrollments: 980, completions: 720 }
        ],
        membershipDistribution: [
          { name: '基礎會員', value: 8500, color: '#8884d8' },
          { name: '進階會員', value: 4200, color: '#82ca9d' },
          { name: 'VIP會員', value: 2720, color: '#ffc658' }
        ],
        dailyActivity: [
          { hour: '00', users: 120 },
          { hour: '03', users: 80 },
          { hour: '06', users: 340 },
          { hour: '09', users: 860 },
          { hour: '12', users: 1240 },
          { hour: '15', users: 980 },
          { hour: '18', users: 1560 },
          { hour: '21', users: 1890 },
          { hour: '24', users: 450 }
        ],
        topContent: [
          { title: '心經導讀', type: '經文', views: 5420, rating: 4.8 },
          { title: '基礎冥想入門', type: '課程', views: 4890, rating: 4.9 },
          { title: '藏醫三根理論', type: '課程', views: 3670, rating: 4.7 },
          { title: '藥師經', type: '經文', views: 3240, rating: 4.6 },
          { title: '正念減壓法', type: '課程', views: 2980, rating: 4.8 }
        