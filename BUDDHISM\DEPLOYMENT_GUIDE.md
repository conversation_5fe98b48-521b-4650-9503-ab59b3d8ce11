# 佛教身心療癒網站 - 測試與部署指南

## 📋 環境準備

### 1. 系統需求
- **Node.js**: 16.0.0 或更高版本
- **MongoDB**: 5.0 或更高版本
- **Redis**: 6.0 或更高版本 (可選，用於緩存)
- **Git**: 最新版本
- **PM2**: 全局安裝 (生產環境)

### 2. 安裝步驟

```bash
# 1. 檢查 Node.js 版本
node --version
npm --version

# 2. 安裝 MongoDB (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install -y mongodb-org

# 3. 安裝 Redis (可選)
sudo apt-get install redis-server

# 4. 全局安裝 PM2
npm install -g pm2

# 5. 複製項目
git clone <your-repository-url>
cd buddhist-healing-website

# 6. 安裝依賴
npm install
```

---

## 🧪 本地測試

### 1. 環境配置

創建 `.env` 文件：

```bash
# 複製環境變數範本
cp .env.example .env
```

`.env` 文件內容：
```env
# 服務器配置
NODE_ENV=development
PORT=3000

# 資料庫配置
MONGODB_URI=mongodb://localhost:27017/buddhist_healing
REDIS_URL=redis://localhost:6379

# JWT 配置
JWT_SECRET=your_super_secret_jwt_key_change_in_production
JWT_REFRESH_SECRET=your_refresh_secret_key
JWT_EXPIRES_IN=7d

# 郵件配置 (開發環境使用 Ethereal)
SMTP_HOST=smtp.ethereal.email
SMTP_PORT=587
SMTP_USER=your_ethereal_user
SMTP_PASS=your_ethereal_pass
SMTP_FROM=<EMAIL>

# API 金鑰
API_KEY=your_internal_api_key
```

### 2. 啟動服務

```bash
# 啟動 MongoDB
sudo systemctl start mongod

# 啟動 Redis (如果安裝)
sudo systemctl start redis

# 啟動開發服務器
npm run dev
```

### 3. 驗證服務

```bash
# 檢查服務器狀態
curl http://localhost:3000/health

# 檢查 API 文檔
curl http://localhost:3000/api/docs

# 檢查前端頁面
open http://localhost:3000
```

---

## ✅ 功能測試

### 1. 手動測試清單

#### 前端頁面測試
```bash
# 主要頁面檢查清單
□ 主頁 (index.html) - 導航、輪播、功能區塊
□ 關於我們 (about_page.html) - 團隊介紹、使命
□ 課程頁面 - 目標、週主題、方法
□ 經文選讀 - 搜尋、方法、理論
□ 身心療癒研究 - 搜尋、理論、方法
□ 藏傳佛教 - 理論、實習
□ 海外實習 - 成員管理、研究成果、未來計畫
□ 工作坊 (workshop_healing_page.html) - 列表、篩選、報名
□ 會員註冊 (membership_registration_page.html) - 方案選擇、表單

# 響應式測試
□ 桌面端 (1920x1080)
□ 平板端 (768x1024)
□ 手機端 (375x667)
```

#### 後端 API 測試

創建測試腳本：

```bash
# 創建測試目錄
mkdir tests
cd tests
```

**API 測試腳本** (`test_api.sh`):
```bash
#!/bin/bash

BASE_URL="http://localhost:3000/api"
echo "🧪 開始 API 測試..."

# 1. 健康檢查
echo "1. 測試健康檢查..."
curl -s "$BASE_URL/../health" | jq .

# 2. 用戶註冊
echo "2. 測試用戶註冊..."
REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPass123!",
    "firstName": "測試",
    "lastName": "用戶",
    "membershipPlan": "premium",
    "agreeToTerms": true
  }')

echo $REGISTER_RESPONSE | jq .

# 提取 token
TOKEN=$(echo $REGISTER_RESPONSE | jq -r '.tokens.access')

# 3. 用戶登入
echo "3. 測試用戶登入..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPass123!"
  }')

echo $LOGIN_RESPONSE | jq .

# 4. 獲取用戶資料
echo "4. 測試獲取用戶資料..."
curl -s -X GET "$BASE_URL/auth/me" \
  -H "Authorization: Bearer $TOKEN" | jq .

# 5. 測試無效 token
echo "5. 測試無效 token..."
curl -s -X GET "$BASE_URL/auth/me" \
  -H "Authorization: Bearer invalid_token" | jq .

echo "✅ API 測試完成"
```

```bash
# 執行測試
chmod +x test_api.sh
./test_api.sh
```

### 2. 自動化測試

創建 Jest 測試套件：

```bash
# 安裝測試依賴
npm install --save-dev jest supertest mongodb-memory-server
```

**用戶認證測試** (`tests/auth.test.js`):
```javascript
const request = require('supertest');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const app = require('../app');
const User = require('../models/User');

let mongoServer;

beforeAll(async () => {
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();
  await mongoose.connect(mongoUri);
});

afterAll(async () => {
  await mongoose.disconnect();
  await mongoServer.stop();
});

afterEach(async () => {
  await User.deleteMany({});
});

describe('Auth Endpoints', () => {
  const validUser = {
    email: '<EMAIL>',
    password: 'TestPass123!',
    firstName: '測試',
    lastName: '用戶',
    agreeToTerms: true
  };

  describe('POST /api/auth/register', () => {
    test('應該成功註冊新用戶', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send(validUser)
        .expect(201);

      expect(response.body.message).toContain('註冊成功');
      expect(response.body.user.email).toBe(validUser.email);
      expect(response.body.tokens).toHaveProperty('access');
    });

    test('應該拒絕重複的郵件地址', async () => {
      await request(app)
        .post('/api/auth/register')
        .send(validUser);

      await request(app)
        .post('/api/auth/register')
        .send(validUser)
        .expect(409);
    });

    test('應該驗證密碼強度', async () => {
      const weakPassword = { ...validUser, password: '123' };
      
      await request(app)
        .post('/api/auth/register')
        .send(weakPassword)
        .expect(400);
    });
  });

  describe('POST /api/auth/login', () => {
    beforeEach(async () => {
      await request(app)
        .post('/api/auth/register')
        .send(validUser);
    });

    test('應該成功登入有效用戶', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: validUser.email,
          password: validUser.password
        })
        .expect(200);

      expect(response.body.message).toContain('登入成功');
      expect(response.body.tokens).toHaveProperty('access');
    });

    test('應該拒絕錯誤的密碼', async () => {
      await request(app)
        .post('/api/auth/login')
        .send({
          email: validUser.email,
          password: 'wrongpassword'
        })
        .expect(401);
    });
  });
});
```

```bash
# 執行測試
npm test

# 生成覆蓋率報告
npm run test:coverage
```

---

## ⚡ 性能測試

### 1. 壓力測試

安裝 Apache Bench：
```bash
# Ubuntu/Debian
sudo apt-get install apache2-utils

# macOS
brew install httpie
```

執行壓力測試：
```bash
# 測試首頁
ab -n 1000 -c 10 http://localhost:3000/

# 測試 API 端點
ab -n 500 -c 5 http://localhost:3000/api/docs

# 測試健康檢查
ab -n 2000 -c 20 http://localhost:3000/health
```

### 2. 負載測試

使用 Artillery.io：
```bash
# 安裝 Artillery
npm install -g artillery

# 創建負載測試配置
```

**負載測試配置** (`load-test.yml`):
```yaml
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
      name: "Warm up"
    - duration: 120
      arrivalRate: 20
      name: "Load test"
    - duration: 60
      arrivalRate: 50
      name: "Spike test"

scenarios:
  - name: "Website browsing"
    weight: 70
    flow:
      - get:
          url: "/"
      - think: 3
      - get:
          url: "/workshop_healing_page.html"
      - think: 5
      - get:
          url: "/membership_registration_page.html"
  
  - name: "API usage"
    weight: 30
    flow:
      - get:
          url: "/health"
      - get:
          url: "/api/docs"
      - post:
          url: "/api/auth/register"
          json:
            email: "load-test-{{ $randomNumber() }}@example.com"
            password: "TestPass123!"
            firstName: "Load"
            lastName: "Test"
            agreeToTerms: true
```

```bash
# 執行負載測試
artillery run load-test.yml
```

---

## 🚀 部署準備

### 1. 生產環境配置

**生產環境 `.env`**:
```env
NODE_ENV=production
PORT=3000

# 生產資料庫
MONGODB_URI=mongodb://your-db-server:27017/buddhist_healing_prod
REDIS_URL=redis://your-redis-server:6379

# 強密碼
JWT_SECRET=your_super_long_random_jwt_secret_for_production_minimum_32_chars
JWT_REFRESH_SECRET=another_super_long_random_secret_for_refresh_tokens
JWT_EXPIRES_IN=24h

# 生產郵件服務 (例如 SendGrid)
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASS=your_sendgrid_api_key
SMTP_FROM=<EMAIL>

# SSL 證書路徑 (如果使用 HTTPS)
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/private-key.pem

# 監控
SENTRY_DSN=your_sentry_dsn_for_error_tracking
```

### 2. 安全檢查

```bash
# 檢查依賴漏洞
npm audit

# 修復可修復的漏洞
npm audit fix

# 強制修復
npm audit fix --force

# 安全掃描
npx snyk test
```

### 3. 建構優化

**生產建構腳本** (`scripts/build.sh`):
```bash
#!/bin/bash

echo "🏗️ 開始生產建構..."

# 1. 清理
rm -rf dist/
mkdir -p dist/

# 2. 複製必要文件
cp -r *.html dist/
cp -r models/ dist/
cp -r routes/ dist/
cp -r middleware/ dist/
cp -r utils/ dist/
cp app.js dist/
cp package.json dist/

# 3. 安裝生產依賴
cd dist/
npm ci --only=production

# 4. 壓縮靜態資源
find . -name "*.js" -exec gzip -k {} \;
find . -name "*.css" -exec gzip -k {} \;
find . -name "*.html" -exec gzip -k {} \;

echo "✅ 建構完成"
```

---

## 🌐 生產環境部署

### 1. Docker 部署

**Dockerfile**:
```dockerfile
FROM node:16-alpine

# 設置工作目錄
WORKDIR /app

# 複製 package files
COPY package*.json ./

# 安裝依賴
RUN npm ci --only=production && npm cache clean --force

# 複製應用代碼
COPY . .

# 創建非 root 用戶
RUN addgroup -g 1001 -S nodejs
RUN adduser -S buddhist-healing -u 1001

# 設置權限
USER buddhist-healing

# 暴露端口
EXPOSE 3000

# 健康檢查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# 啟動命令
CMD ["npm", "start"]
```

**docker-compose.yml**:
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/buddhist_healing
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mongo
      - redis
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs

  mongo:
    image: mongo:5.0
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
    volumes:
      - mongo_data:/data/db
    ports:
      - "27017:27017"

  redis:
    image: redis:6.2-alpine
    restart: unless-stopped
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  mongo_data:
  redis_data:
```

### 2. PM2 部署

**PM2 配置** (`ecosystem.config.js`):
```javascript
module.exports = {
  apps: [{
    name: 'buddhist-healing-api',
    script: 'app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    log_file: './logs/combined.log',
    out_file: './logs/out.log',
    error_file: './logs/error.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }],

  deploy: {
    production: {
      user: 'deploy',
      host: 'your-server.com',
      ref: 'origin/main',
      repo: '**************:your-repo/buddhist-healing.git',
      path: '/var/www/buddhist-healing',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
```

### 3. Nginx 配置

**nginx.conf**:
```nginx
events {
    worker_connections 1024;
}

http {
    upstream app {
        server app:3000;
    }

    # 限制請求大小
    client_max_body_size 10M;

    # Gzip 壓縮
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # SSL 配置
    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/private-key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
        ssl_prefer_server_ciphers off;

        # 安全標頭
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

        # API 代理
        location /api/ {
            proxy_pass http://app;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        # 靜態文件
        location / {
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 健康檢查
        location /health {
            proxy_pass http://app/health;
            access_log off;
        }
    }

    # HTTP 重定向到 HTTPS
    server {
        listen 80;
        server_name your-domain.com;
        return 301 https://$server_name$request_uri;
    }
}
```

### 4. 部署腳本

**部署腳本** (`deploy.sh`):
```bash
#!/bin/bash

set -e

SERVER="your-server.com"
USER="deploy"
APP_DIR="/var/www/buddhist-healing"

echo "🚀 開始部署到生產環境..."

# 1. 備份當前版本
echo "📦 備份當前版本..."
ssh $USER@$SERVER "cd $APP_DIR && tar -czf backup-$(date +%Y%m%d-%H%M%S).tar.gz current/"

# 2. 拉取最新代碼
echo "📥 拉取最新代碼..."
ssh $USER@$SERVER "cd $APP_DIR && git pull origin main"

# 3. 安裝依賴
echo "📦 安裝依賴..."
ssh $USER@$SERVER "cd $APP_DIR && npm ci --only=production"

# 4. 運行測試
echo "🧪 運行測試..."
ssh $USER@$SERVER "cd $APP_DIR && npm test"

# 5. 重啟服務
echo "🔄 重啟服務..."
ssh $USER@$SERVER "cd $APP_DIR && pm2 reload ecosystem.config.js --env production"

# 6. 健康檢查
echo "🏥 健康檢查..."
sleep 10
HEALTH_CHECK=$(curl -s -o /dev/null -w "%{http_code}" https://your-domain.com/health)

if [ $HEALTH_CHECK -eq 200 ]; then
    echo "✅ 部署成功！"
else
    echo "❌ 健康檢查失敗，回滾..."
    ssh $USER@$SERVER "cd $APP_DIR && pm2 reload ecosystem.config.js --env production"
    exit 1
fi

echo "🎉 部署完成！"
```

---

## 📊 監控與維護

### 1. 日誌管理

**日誌配置** (`config/logger.js`):
```javascript
const winston = require('winston');
const path = require('path');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'buddhist-healing-api' },
  transports: [
    new winston.transports.File({ 
      filename: path.join('logs', 'error.log'), 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: path.join('logs', 'combined.log') 
    })
  ]
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}

module.exports = logger;
```

### 2. 監控腳本

**系統監控** (`scripts/monitor.sh`):
```bash
#!/bin/bash

# 監控腳本
check_service() {
    SERVICE_NAME=$1
    URL=$2
    
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" $URL)
    
    if [ $HTTP_CODE -eq 200 ]; then
        echo "✅ $SERVICE_NAME: 正常"
    else
        echo "❌ $SERVICE_NAME: 異常 (HTTP $HTTP_CODE)"
        # 發送警報
        curl -X POST "https://hooks.slack.com/your-webhook" \
          -H 'Content-type: application/json' \
          --data '{"text":"🚨 '$SERVICE_NAME' 服務異常"}'
    fi
}

# 檢查服務
check_service "主應用" "https://your-domain.com/health"
check_service "API文檔" "https://your-domain.com/api/docs"

# 檢查資料庫連接
MONGO_STATUS=$(mongosh --eval "db.adminCommand('ping')" --quiet)
if [[ $MONGO_STATUS == *"ok"* ]]; then
    echo "✅ MongoDB: 正常"
else
    echo "❌ MongoDB: 異常"
fi

# 檢查磁碟空間
DISK_USAGE=$(df -h / | awk 'NR==2{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "⚠️ 磁碟使用率過高: ${DISK_USAGE}%"
fi

# 檢查記憶體使用
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.1f"), $3/$2 * 100.0}')
echo "📊 記憶體使用率: ${MEMORY_USAGE}%"
```

### 3. 備份腳本

**資料備份** (`scripts/backup.sh`):
```bash
#!/bin/bash

BACKUP_DIR="/var/backups/buddhist-healing"
DATE=$(date +%Y%m%d_%H%M%S)

echo "💾 開始備份..."

# 創建備份目錄
mkdir -p $BACKUP_DIR

# 備份 MongoDB
mongodump --host localhost:27017 --db buddhist_healing --out $BACKUP_DIR/mongo_$DATE

# 備份應用文件
tar -czf $BACKUP_DIR/app_$DATE.tar.gz /var/www/buddhist-healing

# 備份日誌
tar -czf $BACKUP_DIR/logs_$DATE.tar.gz /var/www/buddhist-healing/logs

# 清理舊備份 (保留7天)
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "mongo_*" -mtime +7 -exec rm -rf {} \;

echo "✅ 備份完成"
```

---

## 🔄 CI/CD 流程

**GitHub Actions** (`.github/workflows/deploy.yml`):
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:5.0
        ports:
          - 27017:27017
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '16'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
      env:
        MONGODB_URI: mongodb://localhost:27017/test
        JWT_SECRET: test_secret
    
    - name: Run security audit
      run: npm audit --audit-level=high

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /var/www/buddhist-healing
          git pull origin main
          npm ci --only=production
          pm2 reload ecosystem.config.js --env production
          
    - name: Health check
      run: |
        sleep 30
        curl -f https://your-domain.com/health || exit 1
```

---

## 📱 快速部署檢查清單

### 部署前檢查
- [ ] 所有測試通過
- [ ] 安全審計無高風險漏洞  
- [ ] 環境變數配置正確
- [ ] SSL 證書已安裝
- [ ] 資料庫備份完成
- [ ] 監控系統就緒

### 部署步驟
- [ ] 上傳代碼到服務器
- [ ] 安裝生產依賴
- [ ] 配置環境變數
- [ ] 啟動服務 (PM2/Docker)
- [ ] 配置 Nginx 反向代理
- [ ] 執行健康檢查
- [ ] 設置監控和備份

### 部署後驗證
- [ ] 所有頁面正常載入
- [ ] API 端點回應正常
- [ ] 用戶註冊/登入功能正常
- [ ] 郵件發送功能正常
- [ ] 資料庫連接正常
- [ ] 日誌記錄正常
- [ ] 監控警報設置正確

---

## 🆘 故障排除

### 常見問題

1. **服務無法啟動**
   ```bash
   # 檢查日誌
   pm2 logs buddhist-healing-api
   
   # 檢查環境變數
   printenv | grep NODE_ENV
   ```

2. **資料庫連接失敗**
   ```bash
   # 檢查 MongoDB 狀態
   sudo systemctl status mongod
   
   # 檢查連接
   mongosh --host localhost:27017
   ```

3. **郵件發送失敗**
   ```bash
   # 檢查 SMTP 配置
   echo $SMTP_HOST
   
   # 測試連接
   telnet $SMTP_HOST $SMTP_PORT
   ```

4. **性能問題**
   ```bash
   # 檢查系統資源
   htop
   
   # 檢查 Node.js 進程
   pm2 monit
   ```

---

這份完整的測試與部署指南將幫助您安全、可靠地將佛教身心療癒網站部署到生產環境。記住要定期備份、監控系統狀態，並保持安全更新！ 🚀