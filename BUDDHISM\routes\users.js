const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const authenticate = require('../middleware/auth');

// 驗證錯誤處理中間件
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// 獲取用戶資料
router.get('/profile', authenticate, async (req, res) => {
  try {
    const user = await User.findById(req.user._id)
      .select('-password')
      .populate('progress.coursesCompleted', 'title difficulty category')
      .populate('progress.currentCourses', 'title difficulty category progress')
      .populate('progress.certificates');

    res.json({
      message: 'Profile retrieved successfully',
      user: {
        id: user._id,
        email: user.email,
        profile: user.profile,
        membership: user.membership,
        preferences: user.preferences,
        progress: user.progress,
        created: user.created,
        updated: user.updated
      }
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ error: 'Failed to fetch user profile' });
  }
});

// 更新用戶資料
router.put('/profile', authenticate, [
  body('firstName').optional().isLength({ min: 2, max: 50 }).trim()
    .withMessage('First name must be 2-50 characters'),
  body('lastName').optional().isLength({ min: 2, max: 50 }).trim()
    .withMessage('Last name must be 2-50 characters'),
  body('phone').optional().isMobilePhone()
    .withMessage('Invalid phone number format'),
  body('location').optional().isLength({ max: 100 }).trim()
    .withMessage('Location must be less than 100 characters'),
  body('timezone').optional().isIn([
    'Asia/Taipei', 'Asia/Hong_Kong', 'Asia/Shanghai', 'UTC'
  ]).withMessage('Invalid timezone')
], handleValidationErrors, async (req, res) => {
  try {
    const { firstName, lastName, phone, location, timezone, avatar } = req.body;

    const updateData = {};
    if (firstName !== undefined) updateData['profile.firstName'] = firstName;
    if (lastName !== undefined) updateData['profile.lastName'] = lastName;
    if (phone !== undefined) updateData['profile.phone'] = phone;
    if (location !== undefined) updateData['profile.location'] = location;
    if (timezone !== undefined) updateData['profile.timezone'] = timezone;
    if (avatar !== undefined) updateData['profile.avatar'] = avatar;
    
    updateData.updated = new Date();

    const user = await User.findByIdAndUpdate(
      req.user._id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      message: 'Profile updated successfully',
      user: {
        id: user._id,
        email: user.email,
        profile: user.profile,
        membership: user.membership,
        updated: user.updated
      }
    });
  } catch (error) {
    console.error('Update profile error:', error);
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(e => e.message);
      return res.status(400).json({
        error: 'Validation Error',
        details: errors
      });
    }
    res.status(500).json({ error: 'Failed to update profile' });
  }
});

// 更新用戶偏好設定
router.put('/preferences', authenticate, [
  body('language').optional().isIn(['zh-TW', 'zh-CN', 'en', 'ja'])
    .withMessage('Invalid language option'),
  body('notifications.email').optional().isBoolean()
    .withMessage('Email notification must be boolean'),
  body('notifications.push').optional().isBoolean()
    .withMessage('Push notification must be boolean'),
  body('notifications.studyReminders').optional().isBoolean()
    .withMessage('Study reminders must be boolean'),
  body('privacy.profilePublic').optional().isBoolean()
    .withMessage('Profile public setting must be boolean'),
  body('privacy.progressPublic').optional().isBoolean()
    .withMessage('Progress public setting must be boolean')
], handleValidationErrors, async (req, res) => {
  try {
    const { language, notifications, privacy } = req.body;

    const updateData = {};
    if (language !== undefined) updateData['preferences.language'] = language;
    if (notifications) {
      if (notifications.email !== undefined) updateData['preferences.notifications.email'] = notifications.email;
      if (notifications.push !== undefined) updateData['preferences.notifications.push'] = notifications.push;
      if (notifications.studyReminders !== undefined) updateData['preferences.notifications.studyReminders'] = notifications.studyReminders;
    }
    if (privacy) {
      if (privacy.profilePublic !== undefined) updateData['preferences.privacy.profilePublic'] = privacy.profilePublic;
      if (privacy.progressPublic !== undefined) updateData['preferences.privacy.progressPublic'] = privacy.progressPublic;
    }
    
    updateData.updated = new Date();

    const user = await User.findByIdAndUpdate(
      req.user._id,
      { $set: updateData },
      { new: true }
    ).select('-password');

    res.json({
      message: 'Preferences updated successfully',
      preferences: user.preferences
    });
  } catch (error) {
    console.error('Update preferences error:', error);
    res.status(500).json({ error: 'Failed to update preferences' });
  }
});

// 獲取學習進度
router.get('/progress', authenticate, async (req, res) => {
  try {
    const user = await User.findById(req.user._id)
      .populate('progress.coursesCompleted', 'title difficulty category duration instructor')
      .populate('progress.currentCourses', 'title difficulty category duration instructor')
      .populate('progress.certificates')
      .select('progress');

    // 計算統計數據
    const stats = {
      totalCourses: user.progress.coursesCompleted.length + user.progress.currentCourses.length,
      completedCourses: user.progress.coursesCompleted.length,
      currentCourses: user.progress.currentCourses.length,
      certificates: user.progress.certificates.length,
      totalStudyTime: user.progress.totalStudyTime,
      completionRate: user.progress.coursesCompleted.length > 0 
        ? Math.round((user.progress.coursesCompleted.length / (user.progress.coursesCompleted.length + user.progress.currentCourses.length)) * 100)
        : 0
    };

    res.json({
      message: 'Progress retrieved successfully',
      progress: user.progress,
      statistics: stats
    });
  } catch (error) {
    console.error('Get progress error:', error);
    res.status(500).json({ error: 'Failed to fetch learning progress' });
  }
});

// 更新學習進度
router.put('/progress', authenticate, [
  body('courseId').isMongoId().withMessage('Invalid course ID'),
  body('studyTime').optional().isInt({ min: 0 }).withMessage('Study time must be positive integer'),
  body('completed').optional().isBoolean().withMessage('Completed must be boolean')
], handleValidationErrors, async (req, res) => {
  try {
    const { courseId, studyTime, completed } = req.body;
    const userId = req.user._id;

    const updateData = {};
    
    // 添加學習時間
    if (studyTime) {
      updateData.$inc = { 'progress.totalStudyTime': studyTime };
    }

    // 課程完成處理
    if (completed) {
      updateData.$pull = { 'progress.currentCourses': courseId };
      updateData.$addToSet = { 'progress.coursesCompleted': courseId };
    }

    // 更新最後活動時間
    updateData.$set = { 'progress.lastActiveDate': new Date() };

    const user = await User.findByIdAndUpdate(
      userId,
      updateData,
      { new: true }
    ).populate('progress.coursesCompleted', 'title')
      .populate('progress.currentCourses', 'title');

    res.json({
      message: 'Progress updated successfully',
      progress: user.progress
    });
  } catch (error) {
    console.error('Update progress error:', error);
    res.status(500).json({ error: 'Failed to update progress' });
  }
});

// 修改密碼
router.put('/password', authenticate, [
  body('currentPassword').notEmpty().withMessage('Current password is required'),
  body('newPassword').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('New password must be at least 8 characters with uppercase, lowercase and number'),
  body('confirmPassword').custom((value, { req }) => {
    if (value !== req.body.newPassword) {
      throw new Error('Password confirmation does not match');
    }
    return true;
  })
], handleValidationErrors, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user._id;

    // 獲取用戶當前密碼
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // 驗證當前密碼
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({ error: 'Current password is incorrect' });
    }

    // 加密新密碼
    const hashedNewPassword = await bcrypt.hash(newPassword, 12);

    // 更新密碼
    await User.findByIdAndUpdate(userId, {
      password: hashedNewPassword,
      updated: new Date()
    });

    res.json({ message: 'Password updated successfully' });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({ error: 'Failed to change password' });
  }
});

// 刪除帳戶
router.delete('/account', authenticate, [
  body('password').notEmpty().withMessage('Password is required for account deletion'),
  body('confirmDelete').equals('DELETE').withMessage('Must confirm deletion by typing DELETE')
], handleValidationErrors, async (req, res) => {
  try {
    const { password } = req.body;
    const userId = req.user._id;

    // 驗證密碼
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(400).json({ error: 'Password is incorrect' });
    }

    // 軟刪除（標記為已刪除而不是實際刪除）
    await User.findByIdAndUpdate(userId, {
      status: 'deleted',
      deleted: new Date(),
      email: `deleted_${Date.now()}_${user.email}` // 防止email衝突
    });

    res.json({ message: 'Account deleted successfully' });
  } catch (error) {
    console.error('Delete account error:', error);
    res.status(500).json({ error: 'Failed to delete account' });
  }
});

// 獲取用戶活動記錄
router.get('/activity', authenticate, async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const skip = (page - 1) * limit;

    // 這裡應該有一個活動記錄模型，暫時返回基本信息
    const user = await User.findById(req.user._id)
      .select('progress created updated')
      .populate('progress.coursesCompleted', 'title category')
      .populate('progress.currentCourses', 'title category');

    // 模擬活動記錄
    const activities = [
      {
        type: 'registration',
        description: '註冊帳戶',
        timestamp: user.created,
        details: {}
      },
      {
        type: 'profile_update',
        description: '更新個人資料',
        timestamp: user.updated,
        details: {}
      },
      ...user.progress.coursesCompleted.map(course => ({
        type: 'course_completed',
        description: `完成課程：${course.title}`,
        timestamp: new Date(), // 實際應該從課程進度記錄獲取
        details: { courseId: course._id, category: course.category }
      }))
    ];

    // 排序並分頁
    const sortedActivities = activities
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(skip, skip + parseInt(limit));

    res.json({
      message: 'Activity history retrieved successfully',
      activities: sortedActivities,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: activities.length,
        pages: Math.ceil(activities.length / limit)
      }
    });
  } catch (error) {
    console.error('Get activity error:', error);
    res.status(500).json({ error: 'Failed to fetch activity history' });
  }
});

module.exports = router;