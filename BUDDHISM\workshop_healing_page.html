<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>身心療癒工作坊 - 佛教身心療癒網站</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2C3E50;
            --secondary-color: #F39C12;
            --accent-color: #E8F4F8;
            --text-color: #2C3E50;
            --light-bg: #F8F9FA;
            --success-color: #27AE60;
            --warning-color: #E67E22;
            --info-color: #3498DB;
            --danger-color: #E74C3C;
        }

        body {
            font-family: 'Noto Sans CJK TC', 'Microsoft JhengHei', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
            background-color: var(--light-bg);
        }

        .navbar {
            background-color: var(--primary-color) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand, .nav-link {
            color: white !important;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--secondary-color) !important;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            padding: 80px 0 60px;
            margin-bottom: 0;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            opacity: 0.3;
        }

        .breadcrumb {
            background: rgba(255,255,255,0.1);
            border-radius: 25px;
            padding: 10px 20px;
            margin-bottom: 30px;
        }

        .breadcrumb-item a {
            color: var(--secondary-color);
            text-decoration: none;
        }

        .breadcrumb-item.active {
            color: white;
        }

        .workshop-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            margin-bottom: 30px;
            border: none;
        }

        .workshop-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .workshop-image {
            height: 200px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            position: relative;
            overflow: hidden;
        }

        .workshop-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50 10 C70 30, 70 70, 50 90 C30 70, 30 30, 50 10 Z" fill="rgba(255,255,255,0.1)"/></svg>') center/contain no-repeat;
            opacity: 0.6;
        }

        .workshop-type {
            position: absolute;
            top: 15px;
            left: 15px;
            background: rgba(255,255,255,0.9);
            color: var(--primary-color);
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .workshop-level {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .level-beginner {
            background: var(--success-color);
            color: white;
        }

        .level-intermediate {
            background: var(--warning-color);
            color: white;
        }

        .level-advanced {
            background: var(--danger-color);
            color: white;
        }

        .workshop-content {
            padding: 25px;
        }

        .workshop-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
            line-height: 1.3;
        }

        .workshop-instructor {
            color: var(--secondary-color);
            font-weight: 600;
            margin-bottom: 15px;
        }

        .workshop-details {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 20px;
        }

        .workshop-schedule {
            background: var(--accent-color);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .schedule-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .schedule-item:last-child {
            margin-bottom: 0;
        }

        .schedule-icon {
            width: 20px;
            color: var(--primary-color);
            margin-right: 10px;
        }

        .workshop-price {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .price-amount {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--secondary-color);
        }

        .price-original {
            text-decoration: line-through;
            color: #999;
            font-size: 1rem;
            margin-right: 10px;
        }

        .workshop-tags {
            margin-bottom: 20px;
        }

        .workshop-tag {
            display: inline-block;
            background: var(--accent-color);
            color: var(--primary-color);
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-right: 8px;
            margin-bottom: 5px;
        }

        .btn-register {
            width: 100%;
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            border: none;
            color: white;
            padding: 12px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(44, 62, 80, 0.4);
            color: white;
        }

        .btn-register.full {
            background: #95a5a6;
            cursor: not-allowed;
        }

        .btn-register.full:hover {
            transform: none;
            box-shadow: none;
        }

        .workshop-status {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            margin-bottom: 15px;
        }

        .status-available {
            background: var(--success-color);
            color: white;
        }

        .status-filling {
            background: var(--warning-color);
            color: white;
        }

        .status-full {
            background: var(--danger-color);
            color: white;
        }

        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        }

        .filter-btn {
            border: 2px solid var(--accent-color);
            background: white;
            color: var(--primary-color);
            border-radius: 25px;
            padding: 8px 20px;
            margin: 5px;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .filter-btn.active,
        .filter-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .search-box {
            border-radius: 25px;
            border: 2px solid var(--accent-color);
            padding: 12px 20px;
            transition: border-color 0.3s ease;
        }

        .search-box:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(243, 156, 18, 0.25);
        }

        .upcoming-section {
            background: linear-gradient(135deg, var(--info-color) 0%, #5DADE2 100%);
            color: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
        }

        .upcoming-section::before {
            content: '';
            position: absolute;
            top: -50px;
            right: -50px;
            width: 150px;
            height: 150px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
        }

        .instructor-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }

        .instructor-card:hover {
            transform: translateY(-5px);
        }

        .instructor-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid var(--accent-color);
            margin-bottom: 15px;
        }

        .instructor-name {
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .instructor-title {
            color: var(--secondary-color);
            font-size: 0.9rem;
            margin-bottom: 10px;
        }

        .instructor-experience {
            color: #666;
            font-size: 0.85rem;
        }

        .footer {
            background-color: var(--primary-color);
            color: white;
            padding: 40px 0;
            margin-top: 80px;
        }

        .calendar-widget {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            margin-bottom: 30px;
        }

        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--accent-color);
        }

        .calendar-nav {
            background: none;
            border: none;
            color: var(--primary-color);
            font-size: 1.2rem;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .calendar-nav:hover {
            color: var(--secondary-color);
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 5px;
        }

        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .calendar-day:hover {
            background: var(--accent-color);
        }

        .calendar-day.today {
            background: var(--primary-color);
            color: white;
        }

        .calendar-day.has-workshop {
            background: var(--secondary-color);
            color: white;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html">
                <i class="fas fa-lotus me-2"></i>佛教身心療癒
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">主頁</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about_page.html">關於我們</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">課程</a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses_goals_page.html">課程目標</a></li>
                            <li><a class="dropdown-item" href="courses_weekly_page.html">每週主題</a></li>
                            <li><a class="dropdown-item" href="courses_methods_page.html">進行方式</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">經文選讀</a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="scriptures_search_page.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="scriptures_methods_page.html">療癒方法</a></li>
                            <li><a class="dropdown-item" href="scriptures_theory_page.html">療癒理論</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">身心療癒研究</a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="research_search_page.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="research-theory.html">療癒理論</a></li>
                            <li><a class="dropdown-item" href="research-methods.html">療癒方法</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">藏傳佛教專題</a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="tibetan_theory_page.html">藏醫理論</a></li>
                            <li><a class="dropdown-item" href="tibetan_practice_page.html">藏醫實習</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">海外實習體驗</a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="overseas_members_page.html">實習成員</a></li>
                            <li><a class="dropdown-item" href="overseas_research_page.html">研究成果</a></li>
                            <li><a class="dropdown-item" href="overseas_future_plan_page.html">未來計畫</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="workshop_healing_page.html">工作坊</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="membership_page.html">加入會員</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 頁面標題 -->
    <div class="page-header">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.html">主頁</a></li>
                    <li class="breadcrumb-item active">身心療癒工作坊</li>
                </ol>
            </nav>
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">
                        <i class="fas fa-hands-helping me-3"></i>身心療癒工作坊
                    </h1>
                    <p class="lead mb-0">體驗式學習，深度探索佛教身心療癒的奧秘與實踐</p>
                </div>
                <div class="col-lg-4 text-end">
                    <button class="btn btn-outline-light btn-lg" data-bs-toggle="modal" data-bs-target="#calendarModal">
                        <i class="fas fa-calendar-alt me-2"></i>查看行事曆
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要內容 -->
    <div class="container py-5">
        <!-- 即將開始的工作坊 -->
        <div class="upcoming-section">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h3 class="fw-bold mb-3">
                        <i class="fas fa-star me-2"></i>即將開始
                    </h3>
                    <h4 class="fw-bold">正念冥想與情緒療癒工作坊</h4>
                    <p class="mb-3">透過正念冥想練習，學習觀察和調節情緒，建立內在的平靜與穩定。</p>
                    <div class="d-flex flex-wrap gap-3">
                        <span><i class="fas fa-calendar me-2"></i>2024年7月20日</span>
                        <span><i class="fas fa-clock me-2"></i>上午9:00-下午5:00</span>
                        <span><i class="fas fa-map-marker-alt me-2"></i>台北靜心中心</span>
                    </div>
                </div>
                <div class="col-lg-4 text-end">
                    <button class="btn btn-light btn-lg">
                        <i class="fas fa-arrow-right me-2"></i>立即報名
                    </button>
                </div>
            </div>
        </div>

        <!-- 篩選區域 -->
        <div class="filter-section">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text bg-white border-end-0">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" class="form-control search-box border-start-0" placeholder="搜尋工作坊主題...">
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="d-flex flex-wrap">
                        <button class="filter-btn active" data-filter="all">全部</button>
                        <button class="filter-btn" data-filter="meditation">冥想禪修</button>
                        <button class="filter-btn" data-filter="healing">療癒實踐</button>
                        <button class="filter-btn" data-filter="tibetan">藏醫專題</button>
                        <button class="filter-btn" data-filter="weekend">週末班</button>
                        <button class="filter-btn" data-filter="intensive">密集班</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 左側：工作坊列表 -->
            <div class="col-lg-8">
                <div class="row" id="workshopList">
                    <!-- 工作坊卡片 1 -->
                    <div class="col-md-6 workshop-item" data-category="meditation">
                        <div class="workshop-card">
                            <div class="workshop-image">
                                <span class="workshop-type">冥想禪修</span>
                                <span class="workshop-level level-beginner">初級</span>
                            </div>
                            <div class="workshop-content">
                                <span class="workshop-status status-available">報名中</span>
                                <h5 class="workshop-title">正念呼吸與內觀冥想</h5>
                                <div class="workshop-instructor">
                                    <i class="fas fa-user-graduate me-2"></i>釋法師 主講
                                </div>
                                <div class="workshop-details">
                                    學習基礎的正念呼吸技巧，培養專注力和覺察力，透過內觀練習深入了解心的本質。
                                </div>
                                <div class="workshop-schedule">
                                    <div class="schedule-item">
                                        <i class="fas fa-calendar schedule-icon"></i>
                                        <span>2024年7月25日 (週四)</span>
                                    </div>
                                    <div class="schedule-item">
                                        <i class="fas fa-clock schedule-icon"></i>
                                        <span>下午2:00-6:00</span>
                                    </div>
                                    <div class="schedule-item">
                                        <i class="fas fa-map-marker-alt schedule-icon"></i>
                                        <span>台北禪修中心</span>
                                    </div>
                                    <div class="schedule-item">
                                        <i class="fas fa-users schedule-icon"></i>
                                        <span>限額20人 (剩餘8名)</span>
                                    </div>
                                </div>
                                <div class="workshop-tags">
                                    <span class="workshop-tag">正念</span>
                                    <span class="workshop-tag">呼吸法</span>
                                    <span class="workshop-tag">內觀</span>
                                </div>
                                <div class="workshop-price">
                                    <div>
                                        <span class="price-original">NT$1,800</span>
                                        <span class="price-amount">NT$1,500</span>
                                    </div>
                                    <small class="text-muted">會員優惠</small>
                                </div>
                                <button class="btn btn-register">立即報名</button>
                            </div>
                        </div>
                    </div>

                    <!-- 工作坊卡片 2 -->
                    <div class="col-md-6 workshop-item" data-category="healing">
                        <div class="workshop-card">
                            <div class="workshop-image">
                                <span class="workshop-type">療癒實踐</span>
                                <span class="workshop-level level-intermediate">中級</span>
                            </div>
                            <div class="workshop-content">
                                <span class="workshop-status status-filling">即將額滿</span>
                                <h5 class="workshop-title">聲音療癒與梵唱體驗</h5>
                                <div class="workshop-instructor">
                                    <i class="fas fa-user-graduate me-2"></i>明師傅 主講
                                </div>
                                <div class="workshop-details">
                                    探索聲音的療癒力量，學習梵唱和音聲冥想，透過振動頻率調和身心能量。
                                </div>
                                <div class="workshop-schedule">
                                    <div class="schedule-item">
                                        <i class="fas fa-calendar schedule-icon"></i>
                                        <span>2024年7月28日 (週日)</span>
                                    </div>
                                    <div class="schedule-item">
                                        <i class="fas fa-clock schedule-icon"></i>
                                        <span>上午10:00-下午4:00</span>
                                    </div>
                                    <div class="schedule-item">
                                        <i class="fas fa-map-marker-alt schedule-icon"></i>
                                        <span>新竹療癒空間</span>
                                    </div>
                                    <div class="schedule-item">
                                        <i class="fas fa-users schedule-icon"></i>
                                        <span>限額15人 (剩餘2名)</span>
                                    </div>
                                </div>
                                <div class="workshop-tags">
                                    <span class="workshop-tag">聲音療癒</span>
                                    <span class="workshop-tag">梵唱</span>
                                    <span class="workshop-tag">能量調和</span>
                                </div>
                                <div class="workshop-price">
                                    <div>
                                        <span class="price-amount">NT$2,200</span>
                                    </div>
                                    <small class="text-muted">含餐點</small>
                                </div>
                                <button class="btn btn-register">立即報名</button>
                            </div>
                        </div>
                    </div>

                    <!-- 工作坊卡片 3 -->
                    <div class="col-md-6 workshop-item" data-category="tibetan">
                        <div class="workshop-card">
                            <div class="workshop-image">
                                <span class="workshop-type">藏醫專題</span>
                                <span class="workshop-level level-advanced">高級</span>
                            </div>
                            <div class="workshop-content">
                                <span class="workshop-status status-available">報名中</span>
                                <h5 class="workshop-title">藏醫三根理論與診斷</h5>
                                <div class="workshop-instructor">
                                    <i class="fas fa-user-graduate me-2"></i>洛桑醫師 主講
                                </div>
                                <div class="workshop-details">
                                    深入學習藏醫三根理論，掌握脈診、尿診等傳統診斷方法，理解藏醫的整體療癒觀。
                                </div>
                                <div class="workshop-schedule">
                                    <div class="schedule-item">
                                        <i class="fas fa-calendar schedule-icon"></i>
                                        <span>2024年8月3-4日 (週末)</span>
                                    </div>
                                    <div class="schedule-item">
                                        <i class="fas fa-clock schedule-icon"></i>
                                        <span>每日9:00-17:00</span>
                                    </div>
                                    <div class="schedule-item">
                                        <i class="fas fa-map-marker-alt schedule-icon"></i>
                                        <span>台中藏醫學會</span>
                                    </div>
                                    <div class="schedule-item">
                                        <i class="fas fa-users schedule-icon"></i>
                                        <span>限額12人 (剩餘6名)</span>
                                    </div>
                                </div>
                                <div class="workshop-tags">
                                    <span class="workshop-tag">三根理論</span>
                                    <span class="workshop-tag">脈診</span>
                                    <span class="workshop-tag">診斷學</span>
                                </div>
                                <div class="workshop-price">
                                    <div>
                                        <span class="price-amount">NT$4,500</span>
                                    </div>
                                    <small class="text-muted">兩日課程</small>
                                </div>
                                <button class="btn btn-register">立即報名</button>
                            </div>
                        </div>
                    </div>

                    <!-- 工作坊卡片 4 -->
                    <div class="col-md-6 workshop-item" data-category="weekend">
                        <div class="workshop-card">
                            <div class="workshop-image">
                                <span class="workshop-type">週末班</span>
                                <span class="workshop-level level-beginner">初級</span>
                            </div>
                            <div class="workshop-content">
                                <span class="workshop-status status-full">已額滿</span>
                                <h5 class="workshop-title">佛教心理學入門</h5>
                                <div class="workshop-instructor">
                                    <i class="fas fa-user-graduate me-2"></i>陳教授 主講
                                </div>
                                <div class="workshop-details">
                                    從現代心理學角度解讀佛教智慧，學習如何運用佛教心理學改善日常生活和人際關係。
                                </div>
                                <div class="workshop-schedule">
                                    <div class="schedule-item">
                                        <i class="fas fa-calendar schedule-icon"></i>
                                        <span>2024年8月10日 (週六)</span>
                                    </div>
                                    <div class="schedule-item">
                                        <i class="fas fa-clock schedule-icon"></i>
                                        <span>上午9:00-下午5:00</span>
                                    </div>
                                    <div class="schedule-item">
                                        <i class="fas fa-map-marker-alt schedule-icon"></i>
                                        <span>高雄學習中心</span>
                                    </div>
                                    <div class="schedule-item">
                                        <i class="fas fa-users schedule-icon"></i>
                                        <span>限額25人 (已額滿)</span>
                                    </div>
                                </div>
                                <div class="workshop-tags">
                                    <span class="workshop-tag">心理學</span>
                                    <span class="workshop-tag">日常應用</span>
                                    <span class="workshop-tag">人際關係</span>
                                </div>
                                <div class="workshop-price">
                                    <div>
                                        <span class="price-amount">NT$1,800</span>
                                    </div>
                                    <small class="text-muted">候補名單</small>
                                </div>
                                <button class="btn btn-register full" disabled>加入候補</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分頁 -->
                <div class="d-flex justify-content-center mt-4">
                    <nav aria-label="工作坊分頁">
                        <ul class="pagination">
                            <li class="page-item disabled">
                                <a class="page-link" href="#">上一頁</a>
                            </li>
                            <li class="page-item active">
                                <a class="page-link" href="#">1</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">3</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">下一頁</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>

            <!-- 右側：講師介紹和行事曆 -->
            <div class="col-lg-4">
                <!-- 行事曆 Widget -->
                <div class="calendar-widget">
                    <div class="calendar-header">
                        <button class="calendar-nav" id="prevMonth">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <h5 class="fw-bold mb-0" id="currentMonth">2024年7月</h5>
                        <button class="calendar-nav" id="nextMonth">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    <div class="calendar-grid" id="calendarGrid">
                        <!-- 日曆會由 JavaScript 生成 -->
                    </div>
                </div>

                <!-- 精選講師 -->
                <div class="mb-4">
                    <h4 class="fw-bold mb-3">
                        <i class="fas fa-chalkboard-teacher me-2"></i>精選講師
                    </h4>
                    
                    <div class="instructor-card">
                        <img src="https://via.placeholder.com/80x80" alt="釋法師" class="instructor-avatar">
                        <div class="instructor-name">釋法師</div>
                        <div class="instructor-title">正念冥想導師</div>
                        <div class="instructor-experience">20年禪修經驗，專精正念療癒</div>
                    </div>

                    <div class="instructor-card">
                        <img src="https://via.placeholder.com/80x80" alt="明師傅" class="instructor-avatar">
                        <div class="instructor-name">明師傅</div>
                        <div class="instructor-title">聲音療癒師</div>
                        <div class="instructor-experience">梵唱與音聲冥想專家</div>
                    </div>

                    <div class="instructor-card">
                        <img src="https://via.placeholder.com/80x80" alt="洛桑醫師" class="instructor-avatar">
                        <div class="instructor-name">洛桑醫師</div>
                        <div class="instructor-title">藏醫學博士</div>
                        <div class="instructor-experience">傳統藏醫理論與實踐專家</div>
                    </div>
                </div>

                <!-- 工作坊須知 -->
                <div class="plan-card">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-info-circle me-2"></i>參與須知
                    </h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>請穿著舒適寬鬆衣物</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>自備瑜珈墊或坐墊</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>提前15分鐘到場</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>保持開放學習心態</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>尊重他人與環境</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 行事曆 Modal -->
    <div class="modal fade" id="calendarModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-calendar-alt me-2"></i>工作坊行事曆
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <!-- 這裡可以放置更大的日曆 -->
                            <p>完整的工作坊行事曆會顯示在這裡...</p>
                        </div>
                        <div class="col-md-4">
                            <h6 class="fw-bold">即將舉行</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <strong>7/25</strong> - 正念呼吸與內觀冥想
                                </li>
                                <li class="mb-2">
                                    <strong>7/28</strong> - 聲音療癒與梵唱體驗
                                </li>
                                <li class="mb-2">
                                    <strong>8/3-4</strong> - 藏醫三根理論與診斷
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 頁尾 -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <h5><i class="fas fa-lotus me-2"></i>佛教身心療癒網站</h5>
                    <p class="mb-0">致力於推廣佛教身心療癒智慧，提供專業的學習與實踐平台。</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <p class="mb-0">&copy; 2024 佛教身心療癒網站. 版權所有.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // 篩選功能
        const filterBtns = document.querySelectorAll('.filter-btn');
        const workshopItems = document.querySelectorAll('.workshop-item');

        filterBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                // 移除所有 active 類別
                filterBtns.forEach(b => b.classList.remove('active'));
                // 添加 active 類別到當前按鈕
                btn.classList.add('active');

                const filter = btn.getAttribute('data-filter');

                workshopItems.forEach(item => {
                    if (filter === 'all' || item.getAttribute('data-category') === filter) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });

        // 搜尋功能
        const searchBox = document.querySelector('.search-box');
        searchBox.addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase();

            workshopItems.forEach(item => {
                const title = item.querySelector('.workshop-title').textContent.toLowerCase();
                const description = item.querySelector('.workshop-details').textContent.toLowerCase();
                
                if (title.includes(searchTerm) || description.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });

        // 簡單的日曆功能
        function generateCalendar() {
            const calendarGrid = document.getElementById('calendarGrid');
            const currentDate = new Date();
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth();
            
            // 清空日曆
            calendarGrid.innerHTML = '';
            
            // 添加星期標題
            const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
            weekdays.forEach(day => {
                const dayElement = document.createElement('div');
                dayElement.textContent = day;
                dayElement.classList.add('text-muted', 'text-center', 'fw-bold');
                calendarGrid.appendChild(dayElement);
            });
            
            // 獲取本月第一天和天數
            const firstDay = new Date(year, month, 1).getDay();
            const daysInMonth = new Date(year, month + 1, 0).getDate();
            
            // 添加空白天數
            for (let i = 0; i < firstDay; i++) {
                const emptyDay = document.createElement('div');
                calendarGrid.appendChild(emptyDay);
            }
            
            // 添加月份天數
            for (let day = 1; day <= daysInMonth; day++) {
                const dayElement = document.createElement('div');
                dayElement.textContent = day;
                dayElement.classList.add('calendar-day');
                
                // 標記今天
                if (day === currentDate.getDate() && month === currentDate.getMonth()) {
                    dayElement.classList.add('today');
                }
                
                // 標記有工作坊的日子
                if ([20, 25, 28].includes(day)) {
                    dayElement.classList.add('has-workshop');
                    dayElement.title = '有工作坊';
                }
                
                calendarGrid.appendChild(dayElement);
            }
        }

        // 初始化日曆
        generateCalendar();

        // 報名按鈕功能
        document.querySelectorAll('.btn-register').forEach(btn => {
            btn.addEventListener('click', function() {
                if (!this.disabled) {
                    alert('即將跳轉至報名頁面...');
                }
            });
        });
    </script>
</body>
</html>