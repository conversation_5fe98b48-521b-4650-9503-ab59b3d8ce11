const nodemailer = require('nodemailer');
const path = require('path');
const fs = require('fs').promises;

// 郵件模板緩存
const templateCache = new Map();

/**
 * 創建郵件傳輸器
 */
const createTransporter = () => {
    // 根據環境配置不同的郵件服務
    if (process.env.NODE_ENV === 'production') {
        // 生產環境使用正式的 SMTP 服務
        return nodemailer.createTransporter({
            host: process.env.SMTP_HOST,
            port: process.env.SMTP_PORT || 587,
            secure: process.env.SMTP_SECURE === 'true',
            auth: {
                user: process.env.SMTP_USER,
                pass: process.env.SMTP_PASS
            },
            pool: true,
            maxConnections: 5,
            maxMessages: 100
        });
    } else {
        // 開發環境使用 Ethereal Email（測試用）
        return nodemailer.createTransporter({
            host: 'smtp.ethereal.email',
            port: 587,
            auth: {
                user: process.env.ETHEREAL_USER || '<EMAIL>',
                pass: process.env.ETHEREAL_PASS || 'ethereal.pass'
            }
        });
    }
};

/**
 * 載入郵件模板
 */
const loadTemplate = async (templateName) => {
    // 檢查緩存
    if (templateCache.has(templateName)) {
        return templateCache.get(templateName);
    }

    try {
        const templatePath = path.join(__dirname, '../templates/emails', `${templateName}.html`);
        const template = await fs.readFile(templatePath, 'utf-8');
        
        // 緩存模板
        templateCache.set(templateName, template);
        return template;
    } catch (error) {
        console.error(`Failed to load email template: ${templateName}`, error);
        return getDefaultTemplate(templateName);
    }
};

/**
 * 獲取預設模板
 */
const getDefaultTemplate = (templateName) => {
    const templates = {
        welcome: `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>歡迎加入佛教身心療癒社群</title>
                <style>
                    body { font-family: 'Arial', sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: linear-gradient(135deg, #2C3E50 0%, #34495e 100%); color: white; text-align: center; padding: 30px; border-radius: 10px 10px 0 0; }
                    .content { background: white; padding: 30px; border: 1px solid #ddd; }
                    .button { display: inline-block; background: #F39C12; color: white; padding: 12px 30px; text-decoration: none; border-radius: 25px; margin: 20px 0; }
                    .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #666; border-radius: 0 0 10px 10px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🕉️ 歡迎加入佛教身心療癒社群</h1>
                    </div>
                    <div class="content">
                        <h2>親愛的 {{name}}，</h2>
                        <p>恭喜您成功註冊成為我們的 <strong>{{membershipPlan}}</strong> 會員！</p>
                        <p>為了確保帳戶安全，請點擊下方按鈕驗證您的郵件地址：</p>
                        <p style="text-align: center;">
                            <a href="{{verificationUrl}}" class="button">驗證郵件地址</a>
                        </p>
                        <p>驗證完成後，您將可以：</p>
                        <ul>
                            <li>🧘‍♀️ 參與正念冥想課程</li>
                            <li>📚 閱讀豐富的經文資料庫</li>
                            <li>👥 加入學習社群討論</li>
                            <li>🎓 參加專業工作坊</li>
                            <li>🌏 申請海外實習體驗</li>
                        </ul>
                        <p>如有任何問題，請隨時聯繫我們的客服團隊。</p>
                        <p>祝您學習愉快！</p>
                    </div>
                    <div class="footer">
                        <p>此郵件由佛教身心療癒網站自動發送</p>
                        <p>如果您沒有註冊此帳戶，請忽略此郵件</p>
                    </div>
                </div>
            </body>
            </html>
        `,
        passwordReset: `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>重置您的密碼</title>
                <style>
                    body { font-family: 'Arial', sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: linear-gradient(135deg, #E74C3C 0%, #C0392B 100%); color: white; text-align: center; padding: 30px; border-radius: 10px 10px 0 0; }
                    .content { background: white; padding: 30px; border: 1px solid #ddd; }
                    .button { display: inline-block; background: #E74C3C; color: white; padding: 12px 30px; text-decoration: none; border-radius: 25px; margin: 20px 0; }
                    .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #666; border-radius: 0 0 10px 10px; }
                    .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🔒 密碼重置請求</h1>
                    </div>
                    <div class="content">
                        <h2>親愛的 {{name}}，</h2>
                        <p>我們收到了重置您帳戶密碼的請求。</p>
                        <p>請點擊下方按鈕重置您的密碼：</p>
                        <p style="text-align: center;">
                            <a href="{{resetUrl}}" class="button">重置密碼</a>
                        </p>
                        <div class="warning">
                            <strong>⚠️ 重要提醒：</strong>
                            <ul>
                                <li>此連結將在 <strong>{{expiresIn}}</strong> 後過期</li>
                                <li>如果不是您本人操作，請忽略此郵件</li>
                                <li>為了安全，請勿將此連結分享給他人</li>
                            </ul>
                        </div>
                        <p>如果您沒有請求重置密碼，您的帳戶可能面臨安全風險，請立即聯繫我們的客服團隊。</p>
                    </div>
                    <div class="footer">
                        <p>此郵件由佛教身心療癒網站自動發送</p>
                        <p>請勿回覆此郵件</p>
                    </div>
                </div>
            </body>
            </html>
        `,
        emailVerified: `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>郵件驗證成功</title>
                <style>
                    body { font-family: 'Arial', sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: linear-gradient(135deg, #27AE60 0%, #229954 100%); color: white; text-align: center; padding: 30px; border-radius: 10px 10px 0 0; }
                    .content { background: white; padding: 30px; border: 1px solid #ddd; }
                    .button { display: inline-block; background: #27AE60; color: white; padding: 12px 30px; text-decoration: none; border-radius: 25px; margin: 20px 0; }
                    .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #666; border-radius: 0 0 10px 10px; }
                    .success { background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0; color: #155724; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>✅ 驗證成功！</h1>
                    </div>
                    <div class="content">
                        <h2>親愛的 {{name}}，</h2>
                        <div class="success">
                            <strong>🎉 恭喜！</strong> 您的郵件地址已成功驗證，帳戶已啟用。
                        </div>
                        <p>您現在可以享受所有 <strong>{{membershipPlan}}</strong> 會員權益：</p>
                        <ul>
                            <li>🧘‍♀️ 參與正念冥想課程</li>
                            <li>📚 無限制閱讀經文資料庫</li>
                            <li>👥 加入學習社群討論</li>
                            <li>🎓 優先報名工作坊</li>
                            <li>🌏 申請海外實習體驗</li>
                        </ul>
                        <p style="text-align: center;">
                            <a href="{{loginUrl}}" class="button">立即開始學習</a>
                        </p>
                        <p>歡迎來到佛教身心療癒大家庭！</p>
                    </div>
                    <div class="footer">
                        <p>此郵件由佛教身心療癒網站自動發送</p>
                    </div>
                </div>
            </body>
            </html>
        `
    };

    return templates[templateName] || `
        <html>
        <body>
            <h2>{{title}}</h2>
            <p>{{message}}</p>
        </body>
        </html>
    `;
};

/**
 * 渲染模板
 */
const renderTemplate = (template, data) => {
    let rendered = template;
    
    // 替換模板變數
    Object.keys(data).forEach(key => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        rendered = rendered.replace(regex, data[key] || '');
    });
    
    return rendered;
};

/**
 * 發送郵件
 */
const sendEmail = async (options) => {
    try {
        const transporter = createTransporter();
        
        // 載入和渲染模板
        let html = options.html;
        if (options.template && options.data) {
            const template = await loadTemplate(options.template);
            html = renderTemplate(template, options.data);
        }

        // 郵件選項
        const mailOptions = {
            from: {
                name: '佛教身心療癒網站',
                address: process.env.SMTP_FROM || '<EMAIL>'
            },
            to: options.to,
            subject: options.subject,
            html: html,
            text: options.text, // 純文字版本
            attachments: options.attachments || []
        };

        // 發送郵件
        const result = await transporter.sendMail(mailOptions);
        
        // 開發環境顯示預覽連結
        if (process.env.NODE_ENV !== 'production') {
            console.log('📧 Email sent successfully');
            console.log('Preview URL:', nodemailer.getTestMessageUrl(result));
        }

        return {
            success: true,
            messageId: result.messageId,
            previewUrl: process.env.NODE_ENV !== 'production' 
                ? nodemailer.getTestMessageUrl(result) 
                : null
        };

    } catch (error) {
        console.error('Email sending failed:', error);
        throw new Error(`郵件發送失敗: ${error.message}`);
    }
};

/**
 * 批量發送郵件
 */
const sendBulkEmail = async (recipients, templateName, commonData) => {
    const results = [];
    const batchSize = 10; // 每批處理10封郵件

    for (let i = 0; i < recipients.length; i += batchSize) {
        const batch = recipients.slice(i, i + batchSize);
        
        const batchPromises = batch.map(async (recipient) => {
            try {
                const data = { ...commonData, ...recipient.data };
                const result = await sendEmail({
                    to: recipient.email,
                    subject: recipient.subject || commonData.subject,
                    template: templateName,
                    data: data
                });
                
                return {
                    email: recipient.email,
                    success: true,
                    messageId: result.messageId
                };
            } catch (error) {
                return {
                    email: recipient.email,
                    success: false,
                    error: error.message
                };
            }
        });

        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);

        // 批次間稍作暫停，避免過載
        if (i + batchSize < recipients.length) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    return results;
};

/**
 * 驗證郵件地址格式
 */
const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};

/**
 * 清理模板緩存
 */
const clearTemplateCache = () => {
    templateCache.clear();
};

module.exports = {
    sendEmail,
    sendBulkEmail,
    isValidEmail,
    clearTemplateCache,
    renderTemplate
};