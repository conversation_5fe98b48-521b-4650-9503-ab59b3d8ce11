<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>未來計畫 - 佛教身心療癒網站</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2C3E50;
            --secondary-color: #F39C12;
            --accent-color: #E8F4F8;
            --text-color: #2C3E50;
            --light-bg: #F8F9FA;
            --success-color: #27AE60;
            --warning-color: #E67E22;
            --info-color: #3498DB;
        }

        body {
            font-family: 'Noto Sans CJK TC', 'Microsoft JhengHei', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
            background-color: var(--light-bg);
        }

        .navbar {
            background-color: var(--primary-color) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand, .nav-link {
            color: white !important;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--secondary-color) !important;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            padding: 80px 0 60px;
            margin-bottom: 0;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            opacity: 0.3;
        }

        .breadcrumb {
            background: rgba(255,255,255,0.1);
            border-radius: 25px;
            padding: 10px 20px;
            margin-bottom: 30px;
        }

        .breadcrumb-item a {
            color: var(--secondary-color);
            text-decoration: none;
        }

        .breadcrumb-item.active {
            color: white;
        }

        .timeline {
            position: relative;
            padding: 0;
            list-style: none;
        }

        .timeline::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: 40px;
            width: 4px;
            background: linear-gradient(180deg, var(--secondary-color) 0%, var(--primary-color) 100%);
            border-radius: 2px;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 50px;
            padding-left: 100px;
        }

        .timeline-marker {
            position: absolute;
            left: 0;
            top: 0;
            width: 80px;
            height: 80px;
            background: white;
            border: 4px solid var(--secondary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--secondary-color);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            z-index: 2;
        }

        .timeline-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            border-left: 5px solid var(--secondary-color);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .timeline-content:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .timeline-year {
            display: inline-block;
            background: var(--secondary-color);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .plan-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 5px solid var(--info-color);
        }

        .plan-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .plan-priority {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .priority-high {
            background-color: #E74C3C;
            color: white;
        }

        .priority-medium {
            background-color: var(--warning-color);
            color: white;
        }

        .priority-low {
            background-color: var(--success-color);
            color: white;
        }

        .vision-card {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
        }

        .vision-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20px;
            width: 200px;
            height: 200px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
        }

        .partner-logo {
            width: 120px;
            height: 80px;
            object-fit: contain;
            background: white;
            padding: 10px;
            border-radius: 10px;
            margin: 10px;
            transition: transform 0.3s ease;
        }

        .partner-logo:hover {
            transform: scale(1.05);
        }

        .progress-indicator {
            background: var(--accent-color);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .progress-bar-custom {
            height: 8px;
            border-radius: 4px;
            background: linear-gradient(90deg, var(--secondary-color) 0%, var(--primary-color) 100%);
        }

        .milestone-badge {
            background: var(--secondary-color);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            display: inline-block;
            margin: 5px 5px 5px 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-item {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: #34495e;
            transform: translateY(-2px);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 600;
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .footer {
            background-color: var(--primary-color);
            color: white;
            padding: 40px 0;
            margin-top: 80px;
        }

        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html">
                <i class="fas fa-lotus me-2"></i>佛教身心療癒
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">主頁</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about_page.html">關於我們</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">課程</a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses_goals_page.html">課程目標</a></li>
                            <li><a class="dropdown-item" href="courses_weekly_page.html">每週主題</a></li>
                            <li><a class="dropdown-item" href="courses_methods_page.html">進行方式</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">經文選讀</a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="scriptures_search_page.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="scriptures_methods_page.html">療癒方法</a></li>
                            <li><a class="dropdown-item" href="scriptures_theory_page.html">療癒理論</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">身心療癒研究</a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="research_search_page.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="research-theory.html">療癒理論</a></li>
                            <li><a class="dropdown-item" href="research-methods.html">療癒方法</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">藏傳佛教專題</a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="tibetan_theory_page.html">藏醫理論</a></li>
                            <li><a class="dropdown-item" href="tibetan_practice_page.html">藏醫實習</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown">海外實習體驗</a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="overseas_members_page.html">實習成員</a></li>
                            <li><a class="dropdown-item" href="overseas_research_page.html">研究成果</a></li>
                            <li><a class="dropdown-item active" href="overseas_future_plan_page.html">未來計畫</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="workshop_page.html">工作坊</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="membership_page.html">加入會員</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 頁面標題 -->
    <div class="page-header">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.html">主頁</a></li>
                    <li class="breadcrumb-item"><a href="#">海外實習體驗</a></li>
                    <li class="breadcrumb-item active">未來計畫</li>
                </ol>
            </nav>
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">
                        <i class="fas fa-rocket me-3"></i>未來發展計畫
                    </h1>
                    <p class="lead mb-0">展望未來五年，打造更全面的佛教身心療癒教育體系</p>
                </div>
                <div class="col-lg-4 text-end">
                    <button class="btn btn-outline-light btn-lg">
                        <i class="fas fa-download me-2"></i>下載計畫書
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要內容 -->
    <div class="container py-5">
        <!-- 願景聲明 -->
        <div class="vision-card fade-in mb-5">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h2 class="fw-bold mb-3">
                        <i class="fas fa-eye me-2"></i>我們的願景
                    </h2>
                    <p class="lead mb-4">
                        成為全球領先的佛教身心療癒教育機構，透過創新的教學方法和國際合作，
                        培養具備專業能力和慈悲智慧的療癒師，為世界帶來更多的安寧與療癒。
                    </p>
                    <div class="milestone-badge">2030年目標</div>
                    <div class="milestone-badge">國際認證</div>
                    <div class="milestone-badge">全球合作</div>
                </div>
                <div class="col-lg-4 text-center">
                    <i class="fas fa-globe-asia" style="font-size: 8rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>

        <!-- 統計展望 -->
        <div class="stats-grid fade-in">
            <div class="stat-item">
                <div class="stat-number">500+</div>
                <h5>目標學員數</h5>
                <p class="text-muted mb-0">2030年前</p>
            </div>
            <div class="stat-item">
                <div class="stat-number text-success">20</div>
                <h5>合作機構</h5>
                <p class="text-muted mb-0">國際夥伴</p>
            </div>
            <div class="stat-item">
                <div class="stat-number text-warning">10</div>
                <h5>實習據點</h5>
                <p class="text-muted mb-0">全球佈局</p>
            </div>
            <div class="stat-item">
                <div class="stat-number text-info">5</div>
                <h5>專業認證</h5>
                <p class="text-muted mb-0">國際標準</p>
            </div>
        </div>

        <!-- 發展時程表 -->
        <div class="row">
            <div class="col-12">
                <h2 class="text-center mb-5 fade-in">
                    <i class="fas fa-calendar-alt me-2"></i>五年發展藍圖
                </h2>
                
                <ul class="timeline">
                    <!-- 2024年 -->
                    <li class="timeline-item fade-in">
                        <div class="timeline-marker">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="timeline-content">
                            <span class="timeline-year">2024年</span>
                            <h4 class="fw-bold text-primary">基礎建設年</h4>
                            
                            <div class="progress-indicator">
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="fw-bold">整體進度</span>
                                    <span>75%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar progress-bar-custom" style="width: 75%"></div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="fw-bold">已完成目標：</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>網站平台建立</li>
                                        <li><i class="fas fa-check text-success me-2"></i>課程體系規劃</li>
                                        <li><i class="fas fa-check text-success me-2"></i>師資團隊組建</li>
                                        <li><i class="fas fa-check text-success me-2"></i>首批實習計畫啟動</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="fw-bold">進行中項目：</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-spinner fa-spin text-warning me-2"></i>國際合作洽談</li>
                                        <li><i class="fas fa-spinner fa-spin text-warning me-2"></i>認證體系建立</li>
                                        <li><i class="fas fa-spinner fa-spin text-warning me-2"></i>數位學習平台</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </li>

                    <!-- 2025年 -->
                    <li class="timeline-item fade-in">
                        <div class="timeline-marker">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </div>
                        <div class="timeline-content">
                            <span class="timeline-year">2025年</span>
                            <h4 class="fw-bold text-primary">擴展發展年</h4>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="plan-card">
                                        <span class="plan-priority priority-high">高優先</span>
                                        <h6 class="fw-bold mt-2">國際據點設立</h6>
                                        <p class="text-muted mb-0">在尼泊爾、印度建立常設據點</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="plan-card">
                                        <span class="plan-priority priority-high">高優先</span>
                                        <h6 class="fw-bold mt-2">專業認證啟動</h6>
                                        <p class="text-muted mb-0">推出療癒師認證課程</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="plan-card">
                                        <span class="plan-priority priority-medium">中優先</span>
                                        <h6 class="fw-bold mt-2">研究中心成立</h6>
                                        <p class="text-muted mb-0">建立學術研究部門</p>
                                    </div>
                                </div>
                            </div>

                            <h6 class="fw-bold">年度目標：</h6>
                            <ul>
                                <li>學員人數擴展至 100 人</li>
                                <li>建立 3 個海外實習據點</li>
                                <li>發展 5 門專業認證課程</li>
                                <li>完成首期師資培訓計畫</li>
                            </ul>
                        </div>
                    </li>

                    <!-- 2026年 -->
                    <li class="timeline-item fade-in">
                        <div class="timeline-marker">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <div class="timeline-content">
                            <span class="timeline-year">2026年</span>
                            <h4 class="fw-bold text-primary">專業化年</h4>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="fw-bold">核心發展：</h6>
                                    <ul>
                                        <li><strong>學位課程</strong> - 與大學合作開設學位學程</li>
                                        <li><strong>研究發表</strong> - 國際期刊論文發表</li>
                                        <li><strong>技術整合</strong> - VR/AR 沉浸式學習</li>
                                        <li><strong>社區服務</strong> - 公益療癒服務計畫</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="fw-bold">合作夥伴：</h6>
                                    <ul>
                                        <li>國立大學宗教學系</li>
                                        <li>醫學院身心醫學科</li>
                                        <li>國際佛教組織</li>
                                        <li>心理治療協會</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </li>

                    <!-- 2027年 -->
                    <li class="timeline-item fade-in">
                        <div class="timeline-marker">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div class="timeline-content">
                            <span class="timeline-year">2027年</span>
                            <h4 class="fw-bold text-primary">國際化年</h4>
                            
                            <div class="row">
                                <div class="col-md-12">
                                    <h6 class="fw-bold">全球佈局策略：</h6>
                                    <div class="row">
                                        <div class="col-md-3 text-center">
                                            <i class="fas fa-map-marker-alt fa-2x text-primary mb-2"></i>
                                            <h6>亞洲區</h6>
                                            <p class="small">印度、尼泊爾、不丹、斯里蘭卡</p>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <i class="fas fa-map-marker-alt fa-2x text-success mb-2"></i>
                                            <h6>歐洲區</h6>
                                            <p class="small">英國、德國、法國、荷蘭</p>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <i class="fas fa-map-marker-alt fa-2x text-warning mb-2"></i>
                                            <h6>美洲區</h6>
                                            <p class="small">美國、加拿大、巴西</p>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <i class="fas fa-map-marker-alt fa-2x text-info mb-2"></i>
                                            <h6>大洋洲</h6>
                                            <p class="small">澳洲、紐西蘭</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <span class="plan-priority priority-high">重點項目</span>
                                <h6 class="fw-bold mt-2">國際療癒師聯盟成立</h6>
                                <p>建立全球佛教身心療癒師專業網絡，制定國際標準與認證制度。</p>
                            </div>
                        </div>
                    </li>

                    <!-- 2028-2030年 -->
                    <li class="timeline-item fade-in">
                        <div class="timeline-marker">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div class="timeline-content">
                            <span class="timeline-year">2028-2030年</span>
                            <h4 class="fw-bold text-primary">領導地位確立</h4>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="fw-bold">成就里程碑：</h6>
                                    <ul class="list-unstyled">
                                        <li class="mb-2">
                                            <span class="milestone-badge">學術成就</span>
                                            <span class="ms-2">成為世界療癒教育權威</span>
                                        </li>
                                        <li class="mb-2">
                                            <span class="milestone-badge">社會影響</span>
                                            <span class="ms-2">服務全球10萬人次</span>
                                        </li>
                                        <li class="mb-2">
                                            <span class="milestone-badge">技術創新</span>
                                            <span class="ms-2">AI輔助個人化療癒</span>
                                        </li>
                                        <li class="mb-2">
                                            <span class="milestone-badge">永續發展</span>
                                            <span class="ms-2">建立自給自足體系</span>
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="fw-bold">傳承計畫：</h6>
                                    <ul>
                                        <li>培養下一代領導者</li>
                                        <li>建立永續運營模式</li>
                                        <li>創立教育基金會</li>
                                        <li>編撰權威教材體系</li>
                                        <li>建立全球校友網絡</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 合作夥伴 -->
        <div class="row mt-5">
            <div class="col-12">
                <h3 class="text-center mb-4 fade-in">
                    <i class="fas fa-handshake me-2"></i>戰略合作夥伴
                </h3>
                <div class="text-center fade-in">
                    <img src="https://via.placeholder.com/120x80?text=大學" alt="合作大學" class="partner-logo">
                    <img src="https://via.placeholder.com/120x80?text=醫院" alt="合作醫院" class="partner-logo">
                    <img src="https://via.placeholder.com/120x80?text=寺院" alt="合作寺院" class="partner-logo">
                    <img src="https://via.placeholder.com/120x80?text=NGO" alt="國際組織" class="partner-logo">
                    <img src="https://via.placeholder.com/120x80?text=政府" alt="政府機構" class="partner-logo">
                    <img src="https://via.placeholder.com/120x80?text=企業" alt="企業夥伴" class="partner-logo">
                </div>
            </div>
        </div>

        <!-- 行動呼籲 -->
        <div class="vision-card mt-5 fade-in">
            <div class="text-center">
                <h3 class="fw-bold mb-3">
                    <i class="fas fa-hands-helping me-2"></i>一起參與這個願景
                </h3>
                <p class="lead mb-4">
                    無論您是學者、醫療工作者、或是對身心療癒有興趣的朋友，
                    我們都誠摯邀請您加入這個有意義的計畫。
                </p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <button class="btn btn-outline-light btn-lg">
                        <i class="fas fa-user-plus me-2"></i>成為合作夥伴
                    </button>
                    <button class="btn btn-outline-light btn-lg">
                        <i class="fas fa-heart me-2"></i>支持我們
                    </button>
                    <button class="btn btn-outline-light btn-lg">
                        <i class="fas fa-envelope me-2"></i>聯絡我們
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 頁尾 -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <h5><i class="fas fa-lotus me-2"></i>佛教身心療癒網站</h5>
                    <p class="mb-0">致力於推廣佛教身心療癒智慧，提供專業的學習與實踐平台。</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <p class="mb-0">&copy; 2024 佛教身心療癒網站. 版權所有.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // 滾動動畫
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // 觀察所有需要動畫的元素
        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // 進度條動畫
        function animateProgressBar() {
            const progressBars = document.querySelectorAll('.progress-bar-custom');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.transition = 'width 2s ease-in-out';
                    bar.style.width = width;
                }, 500);
            });
        }

        // 當頁面載入完成後執行動畫
        window.addEventListener('load', () => {
            setTimeout(animateProgressBar, 1000);
        });

        // 平滑滾動
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 統計數字動畫
        function animateNumbers() {
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalNumber = parseInt(stat.textContent.replace(/\D/g, ''));
                const symbol = stat.textContent.replace(/\d/g, '');
                let current = 0;
                const increment = finalNumber / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= finalNumber) {
                        current = finalNumber;
                        clearInterval(timer);
                    }
                    stat.textContent = Math.floor(current) + symbol;
                }, 40);
            });
        }

        // 當統計區域進入視野時執行數字動畫
        const statsObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateNumbers();
                    statsObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        const statsGrid = document.querySelector('.stats-grid');
        if (statsGrid) {
            statsObserver.observe(statsGrid);
        }
    </script>
</body>
</html>