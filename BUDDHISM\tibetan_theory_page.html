<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>藏醫理論 - 佛教身心療癒網站</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2C3E50;
            --secondary-color: #F39C12;
            --accent-color: #E8F4F8;
            --text-color: #2C3E50;
            --light-bg: #F8F9FA;
            --success-color: #27AE60;
            --info-color: #3498DB;
            --tibetan-red: #D73027;
            --tibetan-gold: #FFD700;
        }

        body {
            font-family: 'Noto Sans CJK TC', 'Microsoft JhengHei', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #8B4513 100%);
            color: white;
            padding: 120px 0 80px;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,215,0,0.2)"/><circle cx="80" cy="80" r="2" fill="rgba(255,215,0,0.2)"/></svg>') repeat;
        }

        .navbar-custom {
            background: rgba(44, 62, 80, 0.95);
            backdrop-filter: blur(10px);
        }

        .nav-link {
            color: white !important;
            font-weight: 500;
            margin: 0 10px;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--secondary-color) !important;
        }

        .theory-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-top: 4px solid var(--tibetan-gold);
        }

        .theory-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .theory-icon {
            font-size: 3rem;
            color: var(--tibetan-red);
            margin-bottom: 20px;
            text-align: center;
        }

        .section-title {
            position: relative;
            text-align: center;
            margin-bottom: 50px;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: var(--tibetan-gold);
        }

        .three-humors {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .humor-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .humor-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
        }

        .humor-card.wind::before { background: #87CEEB; }
        .humor-card.bile::before { background: #FFD700; }
        .humor-card.phlegm::before { background: #90EE90; }

        .humor-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .humor-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        .humor-card.wind .humor-icon { color: #4682B4; }
        .humor-card.bile .humor-icon { color: #DAA520; }
        .humor-card.phlegm .humor-icon { color: #228B22; }

        .diagnosis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .diagnosis-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            border-left: 4px solid var(--tibetan-red);
        }

        .diagnosis-card:hover {
            transform: translateY(-5px);
        }

        .medicine-wheel {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: conic-gradient(
                from 0deg,
                var(--tibetan-red) 0deg 120deg,
                var(--tibetan-gold) 120deg 240deg,
                var(--info-color) 240deg 360deg
            );
            margin: 40px auto;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .wheel-center {
            width: 150px;
            height: 150px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .principle-box {
            background: var(--light-bg);
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
            border-left: 4px solid var(--tibetan-gold);
        }

        .treatment-methods {
            background: linear-gradient(135deg, var(--light-bg), #f0f8ff);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
        }

        .method-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }

        .method-item:last-child {
            border-bottom: none;
        }

        .method-icon {
            color: var(--tibetan-red);
            font-size: 1.5rem;
            margin-right: 15px;
            width: 30px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .page-header {
                padding: 100px 0 60px;
            }
            .theory-card {
                padding: 20px;
            }
            .medicine-wheel {
                width: 250px;
                height: 250px;
            }
            .wheel-center {
                width: 120px;
                height: 120px;
            }
        }
    </style>
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html" style="color: var(--secondary-color);">
                <i class="fas fa-lotus me-2"></i>佛教身心療癒
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">主頁</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">關於我們</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            課程
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses-goals.html">課程目標</a></li>
                            <li><a class="dropdown-item" href="courses-weekly.html">每週主題</a></li>
                            <li><a class="dropdown-item" href="courses-methods.html">進行方式</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            經文選讀
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="scriptures-search.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="scriptures-methods.html">療癒方法</a></li>
                            <li><a class="dropdown-item" href="scriptures-theory.html">療癒理論</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            身心療癒研究
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="research-search.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="research-theory.html">療癒理論</a></li>
                            <li><a class="dropdown-item" href="research-methods.html">療癒方法</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown">
                            藏傳佛教
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="tibetan-theory.html">藏醫理論</a></li>
                            <li><a class="dropdown-item" href="tibetan-practice.html">藏醫實習</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            海外實習
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="overseas-members.html">成員</a></li>
                            <li><a class="dropdown-item" href="overseas-research.html">研究成果</a></li>
                            <li><a class="dropdown-item" href="overseas-future.html">未來計畫</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="workshop.html">工作坊</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="membership.html">加入會員</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 頁面標題 -->
    <section class="page-header">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-4">藏醫理論</h1>
                    <p class="lead">探索千年藏醫智慧，理解身心療癒的深層原理</p>
                </div>
            </div>
        </div>
    </section>

    <main>
        <!-- 三根理論 -->
        <section class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 class="section-title h1 fw-bold">三根理論 (Three Humors)</h2>
                        <p class="text-center lead mb-5">藏醫認為人體由風、膽、涎三根組成，三者平衡則健康，失衡則產生疾病</p>
                    </div>
                </div>

                <div class="three-humors">
                    <div class="humor-card wind">
                        <div class="humor-icon">
                            <i class="fas fa-wind"></i>
                        </div>
                        <h4 class="fw-bold mb-3">風 (rLung)</h4>
                        <p class="mb-4">掌管運動、呼吸、神經系統與心理活動</p>
                        
                        <div class="text-start">
                            <h6 class="fw-bold">主要功能：</h6>
                            <ul class="mb-3">
                                <li>控制呼吸與心跳</li>
                                <li>調節神經傳導</li>
                                <li>影響情緒與思維</li>
                                <li>促進血液循環</li>
                            </ul>
                            
                            <h6 class="fw-bold">失衡症狀：</h6>
                            <ul class="mb-0">
                                <li>焦慮、失眠</li>
                                <li>心律不整</li>
                                <li>消化不良</li>
                                <li>關節疼痛</li>
                            </ul>
                        </div>
                    </div>

                    <div class="humor-card bile">
                        <div class="humor-icon">
                            <i class="fas fa-fire"></i>
                        </div>
                        <h4 class="fw-bold mb-3">膽 (mKhris-pa)</h4>
                        <p class="mb-4">掌管消化、體溫、勇氣與智慧</p>
                        
                        <div class="text-start">
                            <h6 class="fw-bold">主要功能：</h6>
                            <ul class="mb-3">
                                <li>促進消化吸收</li>
                                <li>調節體溫</li>
                                <li>增強勇氣決斷</li>
                                <li>維持膚色光澤</li>
                            </ul>
                            
                            <h6 class="fw-bold">失衡症狀：</h6>
                            <ul class="mb-0">
                                <li>消化性潰瘍</li>
                                <li>發熱發炎</li>
                                <li>易怒暴躁</li>
                                <li>皮膚病變</li>
                            </ul>
                        </div>
                    </div>

                    <div class="humor-card phlegm">
                        <div class="humor-icon">
                            <i class="fas fa-tint"></i>
                        </div>
                        <h4 class="fw-bold mb-3">涎 (Bad-kan)</h4>
                        <p class="mb-4">掌管體液、免疫、穩定與耐力</p>
                        
                        <div class="text-start">
                            <h6 class="fw-bold">主要功能：</h6>
                            <ul class="mb-3">
                                <li>維持體液平衡</li>
                                <li>增強免疫功能</li>
                                <li>保持情緒穩定</li>
                                <li>滋潤關節組織</li>
                            </ul>
                            
                            <h6 class="fw-bold">失衡症狀：</h6>
                            <ul class="mb-0">
                                <li>水腫腫脹</li>
                                <li>消化遲緩</li>
                                <li>倦怠嗜睡</li>
                                <li>關節僵硬</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 三根平衡圖 -->
                <div class="text-center">
                    <div class="medicine-wheel">
                        <div class="wheel-center">
                            <div>
                                <i class="fas fa-yin-yang fa-2x text-primary mb-2"></i><br>
                                <strong>三根平衡</strong>
                            </div>
                        </div>
                    </div>
                    <p class="text-muted">藏醫治療的核心在於恢復三根的動態平衡</p>
                </div>
            </div>
        </section>

        <!-- 診斷方法 -->
        <section class="py-5" style="background: var(--light-bg);">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 class="section-title h1 fw-bold">藏醫診斷法</h2>
                    </div>
                </div>

                <div class="diagnosis-grid">
                    <div class="diagnosis-card">
                        <h5 class="fw-bold mb-3">
                            <i class="fas fa-eye text-primary me-2"></i>望診
                        </h5>
                        <p>觀察患者的精神狀態、面色、舌象、尿液等外在表現，判斷體質與病症。</p>
                        <ul class="small">
                            <li>面部氣色變化</li>
                            <li>舌苔厚薄顏色</li>
                            <li>尿液澄濁程度</li>
                            <li>整體精神狀態</li>
                        </ul>
                    </div>
                    
                    <div class="diagnosis-card">
                        <h5 class="fw-bold mb-3">
                            <i class="fas fa-hand-paper text-success me-2"></i>觸診
                        </h5>
                        <p>透過脈象診斷，感受三根的狀態。藏醫脈診具有獨特的技法與理論體系。</p>
                        <ul class="small">
                            <li>脈象的深淺快慢</li>
                            <li>脈力的強弱程度</li>
                            <li>脈形的粗細變化</li>
                            <li>左右手脈象差異</li>
                        </ul>
                    </div>
                    
                    <div class="diagnosis-card">
                        <h5 class="fw-bold mb-3">
                            <i class="fas fa-comments text-info me-2"></i>問診
                        </h5>
                        <p>詳細詢問病史、症狀、生活習慣、飲食起居等，了解病因與病機。</p>
                        <ul class="small">
                            <li>症狀發生時間</li>
                            <li>疼痛性質位置</li>
                            <li>飲食喜好習慣</li>
                            <li>情緒心理狀態</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 治療原理 -->
        <section class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 class="section-title h1 fw-bold">治療原理與方法</h2>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-6">
                        <div class="theory-card">
                            <div class="theory-icon">
                                <i class="fas fa-balance-scale"></i>
                            </div>
                            <h4 class="fw-bold text-center mb-4">整體平衡療法</h4>
                            
                            <div class="principle-box">
                                <h6 class="fw-bold">核心原理</h6>
                                <p>藏醫認為疾病源於三根失衡，治療重點在於恢復平衡，而非單純消除症狀。</p>
                            </div>

                            <div class="principle-box">
                                <h6 class="fw-bold">治療策略</h6>
                                <ul class="mb-0">
                                    <li>調理過盛的體液</li>
                                    <li>補充不足的能量</li>
                                    <li>疏通阻滞的經絡</li>
                                    <li>淨化體內毒素</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="theory-card">
                            <div class="theory-icon">
                                <i class="fas fa-leaf"></i>
                            </div>
                            <h4 class="fw-bold text-center mb-4">天然療法</h4>
                            
                            <div class="treatment-methods">
                                <div class="method-item">
                                    <div class="method-icon">
                                        <i class="fas fa-pills"></i>
                                    </div>
                                    <div>
                                        <strong>藥物療法</strong><br>
                                        <small>使用天然草藥、礦物配製的藏藥</small>
                                    </div>
                                </div>
                                
                                <div class="method-item">
                                    <div class="method-icon">
                                        <i class="fas fa-utensils"></i>
                                    </div>
                                    <div>
                                        <strong>飲食療法</strong><br>
                                        <small>根據體質調整飲食，以食物性味調理三根</small>
                                    </div>
                                </div>
                                
                                <div class="method-item">
                                    <div class="method-icon">
                                        <i class="fas fa-spa"></i>
                                    </div>
                                    <div>
                                        <strong>起居療法</strong><br>
                                        <small>調整作息時間，配合季節變化的生活方式</small>
                                    </div>
                                </div>
                                
                                <div class="method-item">
                                    <div class="method-icon">
                                        <i class="fas fa-hands"></i>
                                    </div>
                                    <div>
                                        <strong>外治療法</strong><br>
                                        <small>包括按摩、針灸、放血、熱敷等外用方法</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 現代應用 -->
        <section class="py-5" style="background: var(--light-bg);">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 class="section-title h1 fw-bold">現代醫學應用</h2>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="theory-card">
                            <h4 class="fw-bold text-center mb-4">藏醫與現代整合醫學</h4>
                            
                            <div class="principle-box">
                                <h6 class="fw-bold">科學驗證</h6>
                                <p>現代研究證實，藏醫的三根理論與神經內分泌免疫網路理論有相似之處，許多藏藥的療效也得到了科學驗證。</p>
                            </div>

                            <div class="principle-box">
                                <h6 class="fw-bold">臨床應用</h6>
                                <p>在心理健康領域，藏醫的整體觀念與現代身心醫學相呼應，特別是在壓力管理、情緒調節方面有獨特優勢。</p>
                            </div>

                            <div class="principle-box">
                                <h6 class="fw-bold">未來發展</h6>
                                <p class="mb-0">結合現代科技與藏醫傳統，開發個人化的健康管理方案，為現代人提供更全面的身心療癒服務。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // 導航欄滾動效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar-custom');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // 卡片動畫
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.theory-card, .humor-card, .diagnosis-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = `all 0.6s ease ${index * 0.1}s`;
                observer.observe(card);
            });

            // 藥輪旋轉效果
            const wheel = document.querySelector('.medicine-wheel');
            if (wheel) {
                wheel.addEventListener('mouseenter', function() {
                    this.style.transform = 'rotate(120deg)';
                    this.style.transition = 'transform 2s ease';
                });
                
                wheel.addEventListener('mouseleave', function() {
                    this.style.transform = 'rotate(0deg)';
                });
            }
        });
    </script>
</body>
</html>