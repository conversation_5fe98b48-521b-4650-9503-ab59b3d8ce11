<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>佛學文章列表</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.6;
            color: #333;
            background: transparent;
            padding: 20px;
        }

        /* 文章網格 */
        .articles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .articles-list {
            display: none;
            flex-direction: column;
            gap: 15px;
        }

        /* 網格視圖 */
        .article-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .article-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .article-header {
            background: linear-gradient(135deg, #c9a876, #b8966d);
            color: white;
            padding: 20px;
            position: relative;
        }

        .article-category {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255,255,255,0.2);
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        .article-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .article-title {
            font-size: 1.2rem;
            font-weight: 600;
            line-height: 1.4;
            margin-bottom: 8px;
        }

        .article-difficulty {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .article-body {
            padding: 20px;
        }

        .article-excerpt {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.6;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .article-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-bottom: 15px;
        }

        .article-tag {
            background: #f8f9fa;
            color: #666;
            padding: 3px 8px;
            border-radius: 8px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .article-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: #999;
            border-top: 1px solid #f0f0f0;
            padding-top: 15px;
        }

        .article-date {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .article-stats {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 3px;
        }

        /* 列表視圖 */
        .article-item {
            background: white;
            border-radius: 10px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .article-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .item-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #c9a876, #b8966d);
            color: white;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            flex-shrink: 0;
        }

        .item-content {
            flex: 1;
        }

        .item-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .item-excerpt {
            color: #666;
            font-size: 0.85rem;
            line-height: 1.5;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .item-meta {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 0.75rem;
            color: #999;
        }

        .item-category {
            background: #c9a876;
            color: white;
            padding: 2px 6px;
            border-radius: 6px;
            font-size: 0.7rem;
        }

        /* 空狀態 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-icon {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 20px;
        }

        .empty-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .empty-description {
            color: #999;
            line-height: 1.6;
        }

        /* 載入動畫 */
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading-spinner {
            width: 30px;
            height: 30px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #c9a876;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            .articles-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .article-item {
                flex-direction: column;
                text-align: center;
            }

            .item-icon {
                width: 50px;
                height: 50px;
            }
        }

        @media (max-width: 480px) {
            .article-header,
            .article-body {
                padding: 15px;
            }

            .article-item {
                padding: 15px;
            }
        }

        /* 分頁 */
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
            padding: 20px;
        }

        .page-btn {
            background: white;
            border: 1px solid #e9ecef;
            color: #666;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .page-btn:hover {
            background: #c9a876;
            color: white;
            border-color: #c9a876;
        }

        .page-btn.active {
            background: #c9a876;
            color: white;
            border-color: #c9a876;
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <!-- 載入狀態 -->
    <div class="loading" id="loading">
        <div class="loading-spinner"></div>
        <div>載入中...</div>
    </div>

    <!-- 文章網格視圖 -->
    <div class="articles-grid" id="articles-grid">
        <!-- 文章卡片將通過 JavaScript 動態生成 -->
    </div>

    <!-- 文章列表視圖 -->
    <div class="articles-list" id="articles-list">
        <!-- 文章列表將通過 JavaScript 動態生成 -->
    </div>

    <!-- 空狀態 -->
    <div class="empty-state" id="empty-state" style="display: none;">
        <div class="empty-icon">
            <i class="fas fa-search"></i>
        </div>
        <h3 class="empty-title">沒有找到相關文章</h3>
        <p class="empty-description">請嘗試調整搜索條件或瀏覽其他分類</p>
    </div>

    <!-- 分頁 -->
    <div class="pagination" id="pagination" style="display: none;">
        <!-- 分頁按鈕將動態生成 -->
    </div>

    <script>
        // 文章數據（從原 dharma.html 複製）
        const articlesData = [
            {
                id: 1,
                title: "藏傳佛教中的慈悲修持法門",
                excerpt: "慈悲心是藏傳佛教修行的核心。本文深入探討如何在日常生活中培養真正的慈悲心，包括四無量心的修持方法、慈悲禪修的具體步驟，以及如何將慈悲心運用到人際關係中。",
                category: "practice",
                categoryName: "修行方法",
                difficulty: "beginner",
                difficultyName: "入門",
                tags: ["慈悲修持", "四無量心", "禪修"],
                date: "2025-01-10",
                views: 1245,
                likes: 89,
                icon: "fas fa-heart"
            },
            {
                id: 2,
                title: "中觀派的空性哲學解析",
                excerpt: "空性是大乘佛教的核心思想，中觀派對此有深刻的闡述。本文從龍樹菩薩的根本思想出發，解釋緣起性空的深義，並探討如何在修行中體悟空性。",
                category: "philosophy",
                categoryName: "佛教哲學",
                difficulty: "advanced",
                difficultyName: "高級",
                tags: ["空性教法", "中觀哲學", "緣起"],
                date: "2025-01-08",
                views: 892,
                likes: 67,
                icon: "fas fa-circle-notch"
            },
            {
                id: 3,
                title: "菩提心的培養與實修",
                excerpt: "菩提心是成佛的種子，是大乘修行的根本動機。文章詳細介紹了世俗菩提心和勝義菩提心的區別，以及七重因果教授和自他相換的修持方法。",
                category: "practice",
                categoryName: "修行方法",
                difficulty: "intermediate",
                difficultyName: "進階",
                tags: ["菩提心", "七重因果", "自他相換"],
                date: "2025-01-05",
                views: 1567,
                likes: 134,
                icon: "fas fa-seedling"
            },
            {
                id: 4,
                title: "密宗金剛乘的修行次第",
                excerpt: "金剛乘是藏傳佛教的特色，具有完整的修行體系。本文介紹了密宗修行的四個次第：事部、行部、瑜伽部和無上瑜伽部，以及各自的特點和修持方法。",
                category: "practice",
                categoryName: "修行方法",
                difficulty: "advanced",
                difficultyName: "高級",
                tags: ["金剛乘", "密宗修行", "四部密續"],
                date: "2025-01-03",
                views: 743,
                likes: 56,
                icon: "fas fa-gem"
            },
            {
                id: 5,
                title: "正念覺察在日常生活中的應用",
                excerpt: "正念是佛教修行的基礎，也是現代心理治療的重要方法。文章探討如何將佛教的正念修持融入現代生活，包括正念飲食、正念行走、正念工作等實用技巧。",
                category: "meditation",
                categoryName: "禪修指導",
                difficulty: "beginner",
                difficultyName: "入門",
                tags: ["正念覺察", "日常修行", "生活禪"],
                date: "2025-01-01",
                views: 2134,
                likes: 178,
                icon: "fas fa-eye"
            },
            {
                id: 6,
                title: "《菩提道次第廣論》修學指導",
                excerpt: "宗喀巴大師的《菩提道次第廣論》是藏傳佛教格魯派的根本典籍。本文提供了系統學習此論的方法，包括三士道的修學次第和關鍵要點的把握。",
                category: "sutras",
                categoryName: "經典解讀",
                difficulty: "intermediate",
                difficultyName: "進階",
                tags: ["菩提道次第", "三士道", "宗喀巴"],
                date: "2024-12-28",
                views: 1876,
                likes: 145,
                icon: "fas fa-book"
            }
            // 可以繼續添加更多文章...
        ];

        // 全局變量
        let filteredArticles = [...articlesData];
        let currentView = 'grid';
        let currentPage = 1;
        const articlesPerPage = 9;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            parseUrlParams();
            renderArticles();
            hideLoading();
            
            // 向父頁面發送計數更新
            updateParentCounts();
        });

        // 解析 URL 參數
        function parseUrlParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const category = urlParams.get('category');
            const difficulty = urlParams.get('difficulty');
            const search = urlParams.get('search');
            const view = urlParams.get('view');

            if (view) {
                currentView = view;
            }

            // 應用篩選
            filteredArticles = articlesData.filter(article => {
                let match = true;
                
                if (category && category !== 'all') {
                    match = match && article.category === category;
                }
                
                if (difficulty) {
                    match = match && article.difficulty === difficulty;
                }
                
                if (search) {
                    const searchLower = search.toLowerCase();
                    match = match && (
                        article.title.toLowerCase().includes(searchLower) ||
                        article.excerpt.toLowerCase().includes(searchLower) ||
                        article.tags.some(tag => tag.toLowerCase().includes(searchLower))
                    );
                }
                
                return match;
            });
        }

        // 渲染文章
        function renderArticles() {
            const startIndex = (currentPage - 1) * articlesPerPage;
            const endIndex = startIndex + articlesPerPage;
            const pageArticles = filteredArticles.slice(startIndex, endIndex);

            if (pageArticles.length === 0) {
                showEmptyState();
                return;
            }

            hideEmptyState();

            if (currentView === 'grid') {
                renderGridView(pageArticles);
            } else {
                renderListView(pageArticles);
            }

            renderPagination();
        }

        // 渲染網格視圖
        function renderGridView(articles) {
            const grid = document.getElementById('articles-grid');
            const list = document.getElementById('articles-list');
            
            grid.style.display = 'grid';
            list.style.display = 'none';

            grid.innerHTML = articles.map(article => `
                <article class="article-card" data-article-id="${article.id}">
                    <div class="article-header">
                        <div class="article-category">${article.categoryName}</div>
                        <div class="article-icon">
                            <i class="${article.icon}"></i>
                        </div>
                        <h3 class="article-title">${article.title}</h3>
                        <div class="article-difficulty">${article.difficultyName}</div>
                    </div>
                    <div class="article-body">
                        <p class="article-excerpt">${article.excerpt}</p>
                        <div class="article-tags">
                            ${article.tags.map(tag => `<span class="article-tag">${tag}</span>`).join('')}
                        </div>
                        <div class="article-meta">
                            <div class="article-date">
                                <i class="fas fa-calendar"></i>
                                ${article.date}
                            </div>
                            <div class="article-stats">
                                <div class="stat-item">
                                    <i class="fas fa-eye"></i>
                                    ${article.views}
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-heart"></i>
                                    ${article.likes}
                                </div>
                            </div>
                        </div>
                    </div>
                </article>
            `).join('');
        }

        // 渲染列表視圖
        function renderListView(articles) {
            const grid = document.getElementById('articles-grid');
            const list = document.getElementById('articles-list');
            
            grid.style.display = 'none';
            list.style.display = 'flex';

            list.innerHTML = articles.map(article => `
                <article class="article-item" data-article-id="${article.id}">
                    <div class="item-icon">
                        <i class="${article.icon}"></i>
                    </div>
                    <div class="item-content">
                        <h3 class="item-title">${article.title}</h3>
                        <p class="item-excerpt">${article.excerpt}</p>
                        <div class="item-meta">
                            <span class="item-category">${article.categoryName}</span>
                            <span>${article.difficultyName}</span>
                            <span>${article.date}</span>
                            <span><i class="fas fa-eye"></i> ${article.views}</span>
                            <span><i class="fas fa-heart"></i> ${article.likes}</span>
                        </div>
                    </div>
                </article>
            `).join('');
        }

        // 渲染分頁
        function renderPagination() {
            const totalPages = Math.ceil(filteredArticles.length / articlesPerPage);
            const pagination = document.getElementById('pagination');

            if (totalPages <= 1) {
                pagination.style.display = 'none';
                return;
            }

            pagination.style.display = 'flex';

            let paginationHTML = '';

            // 上一頁
            paginationHTML += `
                <button class="page-btn" ${currentPage === 1 ? 'disabled' : ''} onclick="changePage(${currentPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;

            // 頁數按鈕
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    paginationHTML += `
                        <button class="page-btn ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                            ${i}
                        </button>
                    `;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    paginationHTML += '<span class="page-btn" style="border: none; cursor: default;">...</span>';
                }
            }

            // 下一頁
            paginationHTML += `
                <button class="page-btn" ${currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${currentPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;

            pagination.innerHTML = paginationHTML;
        }

        // 更改頁面
        function changePage(page) {
            if (page < 1 || page > Math.ceil(filteredArticles.length / articlesPerPage)) return;
            
            currentPage = page;
            renderArticles();
            
            // 滾動到頂部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // 顯示空狀態
        function showEmptyState() {
            document.getElementById('articles-grid').style.display = 'none';
            document.getElementById('articles-list').style.display = 'none';
            document.getElementById('empty-state').style.display = 'block';
            document.getElementById('pagination').style.display = 'none';
        }

        // 隱藏空狀態
        function hideEmptyState() {
            document.getElementById('empty-state').style.display = 'none';
        }

        // 隱藏載入動畫
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        // 文章點擊事件
        document.addEventListener('click', function(e) {
            const articleElement = e.target.closest('[data-article-id]');
            if (articleElement) {
                const articleId = articleElement.getAttribute('data-article-id');
                
                // 向父頁面發送消息
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'article_view',
                        articleId: articleId
                    }, '*');
                }
            }
        });

        // 監聽來自父頁面的消息
        window.addEventListener('message', function(event) {
            const data = event.data;
            
            if (data.type === 'change_view') {
                currentView = data.view;
                renderArticles();
            }
        });

        // 更新父頁面計數
        function updateParentCounts() {
            const counts = {};
            
            // 計算各分類文章數量
            articlesData.forEach(article => {
                counts[article.category] = (counts[article.category] || 0) + 1;
            });
            
            counts.all = articlesData.length;
            
            // 向父頁面發送更新
            if (window.parent !== window) {
                window.parent.postMessage({
                    type: 'update_counts',
                    counts: counts
                }, '*');
            }
        }
    </script>
</body>
</html>