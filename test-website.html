<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>澄源閱讀 - 網站功能測試</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .test-section {
            margin: 20px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        .test-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #667eea;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .test-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }

        .test-item h4 {
            margin-bottom: 10px;
            color: #333;
        }

        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            margin-right: 10px;
            margin-bottom: 5px;
        }

        .test-button:hover {
            background: #5a6fd8;
        }

        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .iframe-container {
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }

        .iframe-test {
            width: 100%;
            height: 400px;
            border: none;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-pending { background: #6c757d; }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .logs {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 0;
            }
            
            .test-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>澄源閱讀 - 網站功能測試</h1>
            <p>檢查網站各項功能是否正常運作</p>
        </div>

        <!-- 頁面載入測試 -->
        <div class="test-section">
            <h3 class="test-title">📄 頁面載入測試</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>主要頁面</h4>
                    <button class="test-button" onclick="testPage('index.html')">測試主頁</button>
                    <button class="test-button" onclick="testPage('pages/login.html')">測試登錄頁</button>
                    <button class="test-button" onclick="testPage('pages/home.html')">測試首頁</button>
                    <div id="page-test-result" class="test-result" style="display: none;"></div>
                </div>
                
                <div class="test-item">
                    <h4>佛學智慧頁面</h4>
                    <button class="test-button" onclick="testPage('pages/dharma.html')">測試佛學主頁</button>
                    <button class="test-button" onclick="testPage('pages/dharma-sutras.html')">測試經典解讀</button>
                    <button class="test-button" onclick="testPage('pages/dharma-masters.html')">測試大師教導</button>
                    <button class="test-button" onclick="testPage('pages/dharma-stories.html')">測試佛教故事</button>
                    <div id="dharma-test-result" class="test-result" style="display: none;"></div>
                </div>
                
                <div class="test-item">
                    <h4>其他功能頁面</h4>
                    <button class="test-button" onclick="testPage('pages/healing.html')">測試身心療癒</button>
                    <button class="test-button" onclick="testPage('pages/research.html')">測試最新研究</button>
                    <button class="test-button" onclick="testPage('pages/search.html')">測試搜索頁</button>
                    <button class="test-button" onclick="testPage('pages/favorites.html')">測試收藏頁</button>
                    <div id="other-test-result" class="test-result" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- 響應式設計測試 -->
        <div class="test-section">
            <h3 class="test-title">📱 響應式設計測試</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>視口測試</h4>
                    <button class="test-button" onclick="testResponsive('mobile')">手機視圖 (375px)</button>
                    <button class="test-button" onclick="testResponsive('tablet')">平板視圖 (768px)</button>
                    <button class="test-button" onclick="testResponsive('desktop')">桌面視圖 (1200px)</button>
                    <div id="responsive-test-result" class="test-result" style="display: none;"></div>
                </div>
                
                <div class="test-item">
                    <h4>CSS 載入檢查</h4>
                    <button class="test-button" onclick="testCSS()">檢查樣式表</button>
                    <div id="css-test-result" class="test-result" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- JavaScript 功能測試 -->
        <div class="test-section">
            <h3 class="test-title">⚡ JavaScript 功能測試</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>核心腳本</h4>
                    <button class="test-button" onclick="testJS()">測試 JavaScript 載入</button>
                    <button class="test-button" onclick="testLocalStorage()">測試本地存儲</button>
                    <button class="test-button" onclick="testNavigation()">測試導航功能</button>
                    <div id="js-test-result" class="test-result" style="display: none;"></div>
                </div>
                
                <div class="test-item">
                    <h4>API 連接測試</h4>
                    <button class="test-button" onclick="testAPI()">測試 API 連接</button>
                    <button class="test-button" onclick="testDatabase()">測試數據庫連接</button>
                    <div id="api-test-result" class="test-result" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- 頁面預覽 -->
        <div class="test-section">
            <h3 class="test-title">👁️ 頁面預覽</h3>
            <div class="test-item">
                <h4>當前測試頁面：<span id="current-page">無</span></h4>
                <div class="iframe-container">
                    <iframe id="test-iframe" class="iframe-test" src="about:blank"></iframe>
                </div>
            </div>
        </div>

        <!-- 測試日誌 -->
        <div class="test-section">
            <h3 class="test-title">📋 測試日誌</h3>
            <div class="progress-bar">
                <div id="progress-fill" class="progress-fill"></div>
            </div>
            <div id="test-logs" class="logs">
測試日誌將在這裡顯示...
點擊上方按鈕開始測試
            </div>
        </div>

        <!-- 全體測試 -->
        <div class="test-section">
            <h3 class="test-title">🚀 全體測試</h3>
            <div class="test-item">
                <button class="test-button" onclick="runAllTests()" style="background: #28a745; font-size: 1rem; padding: 12px 24px;">
                    運行所有測試
                </button>
                <button class="test-button" onclick="clearLogs()" style="background: #6c757d;">清除日誌</button>
                <div id="overall-result" class="test-result" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        let testCount = 0;
        let passedTests = 0;
        let failedTests = 0;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logs = document.getElementById('test-logs');
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logs.textContent += `${timestamp} ${prefix} ${message}\n`;
            logs.scrollTop = logs.scrollHeight;
        }

        function updateProgress() {
            const total = testCount;
            const completed = passedTests + failedTests;
            const percentage = total > 0 ? (completed / total) * 100 : 0;
            document.getElementById('progress-fill').style.width = percentage + '%';
        }

        function showResult(elementId, success, message) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `test-result ${success ? 'success' : 'error'}`;
            element.innerHTML = `<span class="status-indicator status-${success ? 'success' : 'error'}"></span>${message}`;
            
            if (success) {
                passedTests++;
                log(message, 'success');
            } else {
                failedTests++;
                log(message, 'error');
            }
            updateProgress();
        }

        async function testPage(pagePath) {
            testCount++;
            log(`開始測試頁面: ${pagePath}`);
            
            try {
                const response = await fetch(pagePath);
                if (response.ok) {
                    const content = await response.text();
                    
                    // 檢查HTML結構
                    const hasDoctype = content.includes('<!DOCTYPE html>');
                    const hasTitle = content.includes('<title>');
                    const hasCharset = content.includes('charset=');
                    
                    if (hasDoctype && hasTitle && hasCharset) {
                        showResult('page-test-result', true, `頁面 ${pagePath} 載入成功並通過基本檢查`);
                        
                        // 在iframe中預覽
                        document.getElementById('test-iframe').src = pagePath;
                        document.getElementById('current-page').textContent = pagePath;
                    } else {
                        showResult('page-test-result', false, `頁面 ${pagePath} 缺少必要的HTML元素`);
                    }
                } else {
                    showResult('page-test-result', false, `頁面 ${pagePath} 載入失敗: ${response.status}`);
                }
            } catch (error) {
                showResult('page-test-result', false, `頁面 ${pagePath} 測試出錯: ${error.message}`);
            }
        }

        function testResponsive(size) {
            testCount++;
            log(`測試響應式設計: ${size}`);
            
            const iframe = document.getElementById('test-iframe');
            const container = iframe.parentElement;
            
            switch(size) {
                case 'mobile':
                    container.style.width = '375px';
                    container.style.margin = '0 auto';
                    break;
                case 'tablet':
                    container.style.width = '768px';
                    container.style.margin = '0 auto';
                    break;
                case 'desktop':
                    container.style.width = '100%';
                    container.style.margin = '0';
                    break;
            }
            
            setTimeout(() => {
                showResult('responsive-test-result', true, `${size} 視圖測試完成`);
            }, 500);
        }

        function testCSS() {
            testCount++;
            log('檢查CSS樣式表載入狀態');
            
            const stylesheets = ['css/main.css', 'css/components.css', 'css/responsive.css'];
            let loadedCount = 0;
            
            stylesheets.forEach(async (css) => {
                try {
                    const response = await fetch(css);
                    if (response.ok) {
                        loadedCount++;
                        log(`✅ ${css} 載入成功`);
                    } else {
                        log(`❌ ${css} 載入失敗: ${response.status}`, 'error');
                    }
                } catch (error) {
                    log(`❌ ${css} 測試出錯: ${error.message}`, 'error');
                }
                
                if (loadedCount === stylesheets.length) {
                    showResult('css-test-result', true, `所有樣式表 (${loadedCount}/${stylesheets.length}) 載入成功`);
                } else if (loadedCount > 0) {
                    showResult('css-test-result', false, `部分樣式表載入失敗 (${loadedCount}/${stylesheets.length})`);
                }
            });
        }

        function testJS() {
            testCount++;
            log('測試JavaScript功能');
            
            const scripts = ['js/main.js', 'js/auth.js', 'js/utils.js'];
            let results = [];
            
            scripts.forEach(async (script) => {
                try {
                    const response = await fetch(script);
                    if (response.ok) {
                        results.push(`✅ ${script}`);
                    } else {
                        results.push(`❌ ${script} (${response.status})`);
                    }
                } catch (error) {
                    results.push(`❌ ${script} (錯誤)`);
                }
                
                if (results.length === scripts.length) {
                    const successCount = results.filter(r => r.includes('✅')).length;
                    showResult('js-test-result', successCount === scripts.length, 
                        `JavaScript文件檢查完成 (${successCount}/${scripts.length})`);
                }
            });
        }

        function testLocalStorage() {
            testCount++;
            log('測試本地存儲功能');
            
            try {
                // 測試localStorage
                const testKey = 'chengyuan_test';
                const testValue = { timestamp: Date.now(), test: true };
                
                localStorage.setItem(testKey, JSON.stringify(testValue));
                const retrieved = JSON.parse(localStorage.getItem(testKey));
                
                if (retrieved && retrieved.test === true) {
                    localStorage.removeItem(testKey);
                    showResult('js-test-result', true, '本地存儲功能正常');
                } else {
                    showResult('js-test-result', false, '本地存儲數據驗證失敗');
                }
            } catch (error) {
                showResult('js-test-result', false, `本地存儲測試失敗: ${error.message}`);
            }
        }

        function testNavigation() {
            testCount++;
            log('測試導航功能');
            
            // 模擬導航測試
            const hasHistory = typeof window.history !== 'undefined';
            const hasLocation = typeof window.location !== 'undefined';
            const hasPostMessage = typeof window.postMessage !== 'undefined';
            
            if (hasHistory && hasLocation && hasPostMessage) {
                showResult('js-test-result', true, '導航相關API可用');
            } else {
                showResult('js-test-result', false, '導航相關API不完整');
            }
        }

        async function testAPI() {
            testCount++;
            log('測試API連接');
            
            // 這裡應該測試實際的API端點
            // 由於是靜態測試，我們模擬API測試
            try {
                // 測試functions目錄下的文件是否存在
                const apiFiles = ['functions/index.js', 'functions/articles.js', 'functions/search.js'];
                let apiCount = 0;
                
                for (const file of apiFiles) {
                    try {
                        const response = await fetch(file);
                        if (response.ok || response.status === 404) {
                            apiCount++;
                        }
                    } catch (error) {
                        log(`API文件 ${file} 檢查失敗`, 'warning');
                    }
                }
                
                showResult('api-test-result', apiCount > 0, 
                    `API文件檢查完成 (${apiCount}/${apiFiles.length})`);
            } catch (error) {
                showResult('api-test-result', false, `API測試失敗: ${error.message}`);
            }
        }

        async function testDatabase() {
            testCount++;
            log('測試數據庫配置');
            
            try {
                // 檢查數據庫配置文件
                const dbFiles = ['database/schema.sql', 'database/seed.sql', 'wrangler.toml'];
                let dbCount = 0;
                
                for (const file of dbFiles) {
                    try {
                        const response = await fetch(file);
                        if (response.ok) {
                            dbCount++;
                            log(`✅ ${file} 存在`);
                        }
                    } catch (error) {
                        log(`❌ ${file} 不存在或無法訪問`);
                    }
                }
                
                showResult('api-test-result', dbCount >= 2, 
                    `數據庫配置文件檢查 (${dbCount}/${dbFiles.length})`);
            } catch (error) {
                showResult('api-test-result', false, `數據庫測試失敗: ${error.message}`);
            }
        }

        async function runAllTests() {
            log('🚀 開始運行所有測試...', 'info');
            clearResults();
            
            // 重置計數器
            testCount = 0;
            passedTests = 0;
            failedTests = 0;
            
            // 順序執行測試
            await testPage('index.html');
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testPage('pages/dharma.html');
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testPage('pages/dharma-sutras.html');
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testCSS();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testJS();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testLocalStorage();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testDatabase();
            
            // 顯示總結
            setTimeout(() => {
                const total = passedTests + failedTests;
                const percentage = total > 0 ? (passedTests / total * 100).toFixed(1) : 0;
                
                const message = `測試完成！通過: ${passedTests}, 失敗: ${failedTests}, 成功率: ${percentage}%`;
                const success = passedTests > failedTests;
                
                showResult('overall-result', success, message);
                log(`🏁 ${message}`, success ? 'success' : 'error');
            }, 2000);
        }

        function clearResults() {
            const results = document.querySelectorAll('.test-result');
            results.forEach(result => {
                result.style.display = 'none';
            });
        }

        function clearLogs() {
            document.getElementById('test-logs').textContent = '測試日誌已清空...\n';
            document.getElementById('progress-fill').style.width = '0%';
            clearResults();
            testCount = 0;
            passedTests = 0;
            failedTests = 0;
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🌟 網站功能測試工具已載入');
            log('📌 點擊按鈕開始測試各項功能');
        });
    </script>
</body>
</html>