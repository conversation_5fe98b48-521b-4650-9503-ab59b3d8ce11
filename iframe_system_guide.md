# 🏗️ iframe 佛學文章系統架構指南

## 🎯 系統概述

全新的 iframe 架構將佛學內容系統分解為多個獨立的子頁面，提供更好的模組化、擴展性和維護性。

## 📁 檔案結構

```
pages/
├── dharma-iframe.html          # 主框架頁面 (替代原 dharma.html)
├── dharma-articles.html        # 文章列表子頁面
├── dharma-meditation.html      # 禪修分類子頁面
├── dharma-philosophy.html      # 哲學分類子頁面（待創建）
├── dharma-practice.html        # 修行分類子頁面（待創建）
├── dharma-sutras.html          # 經典分類子頁面（待創建）
├── dharma-masters.html         # 大師分類子頁面（待創建）
├── dharma-ethics.html          # 倫理分類子頁面（待創建）
├── dharma-tradition.html       # 傳承分類子頁面（待創建）
├── dharma-stories.html         # 故事分類子頁面（待創建）
├── dharma-beginner.html        # 入門難度子頁面（待創建）
├── dharma-intermediate.html    # 進階難度子頁面（待創建）
├── dharma-advanced.html        # 高級難度子頁面（待創建）
└── dharma-search.html          # 搜索結果子頁面（待創建）
```

## 🏛️ 架構優勢

### ✅ 模組化設計
- 每個分類/功能都是獨立的 HTML 頁面
- 便於單獨開發和維護
- 可以獨立測試和調試

### ✅ 高度可擴展
- 新增分類只需添加對應的子頁面
- 可以為每個分類設計專屬的介面和功能
- 支援無限層級的內容組織

### ✅ 性能優化
- 按需載入內容，減少初始載入時間
- 每個子頁面可以獨立優化
- 支援緩存策略

### ✅ 維護便利
- 修改特定功能不影響其他模組
- 代碼結構清晰，易於理解
- 便於團隊協作開發

## 🚀 如何使用新系統

### 1. 替換主頁面
將導航中的 dharma.html 替換為 dharma-iframe.html：

```javascript
// 在 index.html 或主導航中
data-page="dharma-iframe"  // 替代原來的 dharma
```

### 2. 訪問方式
```html
<!-- 直接開啟主框架 -->
pages/dharma-iframe.html

<!-- 帶參數開啟特定分類 -->
pages/dharma-iframe.html?category=meditation
```

## ➕ 如何新增內容

### 方法一：在現有子頁面添加文章

1. **找到對應的子頁面**
   - 禪修相關：`dharma-meditation.html`
   - 文章列表：`dharma-articles.html`

2. **添加文章數據**
   ```javascript
   // 在對應子頁面的 JavaScript 區域
   const articles = [
       {
           id: 22,  // 新的唯一ID
           title: "您的文章標題",
           excerpt: "文章摘要...",
           category: "meditation",
           difficulty: "beginner",
           // ... 其他欄位
       }
   ];
   ```

### 方法二：創建新的分類子頁面

1. **複製範本**
   ```bash
   # 複製現有子頁面作為範本
   cp dharma-meditation.html dharma-stories.html
   ```

2. **修改內容**
   - 更新頁面標題和介紹
   - 替換相關的文章數據
   - 調整樣式和佈局

3. **更新主框架導航**
   在 `dharma-iframe.html` 中添加新的導航項目：
   ```html
   <li class="nav-item">
       <a class="nav-link" data-category="stories" data-page="dharma-stories">
           <i class="nav-icon fas fa-book-open"></i>
           <span class="nav-text">佛教故事</span>
           <span class="nav-count">5</span>
       </a>
   </li>
   ```

## 🔄 通信機制

### 子頁面向主框架發送消息
```javascript
// 在子頁面中
window.parent.postMessage({
    type: 'navigate',           // 導航請求
    page: 'dharma-philosophy',  // 目標頁面
    options: { category: 'philosophy' }
}, '*');

window.parent.postMessage({
    type: 'article_view',       // 文章查看
    articleId: 123
}, '*');

window.parent.postMessage({
    type: 'update_counts',      // 更新計數
    counts: { meditation: 5, philosophy: 8 }
}, '*');
```

### 主框架向子頁面發送消息
```javascript
// 在主框架中
contentFrame.contentWindow.postMessage({
    type: 'change_view',        // 改變視圖模式
    view: 'list'               // grid 或 list
}, '*');
```

## 🎨 自定義樣式

### 統一主題
所有子頁面使用一致的設計風格：
- 主色調：`#c9a876` (金棕色)
- 輔助色：`#b8966d`
- 背景色：`#f8f6f1`

### 響應式設計
所有子頁面都支援響應式設計：
- 桌面版：多欄佈局
- 平板版：適應性佈局
- 手機版：單欄佈局

## 🔧 開發工具

### 快速創建子頁面範本
```javascript
// 子頁面基本結構範本
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <title>分類名稱 - 佛學智慧</title>
    <!-- 統一的 CSS 引用 -->
</head>
<body>
    <!-- 分類介紹區 -->
    <div class="category-intro">
        <!-- 介紹內容 -->
    </div>
    
    <!-- 內容區域 -->
    <div class="content-area">
        <!-- 具體內容 -->
    </div>
    
    <script>
        // 通信邏輯
        window.addEventListener('message', function(event) {
            // 處理來自主框架的消息
        });
        
        // 文章點擊處理
        document.addEventListener('click', function(e) {
            // 向主框架發送消息
        });
    </script>
</body>
</html>
```

## 📱 移動端支援

系統完全支援移動端使用：
- 響應式側邊欄
- 觸控友好的導航
- 自適應佈局
- 快速載入

## 🔍 SEO 友好

每個子頁面都可以：
- 設置獨立的 meta 標籤
- 優化特定關鍵詞
- 提供結構化數據
- 支援社交媒體分享

## 🚀 部署建議

1. **本地開發**：直接在瀏覽器中打開檔案測試
2. **生產環境**：建議使用 HTTP 服務器部署
3. **CDN 支援**：可以將子頁面分別部署到 CDN
4. **緩存策略**：可以對不同子頁面設置不同的緩存時間

這個 iframe 架構為佛學內容系統提供了強大的擴展能力和維護便利性！