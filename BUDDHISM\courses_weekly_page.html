<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>每週主題 - 佛教身心療癒網站</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2C3E50;
            --secondary-color: #F39C12;
            --accent-color: #E8F4F8;
            --text-color: #2C3E50;
            --light-bg: #F8F9FA;
            --success-color: #27AE60;
            --info-color: #3498DB;
        }

        body {
            font-family: 'Noto Sans CJK TC', 'Microsoft JhengHei', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            padding: 120px 0 80px;
            position: relative;
            overflow: hidden;
        }

        .navbar-custom {
            background: rgba(44, 62, 80, 0.95);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .nav-link {
            color: white !important;
            font-weight: 500;
            margin: 0 10px;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--secondary-color) !important;
        }

        .section-title {
            position: relative;
            text-align: center;
            margin-bottom: 50px;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: var(--secondary-color);
        }

        .week-card {
            background: white;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            border-left: 5px solid var(--secondary-color);
        }

        .week-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .week-header {
            background: linear-gradient(135deg, var(--primary-color), #34495e);
            color: white;
            padding: 25px 30px;
            position: relative;
        }

        .week-number {
            position: absolute;
            top: -15px;
            right: 30px;
            background: var(--secondary-color);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .week-content {
            padding: 30px;
        }

        .week-icon {
            font-size: 2.5rem;
            color: var(--secondary-color);
            margin-bottom: 15px;
        }

        .week-goals {
            background: var(--light-bg);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .week-goals h6 {
            color: var(--primary-color);
            margin-bottom: 15px;
        }

        .goal-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .goal-item i {
            color: var(--success-color);
            margin-right: 10px;
        }

        .resources-section {
            background: var(--light-bg);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .resource-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }

        .resource-item:last-child {
            border-bottom: none;
        }

        .resource-icon {
            color: var(--info-color);
            margin-right: 15px;
            font-size: 1.2rem;
        }

        .filter-tabs {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 40px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .filter-btn {
            background: transparent;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            padding: 10px 25px;
            border-radius: 25px;
            margin: 5px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .filter-btn.active,
        .filter-btn:hover {
            background: var(--primary-color);
            color: white;
        }

        .progress-indicator {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 40px 0;
            position: relative;
        }

        .progress-indicator::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: #ddd;
            z-index: 1;
        }

        .progress-step {
            background: white;
            border: 3px solid #ddd;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 2;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .progress-step.completed {
            background: var(--success-color);
            border-color: var(--success-color);
            color: white;
        }

        .progress-step.current {
            background: var(--secondary-color);
            border-color: var(--secondary-color);
            color: white;
            transform: scale(1.2);
        }

        .month-section {
            margin-bottom: 60px;
        }

        .month-header {
            background: var(--primary-color);
            color: white;
            padding: 20px 30px;
            border-radius: 15px 15px 0 0;
            margin-bottom: 0;
        }

        .month-content {
            background: white;
            border-radius: 0 0 15px 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        @media (max-width: 768px) {
            .page-header {
                padding: 100px 0 60px;
            }
            
            .week-content {
                padding: 20px;
            }
            
            .filter-btn {
                padding: 8px 15px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html" style="color: var(--secondary-color);">
                <i class="fas fa-lotus me-2"></i>佛教身心療癒
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">主頁</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">關於我們</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown">
                            課程
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses-goals.html">課程目標</a></li>
                            <li><a class="dropdown-item active" href="courses-weekly.html">每週主題</a></li>
                            <li><a class="dropdown-item" href="courses-methods.html">進行方式</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            經文選讀
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="scriptures-search.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="scriptures-methods.html">療癒方法</a></li>
                            <li><a class="dropdown-item" href="scriptures-theory.html">療癒理論</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            身心療癒研究
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="research-search.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="research-theory.html">療癒理論</a></li>
                            <li><a class="dropdown-item" href="research-methods.html">療癒方法</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            藏傳佛教
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="tibetan-theory.html">藏醫理論</a></li>
                            <li><a class="dropdown-item" href="tibetan-practice.html">藏醫實習</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            海外實習
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="overseas-members.html">成員</a></li>
                            <li><a class="dropdown-item" href="overseas-research.html">研究成果</a></li>
                            <li><a class="dropdown-item" href="overseas-future.html">未來計畫</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="workshop.html">工作坊</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="membership.html">加入會員</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 頁面標題 -->
    <section class="page-header">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-4">每週主題</h1>
                    <p class="lead">系統性的12個月學習計劃，循序漸進掌握佛教身心療癒精髓</p>
                </div>
            </div>
        </div>
    </section>

    <main>
        <!-- 學習進度指示器 -->
        <section class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 class="section-title h3 fw-bold">學習進度</h2>
                    </div>
                </div>
                <div class="progress-indicator">
                    <div class="progress-step completed" title="第1-3月：基礎入門">1</div>
                    <div class="progress-step current" title="第4-6月：深度學習">2</div>
                    <div class="progress-step" title="第7-9月：實踐應用">3</div>
                    <div class="progress-step" title="第10-12月：專業認證">4</div>
                </div>
                <div class="row text-center mt-3">
                    <div class="col-3">
                        <small class="text-muted">基礎入門</small>
                    </div>
                    <div class="col-3">
                        <small class="text-muted">深度學習</small>
                    </div>
                    <div class="col-3">
                        <small class="text-muted">實踐應用</small>
                    </div>
                    <div class="col-3">
                        <small class="text-muted">專業認證</small>
                    </div>
                </div>
            </div>
        </section>

        <!-- 篩選功能 -->
        <section class="py-3">
            <div class="container">
                <div class="filter-tabs text-center">
                    <h5 class="mb-3">選擇學習階段</h5>
                    <button class="filter-btn active" data-filter="all">全部主題</button>
                    <button class="filter-btn" data-filter="basic">基礎入門 (1-3月)</button>
                    <button class="filter-btn" data-filter="intermediate">深度學習 (4-6月)</button>
                    <button class="filter-btn" data-filter="advanced">實踐應用 (7-9月)</button>
                    <button class="filter-btn" data-filter="expert">專業認證 (10-12月)</button>
                </div>
            </div>
        </section>

        <!-- 第一階段：基礎入門 (1-3月) -->
        <section class="py-5" data-stage="basic">
            <div class="container">
                <div class="month-section">
                    <div class="month-header">
                        <h3 class="mb-0"><i class="fas fa-seedling me-2"></i>第一階段：基礎入門 (1-3月)</h3>
                    </div>
                    <div class="month-content">
                        <div class="row">
                            <!-- 第1週 -->
                            <div class="col-lg-6">
                                <div class="week-card">
                                    <div class="week-header">
                                        <div class="week-number">1</div>
                                        <div class="week-icon">
                                            <i class="fas fa-door-open"></i>
                                        </div>
                                        <h4 class="mb-2">佛教基礎概念入門</h4>
                                        <p class="mb-0">建立正確的佛教世界觀</p>
                                    </div>
                                    <div class="week-content">
                                        <p>了解佛教的基本教義，包括四聖諦、八正道等核心概念，為後續學習建立穩固基礎。</p>
                                        
                                        <div class="week-goals">
                                            <h6><i class="fas fa-target me-2"></i>學習目標</h6>
                                            <div class="goal-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>理解四聖諦的基本內容</span>
                                            </div>
                                            <div class="goal-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>認識八正道的修習方向</span>
                                            </div>
                                            <div class="goal-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>建立正確的學習心態</span>
                                            </div>
                                        </div>

                                        <div class="resources-section">
                                            <h6><i class="fas fa-book me-2"></i>學習資源</h6>
                                            <div class="resource-item">
                                                <i class="fas fa-video resource-icon"></i>
                                                <span>入門導覽影片 (30分鐘)</span>
                                            </div>
                                            <div class="resource-item">
                                                <i class="fas fa-file-pdf resource-icon"></i>
                                                <span>佛教基礎概念手冊</span>
                                            </div>
                                            <div class="resource-item">
                                                <i class="fas fa-headphones resource-icon"></i>
                                                <span>引導冥想音頻</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 第2週 -->
                            <div class="col-lg-6">
                                <div class="week-card">
                                    <div class="week-header">
                                        <div class="week-number">2</div>
                                        <div class="week-icon">
                                            <i class="fas fa-wind"></i>
                                        </div>
                                        <h4 class="mb-2">正念呼吸練習</h4>
                                        <p class="mb-0">掌握基礎正念技巧</p>
                                    </div>
                                    <div class="week-content">
                                        <p>學習正念呼吸的基本方法，培養專注力與覺察力，為後續的冥想練習做準備。</p>
                                        
                                        <div class="week-goals">
                                            <h6><i class="fas fa-target me-2"></i>學習目標</h6>
                                            <div class="goal-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>掌握正念呼吸技巧</span>
                                            </div>
                                            <div class="goal-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>建立日常練習習慣</span>
                                            </div>
                                            <div class="goal-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>體驗覺察的力量</span>
                                            </div>
                                        </div>

                                        <div class="resources-section">
                                            <h6><i class="fas fa-book me-2"></i>學習資源</h6>
                                            <div class="resource-item">
                                                <i class="fas fa-play-circle resource-icon"></i>
                                                <span>呼吸練習指導 (20分鐘)</span>
                                            </div>
                                            <div class="resource-item">
                                                <i class="fas fa-calendar resource-icon"></i>
                                                <span>21天練習計劃表</span>
                                            </div>
                                            <div class="resource-item">
                                                <i class="fas fa-mobile-alt resource-icon"></i>
                                                <span>正念提醒APP</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 第3-4週類似結構... -->
                            <div class="col-lg-6">
                                <div class="week-card">
                                    <div class="week-header">
                                        <div class="week-number">3</div>
                                        <div class="week-icon">
                                            <i class="fas fa-heart"></i>
                                        </div>
                                        <h4 class="mb-2">慈心禪修</h4>
                                        <p class="mb-0">培養慈悲心與愛心</p>
                                    </div>
                                    <div class="week-content">
                                        <p>學習慈心禪修的方法，培養對自己和他人的慈悲心，建立正向的情緒基礎。</p>
                                        
                                        <div class="week-goals">
                                            <h6><i class="fas fa-target me-2"></i>學習目標</h6>
                                            <div class="goal-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>學習慈心禪修步驟</span>
                                            </div>
                                            <div class="goal-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>培養自我慈悲能力</span>
                                            </div>
                                            <div class="goal-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>擴展慈悲至他人</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="week-card">
                                    <div class="week-header">
                                        <div class="week-number">4</div>
                                        <div class="week-icon">
                                            <i class="fas fa-eye"></i>
                                        </div>
                                        <h4 class="mb-2">身體覺察練習</h4>
                                        <p class="mb-0">建立身心連結</p>
                                    </div>
                                    <div class="week-content">
                                        <p>透過身體掃描等技巧，培養對身體感受的覺察能力，理解身心一體的概念。</p>
                                        
                                        <div class="week-goals">
                                            <h6><i class="fas fa-target me-2"></i>學習目標</h6>
                                            <div class="goal-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>掌握身體掃描技巧</span>
                                            </div>
                                            <div class="goal-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>提升身體覺察力</span>
                                            </div>
                                            <div class="goal-item">
                                                <i class="fas fa-check-circle"></i>
                                                <span>理解身心連結</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 第二階段：深度學習 (4-6月) -->
        <section class="py-5" data-stage="intermediate" style="background: var(--light-bg);">
            <div class="container">
                <div class="month-section">
                    <div class="month-header">
                        <h3 class="mb-0"><i class="fas fa-tree me-2"></i>第二階段：深度學習 (4-6月)</h3>
                    </div>
                    <div class="month-content">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="week-card">
                                    <div class="week-header">
                                        <div class="week-number">5</div>
                                        <div class="week-icon">
                                            <i class="fas fa-scroll"></i>
                                        </div>
                                        <h4 class="mb-2">經典文獻研讀</h4>
                                        <p class="mb-0">深入佛教智慧寶藏</p>
                                    </div>
                                    <div class="week-content">
                                        <p>研讀重要佛教經典，如《心經》、《金剛經》等，理解其中的療癒智慧。</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="week-card">
                                    <div class="week-header">
                                        <div class="week-number">6</div>
                                        <div class="week-icon">
                                            <i class="fas fa-brain"></i>
                                        </div>
                                        <h4 class="mb-2">情緒觀察與轉化</h4>
                                        <p class="mb-0">掌握情緒調節技巧</p>
                                    </div>
                                    <div class="week-content">
                                        <p>學習如何觀察情緒起伏，運用佛教方法轉化負面情緒，建立內心平靜。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 快速導航 -->
        <section class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center">
                        <h3 class="mb-4">相關課程</h3>
                        <div class="d-flex justify-content-center gap-3 flex-wrap">
                            <a href="courses-goals.html" class="btn btn-outline-primary">課程目標</a>
                            <a href="courses-methods.html" class="btn btn-outline-primary">進行方式</a>
                            <a href="membership.html" class="btn btn-primary" style="background: var(--secondary-color); border-color: var(--secondary-color);">立即報名</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // 導航欄滾動效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar-custom');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // 篩選功能
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 移除所有按鈕的 active 類別
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                // 為當前按鈕添加 active 類別
                this.classList.add('active');
                
                const filter = this.getAttribute('data-filter');
                const sections = document.querySelectorAll('[data-stage]');
                
                sections.forEach(section => {
                    if (filter === 'all' || section.getAttribute('data-stage') === filter) {
                        section.style.display = 'block';
                        section.style.opacity = '0';
                        setTimeout(() => {
                            section.style.opacity = '1';
                            section.style.transition = 'opacity 0.5s ease';
                        }, 100);
                    } else {
                        section.style.display = 'none';
                    }
                });
            });
        });

        // 週卡片動畫
        const weekCards = document.querySelectorAll('.week-card');
        const cardObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        weekCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = `all 0.6s ease ${index * 0.1}s`;
            cardObserver.observe(card);
        });

        // 進度步驟點擊效果
        document.querySelectorAll('.progress-step').forEach(step => {
            step.addEventListener('click', function() {
                const stepNumber = this.textContent;
                let filterValue = '';
                
                switch(stepNumber) {
                    case '1': filterValue = 'basic'; break;
                    case '2': filterValue = 'intermediate'; break;
                    case '3': filterValue = 'advanced'; break;
                    case '4': filterValue = 'expert'; break;
                }
                
                // 觸發對應的篩選按鈕
                const filterBtn = document.querySelector(`[data-filter="${filterValue}"]`);
                if (filterBtn) {
                    filterBtn.click();
                }
            });
        });
    </script>
</body>
</html>驟點擊效果
        document.querySelectorAll('.progress-step').forEach(step => {
            step.addEventListener('click', function() {