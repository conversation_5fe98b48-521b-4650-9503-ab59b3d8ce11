const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const userSchema = new mongoose.Schema({
    email: {
        type: String,
        required: [true, '請提供電子郵件'],
        unique: true,
        lowercase: true,
        match: [/^\S+@\S+\.\S+$/, '請提供有效的電子郵件格式']
    },
    password: {
        type: String,
        required: [true, '請提供密碼'],
        minlength: [8, '密碼至少需要8個字符'],
        select: false // 預設不返回密碼
    },
    profile: {
        firstName: {
            type: String,
            required: [true, '請提供名字'],
            maxlength: [50, '名字不能超過50個字符']
        },
        lastName: {
            type: String,
            required: [true, '請提供姓氏'],
            maxlength: [50, '姓氏不能超過50個字符']
        },
        avatar: {
            type: String,
            default: ''
        },
        phone: {
            type: String,
            match: [/^[\+]?[0-9\-\s\(\)]+$/, '請提供有效的電話號碼']
        },
        birthDate: {
            type: Date
        },
        gender: {
            type: String,
            enum: ['male', 'female', 'other', 'prefer_not_to_say'],
            default: 'prefer_not_to_say'
        },
        location: {
            city: String,
            country: String,
            timezone: {
                type: String,
                default: 'Asia/Taipei'
            }
        },
        bio: {
            type: String,
            maxlength: [500, '個人簡介不能超過500個字符']
        }
    },
    membership: {
        plan: {
            type: String,
            enum: ['basic', 'premium', 'vip'],
            default: 'basic'
        },
        status: {
            type: String,
            enum: ['active', 'cancelled', 'expired', 'pending'],
            default: 'active'
        },
        startDate: {
            type: Date,
            default: Date.now
        },
        endDate: {
            type: Date,
            default: () => new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30天後
        },
        autoRenewal: {
            type: Boolean,
            default: true
        },
        paymentMethod: {
            type: String,
            enum: ['credit', 'atm', 'linepay', 'applepay'],
            default: 'credit'
        },
        stripeCustomerId: String,
        subscriptionId: String
    },
    preferences: {
        language: {
            type: String,
            enum: ['zh-TW', 'zh-CN', 'en', 'ja'],
            default: 'zh-TW'
        },
        notifications: {
            email: {
                newsletter: { type: Boolean, default: true },
                courseUpdates: { type: Boolean, default: true },
                workshopReminders: { type: Boolean, default: true },
                promotions: { type: Boolean, default: false }
            },
            push: {
                enabled: { type: Boolean, default: true },
                dailyReminders: { type: Boolean, default: false },
                weeklyProgress: { type: Boolean, default: true }
            }
        },
        privacy: {
            profileVisibility: {
                type: String,
                enum: ['public', 'members', 'private'],
                default: 'members'
            },
            showProgress: { type: Boolean, default: true },
            showCertificates: { type: Boolean, default: true }
        },
        interests: [{
            type: String,
            enum: [
                'meditation', 'tibetan_medicine', 'scriptures', 
                'sound_healing', 'mind_body_healing', 'overseas_study',
                'research', 'workshops', 'community'
            ]
        }]
    },
    progress: {
        coursesEnrolled: [{
            courseId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Course'
            },
            enrollDate: {
                type: Date,
                default: Date.now
            },
            progress: {
                type: Number,
                min: 0,
                max: 100,
                default: 0
            },
            completedModules: [String],
            lastAccessed: Date,
            timeSpent: {
                type: Number,
                default: 0 // 分鐘
            }
        }],
        coursesCompleted: [{
            courseId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Course'
            },
            completionDate: {
                type: Date,
                default: Date.now
            },
            grade: {
                type: Number,
                min: 0,
                max: 100
            },
            certificateId: String
        }],
        certificates: [{
            type: {
                type: String,
                enum: ['course', 'workshop', 'program'],
                required: true
            },
            title: String,
            issueDate: {
                type: Date,
                default: Date.now
            },
            certificateId: String,
            verificationUrl: String,
            metadata: {
                instructor: String,
                duration: Number,
                skills: [String]
            }
        }],
        totalStudyTime: {
            type: Number,
            default: 0 // 總學習時間（分鐘）
        },
        longestStreak: {
            type: Number,
            default: 0 // 最長連續學習天數
        },
        currentStreak: {
            type: Number,
            default: 0 // 當前連續學習天數
        },
        lastActiveDate: {
            type: Date,
            default: Date.now
        },
        achievements: [{
            type: String,
            dateEarned: {
                type: Date,
                default: Date.now
            },
            description: String
        }]
    },
    bookmarks: {
        scriptures: [{
            scriptureId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Scripture'
            },
            bookmarkedAt: {
                type: Date,
                default: Date.now
            },
            tags: [String],
            notes: String
        }],
        research: [{
            researchId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Research'
            },
            bookmarkedAt: {
                type: Date,
                default: Date.now
            },
            tags: [String],
            notes: String
        }]
    },
    workshops: {
        registered: [{
            workshopId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Workshop'
            },
            registrationDate: {
                type: Date,
                default: Date.now
            },
            status: {
                type: String,
                enum: ['registered', 'waitlist', 'attended', 'cancelled'],
                default: 'registered'
            },
            feedback: {
                rating: {
                    type: Number,
                    min: 1,
                    max: 5
                },
                comment: String,
                submittedAt: Date
            }
        }]
    },
    overseas: {
        applications: [{
            programId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'OverseasProgram'
            },
            applicationDate: {
                type: Date,
                default: Date.now
            },
            status: {
                type: String,
                enum: ['pending', 'approved', 'rejected', 'completed'],
                default: 'pending'
            },
            documents: [{
                type: String,
                url: String,
                uploadDate: Date
            }],
            motivation: String,
            experience: String
        }]
    },
    security: {
        lastLogin: Date,
        loginAttempts: {
            type: Number,
            default: 0
        },
        lockUntil: Date,
        passwordResetToken: String,
        passwordResetExpires: Date,
        emailVerified: {
            type: Boolean,
            default: false
        },
        emailVerificationToken: String,
        twoFactorEnabled: {
            type: Boolean,
            default: false
        },
        twoFactorSecret: String
    },
    role: {
        type: String,
        enum: ['user', 'instructor', 'admin', 'moderator'],
        default: 'user'
    },
    status: {
        type: String,
        enum: ['active', 'inactive', 'suspended', 'pending'],
        default: 'pending'
    }
}, {
    timestamps: true,
    toJSON: { 
        virtuals: true,
        transform: function(doc, ret) {
            delete ret.password;
            delete ret.security.passwordResetToken;
            delete ret.security.emailVerificationToken;
            delete ret.security.twoFactorSecret;
            return ret;
        }
    },
    toObject: { virtuals: true }
});

// 虛擬欄位
userSchema.virtual('fullName').get(function() {
    return `${this.profile.firstName} ${this.profile.lastName}`;
});

userSchema.virtual('isLocked').get(function() {
    return !!(this.security.lockUntil && this.security.lockUntil > Date.now());
});

userSchema.virtual('membershipActive').get(function() {
    return this.membership.status === 'active' && 
           this.membership.endDate > new Date();
});

// 索引
userSchema.index({ email: 1 });
userSchema.index({ 'membership.plan': 1, 'membership.status': 1 });
userSchema.index({ 'progress.lastActiveDate': -1 });
userSchema.index({ createdAt: -1 });

// 預處理中間件
userSchema.pre('save', async function(next) {
    // 密碼加密
    if (this.isModified('password')) {
        const salt = await bcrypt.genSalt(12);
        this.password = await bcrypt.hash(this.password, salt);
    }
    
    // 更新最後活躍時間
    if (this.isModified() && !this.isNew) {
        this.progress.lastActiveDate = new Date();
    }
    
    next();
});

// 實例方法
userSchema.methods.comparePassword = async function(candidatePassword) {
    if (!this.password) return false;
    return await bcrypt.compare(candidatePassword, this.password);
};

userSchema.methods.generateAuthToken = function() {
    const payload = {
        id: this._id,
        email: this.email,
        role: this.role,
        membership: this.membership.plan
    };
    
    return jwt.sign(payload, process.env.JWT_SECRET, {
        expiresIn: process.env.JWT_EXPIRES_IN || '7d'
    });
};

userSchema.methods.generateRefreshToken = function() {
    const payload = {
        id: this._id,
        type: 'refresh'
    };
    
    return jwt.sign(payload, process.env.JWT_REFRESH_SECRET, {
        expiresIn: '30d'
    });
};

userSchema.methods.createPasswordResetToken = function() {
    const resetToken = require('crypto').randomBytes(32).toString('hex');
    
    this.security.passwordResetToken = require('crypto')
        .createHash('sha256')
        .update(resetToken)
        .digest('hex');
        
    this.security.passwordResetExpires = Date.now() + 10 * 60 * 1000; // 10分鐘
    
    return resetToken;
};

userSchema.methods.createEmailVerificationToken = function() {
    const verificationToken = require('crypto').randomBytes(32).toString('hex');
    
    this.security.emailVerificationToken = require('crypto')
        .createHash('sha256')
        .update(verificationToken)
        .digest('hex');
        
    return verificationToken;
};

userSchema.methods.incrementLoginAttempts = function() {
    // 如果有前一次鎖定且已過期，重置計數
    if (this.security.lockUntil && this.security.lockUntil < Date.now()) {
        return this.updateOne({
            $unset: { 'security.lockUntil': 1 },
            $set: { 'security.loginAttempts': 1 }
        });
    }
    
    const updates = { $inc: { 'security.loginAttempts': 1 } };
    
    // 如果達到最大嘗試次數且目前未鎖定，設定鎖定時間
    if (this.security.loginAttempts + 1 >= 5 && !this.isLocked) {
        updates.$set = { 'security.lockUntil': Date.now() + 2 * 60 * 60 * 1000 }; // 鎖定2小時
    }
    
    return this.updateOne(updates);
};

userSchema.methods.resetLoginAttempts = function() {
    return this.updateOne({
        $unset: { 
            'security.loginAttempts': 1, 
            'security.lockUntil': 1 
        }
    });
};

// 靜態方法
userSchema.statics.findByEmail = function(email) {
    return this.findOne({ email: email.toLowerCase() });
};

userSchema.statics.getActiveMembers = function() {
    return this.find({
        'membership.status': 'active',
        'membership.endDate': { $gt: new Date() }
    });
};

userSchema.statics.getMembershipStats = function() {
    return this.aggregate([
        {
            $group: {
                _id: '$membership.plan',
                count: { $sum: 1 },
                active: {
                    $sum: {
                        $cond: [
                            { 
                                $and: [
                                    { $eq: ['$membership.status', 'active'] },
                                    { $gt: ['$membership.endDate', new Date()] }
                                ]
                            },
                            1,
                            0
                        ]
                    }
                }
            }
        }
    ]);
};

module.exports = mongoose.model('User', userSchema);