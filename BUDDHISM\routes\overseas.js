const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const authenticate = require('../middleware/auth');

// 驗證錯誤處理中間件
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// 模擬海外項目數據
const overseasPrograms = {
  pilgrimages: [
    {
      id: '1',
      title: '印度佛教聖地朝聖之旅',
      destination: '印度',
      duration: 14,
      startDate: '2024-03-15',
      endDate: '2024-03-28',
      maxParticipants: 25,
      currentParticipants: 18,
      price: 89000,
      currency: 'TWD',
      difficulty: 'moderate',
      highlights: [
        '菩提伽耶金剛座',
        '鹿野苑初轉法輪地',
        '靈鷲山說法聖地',
        '那爛陀大學遺址'
      ],
      includes: ['機票', '住宿', '三餐', '導遊', '門票'],
      leader: '慈悲法師',
      status: 'open_registration',
      requirements: ['身體健康', '基本佛學知識', '團體精神'],
      itinerary: [
        { day: 1, location: '台北→德里', activities: ['搭機前往印度', '抵達德里機場'] },
        { day: 2, location: '德里→菩提伽耶', activities: ['參訪正覺大塔', '金剛座禪坐'] }
      ]
    },
    {
      id: '2',
      title: '西藏拉薩心靈之旅',
      destination: '西藏',
      duration: 10,
      startDate: '2024-05-20',
      endDate: '2024-05-29',
      maxParticipants: 20,
      currentParticipants: 12,
      price: 125000,
      currency: 'TWD',
      difficulty: 'challenging',
      highlights: [
        '布達拉宮朝聖',
        '大昭寺參拜',
        '色拉寺辯經',
        '納木錯湖巡禮'
      ],
      includes: ['機票', '住宿', '導遊', '氧氣設備', '高原保險'],
      leader: '智慧仁波切',
      status: 'open_registration',
      requirements: ['高原適應能力', '身體檢查報告', '進藏許可'],
      itinerary: []
    }
  ],
  retreats: [
    {
      id: '1',
      title: '緬甸內觀禪修營',
      location: '緬甸仰光',
      duration: 21,
      startDate: '2024-02-01',
      endDate: '2024-02-21',
      maxParticipants: 15,
      currentParticipants: 8,
      price: 45000,
      currency: 'TWD',
      style: 'vipassana',
      teacher: 'Sayalay Susila',
      schedule: [
        '04:30 起床鐘聲',
        '05:00-06:30 晨坐',
        '06:30-08:00 早餐休息',
        '08:00-11:00 上午禪坐',
        '11:00-13:00 午餐休息',
        '13:00-17:00 下午禪坐',
        '17:00-18:00 茶點時間',
        '18:00-19:00 晚間開示',
        '19:00-20:00 晚坐',
        '20:00 止語休息'
      ],
      requirements: ['禪修基礎', '身心健康', '嚴守戒律'],
      status: 'open_registration'
    }
  ],
  studyPrograms: [
    {
      id: '1',
      title: '泰國法身寺佛學研修',
      institution: '泰國法身寺',
      duration: 30,
      startDate: '2024-06-01',
      endDate: '2024-06-30',
      subjects: ['巴利文', '阿毘達摩', '戒律學', '禪修指導'],
      certification: true,
      price: 35000,
      currency: 'TWD',
      language: '英文/中文翻譯',
      credits: 6,
      professor: 'Phra Ajahn Chah',
      requirements: ['大學學歷', '佛學基礎', '英語能力'],
      status: 'accepting_applications'
    }
  ]
};

// 獲取朝聖旅程列表
router.get('/pilgrimages', async (req, res) => {
  try {
    const {
      destination,
      duration,
      difficulty,
      status,
      priceRange,
      page = 1,
      limit = 10
    } = req.query;

    let pilgrimages = [...overseasPrograms.pilgrimages];

    // 應用篩選
    if (destination) {
      pilgrimages = pilgrimages.filter(p => 
        p.destination.toLowerCase().includes(destination.toLowerCase())
      );
    }
    if (difficulty) {
      pilgrimages = pilgrimages.filter(p => p.difficulty === difficulty);
    }
    if (status) {
      pilgrimages = pilgrimages.filter(p => p.status === status);
    }
    if (duration) {
      const [minDays, maxDays] = duration.split('-').map(Number);
      pilgrimages = pilgrimages.filter(p => 
        p.duration >= minDays && p.duration <= maxDays
      );
    }
    if (priceRange) {
      const [minPrice, maxPrice] = priceRange.split('-').map(Number);
      pilgrimages = pilgrimages.filter(p => 
        p.price >= minPrice && p.price <= maxPrice
      );
    }

    // 分頁
    const skip = (page - 1) * limit;
    const paginatedPilgrimages = pilgrimages.slice(skip, skip + parseInt(limit));

    res.json({
      message: 'Pilgrimages retrieved successfully',
      pilgrimages: paginatedPilgrimages,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: pilgrimages.length,
        pages: Math.ceil(pilgrimages.length / limit)
      },
      filters: {
        destinations: [...new Set(overseasPrograms.pilgrimages.map(p => p.destination))],
        difficulties: ['easy', 'moderate', 'challenging'],
        statuses: ['open_registration', 'closed', 'completed', 'cancelled']
      }
    });
  } catch (error) {
    console.error('Get pilgrimages error:', error);
    res.status(500).json({ error: 'Failed to fetch pilgrimages' });
  }
});

// 獲取特定朝聖詳情
router.get('/pilgrimages/:id', async (req, res) => {
  try {
    const pilgrimageId = req.params.id;
    const pilgrimage = overseasPrograms.pilgrimages.find(p => p.id === pilgrimageId);

    if (!pilgrimage) {
      return res.status(404).json({ error: 'Pilgrimage not found' });
    }

    // 計算剩餘名額和報名截止日期
    const spotsLeft = pilgrimage.maxParticipants - pilgrimage.currentParticipants;
    const registrationDeadline = new Date(pilgrimage.startDate);
    registrationDeadline.setDate(registrationDeadline.getDate() - 30); // 出發前30天截止

    res.json({
      message: 'Pilgrimage details retrieved successfully',
      pilgrimage: {
        ...pilgrimage,
        spotsLeft,
        registrationDeadline,
        isRegistrationOpen: new Date() < registrationDeadline && spotsLeft > 0
      }
    });
  } catch (error) {
    console.error('Get pilgrimage details error:', error);
    res.status(500).json({ error: 'Failed to fetch pilgrimage details' });
  }
});

// 報名朝聖
router.post('/pilgrimages/:id/register', authenticate, [
  body('emergencyContact').notEmpty().withMessage('Emergency contact is required'),
  body('emergencyPhone').isMobilePhone().withMessage('Valid emergency phone required'),
  body('medicalConditions').optional().isLength({ max: 500 }).withMessage('Medical conditions too long'),
  body('dietaryRequirements').optional().isLength({ max: 200 }).withMessage('Dietary requirements too long'),
  body('passportNumber').notEmpty().withMessage('Passport number is required'),
  body('passportExpiry').isISO8601().withMessage('Valid passport expiry date required')
], handleValidationErrors, async (req, res) => {
  try {
    const pilgrimageId = req.params.id;
    const userId = req.user._id;
    const {
      emergencyContact,
      emergencyPhone,
      medicalConditions,
      dietaryRequirements,
      passportNumber,
      passportExpiry,
      specialRequests
    } = req.body;

    const pilgrimage = overseasPrograms.pilgrimages.find(p => p.id === pilgrimageId);
    if (!pilgrimage) {
      return res.status(404).json({ error: 'Pilgrimage not found' });
    }

    // 檢查名額
    const spotsLeft = pilgrimage.maxParticipants - pilgrimage.currentParticipants;
    if (spotsLeft <= 0) {
      return res.status(400).json({ error: 'No spots available' });
    }

    // 檢查報名截止日期
    const registrationDeadline = new Date(pilgrimage.startDate);
    registrationDeadline.setDate(registrationDeadline.getDate() - 30);
    if (new Date() > registrationDeadline) {
      return res.status(400).json({ error: 'Registration deadline has passed' });
    }

    // 檢查護照有效期
    if (new Date(passportExpiry) <= new Date(pilgrimage.endDate)) {
      return res.status(400).json({ error: 'Passport must be valid beyond travel end date' });
    }

    // 創建報名記錄
    const registration = {
      id: Date.now().toString(),
      userId,
      pilgrimageId,
      emergencyContact,
      emergencyPhone,
      medicalConditions,
      dietaryRequirements,
      passportNumber,
      passportExpiry,
      specialRequests,
      status: 'pending_payment',
      registeredAt: new Date(),
      paymentDueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天內付款
    };

    // 更新參與人數（模擬）
    pilgrimage.currentParticipants += 1;

    console.log('Pilgrimage registration:', registration);

    res.json({
      message: 'Registration submitted successfully',
      registration: {
        id: registration.id,
        pilgrimageTitle: pilgrimage.title,
        status: registration.status,
        paymentDueDate: registration.paymentDueDate,
        totalAmount: pilgrimage.price
      },
      nextSteps: [
        '請於7天內完成付款',
        '我們會發送詳細行程資料',
        '出發前30天會舉行行前說明會'
      ]
    });
  } catch (error) {
    console.error('Register pilgrimage error:', error);
    res.status(500).json({ error: 'Failed to register for pilgrimage' });
  }
});

// 獲取禪修營列表
router.get('/retreats', async (req, res) => {
  try {
    const { style, location, duration, teacher } = req.query;
    let retreats = [...overseasPrograms.retreats];

    if (style) retreats = retreats.filter(r => r.style === style);
    if (location) retreats = retreats.filter(r => r.location.includes(location));
    if (teacher) retreats = retreats.filter(r => r.teacher.includes(teacher));
    if (duration) {
      const [minDays, maxDays] = duration.split('-').map(Number);
      retreats = retreats.filter(r => r.duration >= minDays && r.duration <= maxDays);
    }

    res.json({
      message: 'Retreats retrieved successfully',
      retreats,
      styles: ['vipassana', 'zen', 'tibetan', 'thai_forest'],
      locations: [...new Set(overseasPrograms.retreats.map(r => r.location))]
    });
  } catch (error) {
    console.error('Get retreats error:', error);
    res.status(500).json({ error: 'Failed to fetch retreats' });
  }
});

// 獲取研修項目列表
router.get('/study-programs', async (req, res) => {
  try {
    const { institution, duration, subjects, certification } = req.query;
    let programs = [...overseasPrograms.studyPrograms];

    if (institution) {
      programs = programs.filter(p => 
        p.institution.toLowerCase().includes(institution.toLowerCase())
      );
    }
    if (certification === 'true') {
      programs = programs.filter(p => p.certification === true);
    }
    if (subjects) {
      const subjectList = subjects.split(',');
      programs = programs.filter(p => 
        subjectList.some(subject => 
          p.subjects.some(s => s.toLowerCase().includes(subject.toLowerCase()))
        )
      );
    }

    res.json({
      message: 'Study programs retrieved successfully',
      programs,
      institutions: [...new Set(overseasPrograms.studyPrograms.map(p => p.institution))],
      availableSubjects: [
        '巴利文', '梵文', '阿毘達摩', '戒律學', '禪修指導', 
        '佛教史', '唯識學', '中觀學', '淨土學'
      ]
    });
  } catch (error) {
    console.error('Get study programs error:', error);
    res.status(500).json({ error: 'Failed to fetch study programs' });
  }
});

// 申請研修項目
router.post('/study-programs/:id/apply', authenticate, [
  body('motivation').isLength({ min: 100, max: 1000 }).withMessage('Motivation letter must be 100-1000 characters'),
  body('education').notEmpty().withMessage('Education background is required'),
  body('buddhismExperience').isLength({ min: 50, max: 500 }).withMessage('Buddhism experience must be 50-500 characters'),
  body('languageSkills').isArray().withMessage('Language skills must be an array'),
  body('references').optional().isArray().withMessage('References must be an array')
], handleValidationErrors, async (req, res) => {
  try {
    const programId = req.params.id;
    const userId = req.user._id;
    const {
      motivation,
      education,
      buddhismExperience,
      languageSkills,
      references,
      portfolioUrl
    } = req.body;

    const program = overseasPrograms.studyPrograms.find(p => p.id === programId);
    if (!program) {
      return res.status(404).json({ error: 'Study program not found' });
    }

    // 檢查申請截止日期
    const applicationDeadline = new Date(program.startDate);
    applicationDeadline.setDate(applicationDeadline.getDate() - 60); // 開始前60天截止申請
    if (new Date() > applicationDeadline) {
      return res.status(400).json({ error: 'Application deadline has passed' });
    }

    // 創建申請記錄
    const application = {
      id: Date.now().toString(),
      userId,
      programId,
      motivation,
      education,
      buddhismExperience,
      languageSkills,
      references,
      portfolioUrl,
      status: 'under_review',
      submittedAt: new Date(),
      reviewDeadline: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000) // 21天內審核
    };

    console.log('Study program application:', application);

    res.json({
      message: 'Application submitted successfully',
      application: {
        id: application.id,
        programTitle: program.title,
        status: application.status,
        submittedAt: application.submittedAt,
        reviewDeadline: application.reviewDeadline
      },
      nextSteps: [
        '審核委員會將在21天內審核您的申請',
        '如需補充材料會另行通知',
        '錄取結果將以電子郵件通知'
      ]
    });
  } catch (error) {
    console.error('Apply study program error:', error);
    res.status(500).json({ error: 'Failed to submit application' });
  }
});

// 獲取用戶的報名/申請記錄
router.get('/my/registrations', authenticate, async (req, res) => {
  try {
    const userId = req.user._id;
    const { type, status } = req.query;

    // 模擬用戶報名記錄
    const userRegistrations = {
      pilgrimages: [
        {
          id: '1',
          pilgrimageId: '1',
          title: '印度佛教聖地朝聖之旅',
          status: 'confirmed',
          registeredAt: '2023-12-01',
          paymentStatus: 'paid',
          departureDate: '2024-03-15'
        }
      ],
      retreats: [],
      studyPrograms: [
        {
          id: '1',
          programId: '1',
          title: '泰國法身寺佛學研修',
          status: 'under_review',
          appliedAt: '2023-11-15',
          reviewDeadline: '2023-12-06'
        }
      ]
    };

    let result = {};
    if (!type || type === 'pilgrimages') result.pilgrimages = userRegistrations.pilgrimages;
    if (!type || type === 'retreats') result.retreats = userRegistrations.retreats;
    if (!type || type === 'study-programs') result.studyPrograms = userRegistrations.studyPrograms;

    // 狀態篩選
    if (status) {
      Object.keys(result).forEach(key => {
        result[key] = result[key].filter(item => item.status === status);
      });
    }

    res.json({
      message: 'User registrations retrieved successfully',
      registrations: result,
      summary: {
        totalPilgrimages: userRegistrations.pilgrimages.length,
        totalRetreats: userRegistrations.retreats.length,
        totalStudyPrograms: userRegistrations.studyPrograms.length,
        upcomingEvents: 1
      }
    });
  } catch (error) {
    console.error('Get user registrations error:', error);
    res.status(500).json({ error: 'Failed to fetch user registrations' });
  }
});

// 取消報名/申請
router.delete('/registrations/:id/cancel', authenticate, [
  body('reason').optional().isLength({ max: 500 }).withMessage('Reason too long'),
  body('type').isIn(['pilgrimage', 'retreat', 'study-program']).withMessage('Invalid type')
], handleValidationErrors, async (req, res) => {
  try {
    const registrationId = req.params.id;
    const { reason, type } = req.body;
    const userId = req.user._id;

    // 檢查取消政策
    const cancellationFee = calculateCancellationFee(type, new Date());

    // 處理取消（實際應更新數據庫）
    console.log(`Cancellation: User ${userId} cancelled ${type} registration ${registrationId}`);

    res.json({
      message: 'Registration cancelled successfully',
      cancellationDetails: {
        registrationId,
        type,
        cancelledAt: new Date(),
        reason,
        refundAmount: cancellationFee.refundAmount,
        cancellationFee: cancellationFee.fee,
        refundProcessingTime: '5-10 business days'
      }
    });
  } catch (error) {
    console.error('Cancel registration error:', error);
    res.status(500).json({ error: 'Failed to cancel registration' });
  }
});

// 計算取消費用的輔助函數
function calculateCancellationFee(type, cancellationDate) {
  // 簡化的取消費用計算
  const baseFee = type === 'pilgrimage' ? 5000 : type === 'retreat' ? 3000 : 1000;
  const daysBeforeEvent = 30; // 假設還有30天
  
  if (daysBeforeEvent > 30) {
    return { fee: baseFee * 0.1, refundAmount: baseFee * 0.9 };
  } else if (daysBeforeEvent > 14) {
    return { fee: baseFee * 0.3, refundAmount: baseFee * 0.7 };
  } else {
    return { fee: baseFee * 0.5, refundAmount: baseFee * 0.5 };
  }
}

module.exports = router;