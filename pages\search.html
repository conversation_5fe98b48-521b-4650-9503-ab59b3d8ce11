<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索 - 澄源閱讀</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 搜索頭部 */
        .search-header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            text-align: center;
        }

        .search-title {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        }

        .search-subtitle {
            color: #666;
            margin-bottom: 30px;
        }

        .search-container {
            position: relative;
            max-width: 600px;
            margin: 0 auto;
        }

        .search-input {
            width: 100%;
            padding: 15px 60px 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 50px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .search-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }

        .search-btn:hover {
            transform: translateY(-50%) scale(1.1);
        }

        /* 搜索建議 */
        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .suggestion-item {
            padding: 12px 20px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: background 0.2s ease;
        }

        .suggestion-item:hover {
            background: #f8f9fa;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        /* 快速搜索標籤 */
        .quick-search {
            margin-top: 20px;
        }

        .quick-search-label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 10px;
        }

        .quick-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }

        .quick-tag {
            background: #f0f4ff;
            color: #667eea;
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .quick-tag:hover {
            background: #667eea;
            color: white;
            border-color: #5a52d5;
        }

        /* 搜索結果區域 */
        .search-results {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            display: none;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .results-info {
            color: #666;
        }

        .results-filters {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #e1e5e9;
            border-radius: 6px;
            background: white;
            font-size: 0.8rem;
        }

        .results-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .result-item {
            padding: 20px;
            border: 1px solid #f0f0f0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .result-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .result-category {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.7rem;
            display: inline-block;
            margin-bottom: 8px;
        }

        .result-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .result-excerpt {
            color: #666;
            line-height: 1.5;
            margin-bottom: 10px;
        }

        .result-highlight {
            background: #fff3cd;
            padding: 1px 3px;
            border-radius: 3px;
        }

        .result-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: #999;
        }

        .result-source {
            font-weight: 500;
        }

        /* 搜索歷史 */
        .search-history {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .clear-history {
            color: #667eea;
            cursor: pointer;
            font-size: 0.8rem;
            text-decoration: underline;
        }

        .clear-history:hover {
            color: #5a52d5;
        }

        .history-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .history-item {
            display: flex;
            justify-content: between;
            align-items: center;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .history-item:hover {
            background: #f8f9fa;
        }

        .history-query {
            flex: 1;
            color: #666;
        }

        .history-time {
            font-size: 0.7rem;
            color: #999;
        }

        /* 熱門搜索 */
        .popular-searches {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .popular-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .popular-item {
            padding: 15px;
            border: 1px solid #f0f0f0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .popular-item:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .popular-query {
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }

        .popular-count {
            font-size: 0.7rem;
            color: #999;
        }

        /* 空狀態 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-icon {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 20px;
        }

        .empty-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .empty-description {
            color: #999;
            line-height: 1.6;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .search-header {
                padding: 25px;
            }
            
            .search-title {
                font-size: 1.5rem;
            }
            
            .results-header {
                flex-direction: column;
                align-items: stretch;
            }
            
            .results-filters {
                justify-content: center;
            }
            
            .popular-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .search-input {
                font-size: 1rem;
                padding: 12px 50px 12px 15px;
            }
            
            .search-btn {
                width: 40px;
                height: 40px;
            }
            
            .quick-tags {
                justify-content: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 搜索頭部 -->
        <header class="search-header">
            <h1 class="search-title">智慧搜索</h1>
            <p class="search-subtitle">在澄源閱讀中探索佛學智慧與身心療癒知識</p>
            
            <div class="search-container">
                <input type="text" class="search-input" id="search-input" placeholder="搜索文章、作者或關鍵詞...">
                <button class="search-btn" id="search-btn">
                    <i class="fas fa-search"></i>
                </button>
                
                <div class="search-suggestions" id="suggestions">
                    <!-- 搜索建議將動態生成 -->
                </div>
            </div>
            
            <div class="quick-search">
                <div class="quick-search-label">熱門搜索：</div>
                <div class="quick-tags">
                    <span class="quick-tag" data-query="慈悲修持">慈悲修持</span>
                    <span class="quick-tag" data-query="正念冥想">正念冥想</span>
                    <span class="quick-tag" data-query="藏傳佛教">藏傳佛教</span>
                    <span class="quick-tag" data-query="身心療癒">身心療癒</span>
                    <span class="quick-tag" data-query="禪修方法">禪修方法</span>
                    <span class="quick-tag" data-query="佛教哲學">佛教哲學</span>
                </div>
            </div>
        </header>

        <!-- 搜索結果 -->
        <section class="search-results" id="search-results">
            <div class="results-header">
                <div class="results-info" id="results-info">
                    找到 0 個相關結果
                </div>
                <div class="results-filters">
                    <select class="filter-select" id="category-filter">
                        <option value="">所有分類</option>
                        <option value="dharma">佛學智慧</option>
                        <option value="healing">身心療癒</option>
                        <option value="research">最新研究</option>
                    </select>
                    <select class="filter-select" id="sort-filter">
                        <option value="relevance">相關度</option>
                        <option value="date">發布時間</option>
                        <option value="views">瀏覽量</option>
                    </select>
                </div>
            </div>
            
            <div class="results-list" id="results-list">
                <!-- 搜索結果將動態生成 -->
            </div>
            
            <div class="empty-state" id="empty-state" style="display: none;">
                <div class="empty-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3 class="empty-title">沒有找到相關結果</h3>
                <p class="empty-description">請嘗試使用不同的關鍵詞或瀏覽推薦內容</p>
            </div>
        </section>

        <!-- 搜索歷史 -->
        <section class="search-history">
            <div class="history-header">
                <h2 class="section-title">
                    <i class="fas fa-history"></i>
                    搜索歷史
                </h2>
                <span class="clear-history" id="clear-history">清除歷史</span>
            </div>
            <div class="history-list" id="history-list">
                <!-- 搜索歷史將動態生成 -->
            </div>
        </section>

        <!-- 熱門搜索 -->
        <section class="popular-searches">
            <h2 class="section-title">
                <i class="fas fa-fire"></i>
                熱門搜索
            </h2>
            <div class="popular-grid" id="popular-grid">
                <!-- 熱門搜索將動態生成 -->
            </div>
        </section>
    </div>

    <script>
        // 模擬搜索數據
        const searchDatabase = [
            {
                id: 1,
                title: "藏傳佛教中的慈悲修持法門",
                category: "dharma",
                categoryName: "佛學智慧",
                excerpt: "慈悲心是藏傳佛教修行的核心，也是成就菩薩道的根本。本文將深入探討如何在日常生活中培養真正的慈悲心...",
                content: "慈悲心 四無量心 禪修 藏傳佛教 修行方法",
                author: "釋智慧",
                date: "2025-01-10",
                views: 1245
            },
            {
                id: 2,
                title: "正念冥想對現代人壓力緩解的應用",
                category: "healing",
                categoryName: "身心療癒",
                excerpt: "結合科學研究與傳統修行，探討正念冥想如何有效緩解現代生活中的各種壓力...",
                content: "正念 冥想 壓力管理 身心健康 療癒方法",
                author: "Dr. 陳明",
                date: "2025-01-08",
                views: 892
            },
            {
                id: 3,
                title: "冥想對大腦神經可塑性的影響研究",
                category: "research",
                categoryName: "最新研究",
                excerpt: "最新神經科學研究顯示，長期冥想練習能夠改變大腦結構，提升認知能力...",
                content: "神經科學 大腦研究 冥想 認知能力 神經可塑性",
                author: "Dr. 王研究",
                date: "2025-01-05",
                views: 1567
            }
        ];

        // 搜索建議數據
        const searchSuggestions = [
            "慈悲修持", "正念冥想", "藏傳佛教", "身心療癒", "禪修方法",
            "佛教哲學", "壓力管理", "神經科學", "冥想研究", "修行指南"
        ];

        // 搜索歷史
        let searchHistory = JSON.parse(localStorage.getItem('search_history') || '[]');

        // 熱門搜索數據
        const popularSearches = [
            { query: "慈悲修持", count: 1234 },
            { query: "正念冥想", count: 987 },
            { query: "藏傳佛教", count: 756 },
            { query: "身心療癒", count: 543 },
            { query: "禪修方法", count: 432 },
            { query: "佛教哲學", count: 321 }
        ];

        let currentResults = [];

        // 執行搜索
        function performSearch(query, category = '', sort = 'relevance') {
            if (!query.trim()) return;

            // 添加到搜索歷史
            addToHistory(query);

            // 過濾結果
            currentResults = searchDatabase.filter(item => {
                const matchQuery = 
                    item.title.toLowerCase().includes(query.toLowerCase()) ||
                    item.content.toLowerCase().includes(query.toLowerCase()) ||
                    item.excerpt.toLowerCase().includes(query.toLowerCase());
                
                const matchCategory = !category || item.category === category;
                
                return matchQuery && matchCategory;
            });

            // 排序結果
            sortResults(sort);

            // 顯示結果
            displayResults(query);
        }

        // 排序結果
        function sortResults(sort) {
            switch(sort) {
                case 'date':
                    currentResults.sort((a, b) => new Date(b.date) - new Date(a.date));
                    break;
                case 'views':
                    currentResults.sort((a, b) => b.views - a.views);
                    break;
                default: // relevance
                    // 保持原始順序（相關度）
                    break;
            }
        }

        // 顯示搜索結果
        function displayResults(query) {
            const resultsSection = document.getElementById('search-results');
            const resultsInfo = document.getElementById('results-info');
            const resultsList = document.getElementById('results-list');
            const emptyState = document.getElementById('empty-state');

            resultsSection.style.display = 'block';
            resultsInfo.textContent = `找到 ${currentResults.length} 個「${query}」的相關結果`;

            if (currentResults.length === 0) {
                resultsList.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            resultsList.style.display = 'block';
            emptyState.style.display = 'none';

            resultsList.innerHTML = currentResults.map(result => {
                const highlightedTitle = highlightText(result.title, query);
                const highlightedExcerpt = highlightText(result.excerpt, query);

                return `
                    <div class="result-item" data-id="${result.id}">
                        <span class="result-category">${result.categoryName}</span>
                        <h3 class="result-title">${highlightedTitle}</h3>
                        <p class="result-excerpt">${highlightedExcerpt}</p>
                        <div class="result-meta">
                            <span class="result-source">${result.author} • ${result.date}</span>
                            <span>${result.views} 次瀏覽</span>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 高亮搜索關鍵詞
        function highlightText(text, query) {
            const regex = new RegExp(`(${query})`, 'gi');
            return text.replace(regex, '<span class="result-highlight">$1</span>');
        }

        // 顯示搜索建議
        function showSuggestions(query) {
            const suggestions = document.getElementById('suggestions');
            
            if (!query.trim()) {
                suggestions.style.display = 'none';
                return;
            }

            const matches = searchSuggestions.filter(suggestion =>
                suggestion.toLowerCase().includes(query.toLowerCase())
            );

            if (matches.length === 0) {
                suggestions.style.display = 'none';
                return;
            }

            suggestions.innerHTML = matches.map(match => `
                <div class="suggestion-item" data-suggestion="${match}">
                    ${highlightText(match, query)}
                </div>
            `).join('');

            suggestions.style.display = 'block';
        }

        // 隱藏搜索建議
        function hideSuggestions() {
            setTimeout(() => {
                document.getElementById('suggestions').style.display = 'none';
            }, 200);
        }

        // 添加到搜索歷史
        function addToHistory(query) {
            const existingIndex = searchHistory.findIndex(item => item.query === query);
            
            if (existingIndex > -1) {
                searchHistory.splice(existingIndex, 1);
            }

            searchHistory.unshift({
                query: query,
                time: new Date().toLocaleString()
            });

            // 限制歷史記錄數量
            if (searchHistory.length > 10) {
                searchHistory = searchHistory.slice(0, 10);
            }

            localStorage.setItem('search_history', JSON.stringify(searchHistory));
            renderHistory();
        }

        // 渲染搜索歷史
        function renderHistory() {
            const historyList = document.getElementById('history-list');
            
            if (searchHistory.length === 0) {
                historyList.innerHTML = '<div style="text-align: center; color: #999; padding: 20px;">暫無搜索歷史</div>';
                return;
            }

            historyList.innerHTML = searchHistory.map(item => `
                <div class="history-item" data-query="${item.query}">
                    <span class="history-query">${item.query}</span>
                    <span class="history-time">${item.time}</span>
                </div>
            `).join('');
        }

        // 渲染熱門搜索
        function renderPopularSearches() {
            const popularGrid = document.getElementById('popular-grid');
            
            popularGrid.innerHTML = popularSearches.map(item => `
                <div class="popular-item" data-query="${item.query}">
                    <div class="popular-query">${item.query}</div>
                    <div class="popular-count">${item.count} 次搜索</div>
                </div>
            `).join('');
        }

        // 事件監聽
        const searchInput = document.getElementById('search-input');
        const searchBtn = document.getElementById('search-btn');

        // 搜索輸入事件
        searchInput.addEventListener('input', function(e) {
            showSuggestions(e.target.value);
        });

        searchInput.addEventListener('blur', hideSuggestions);

        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch(this.value);
            }
        });

        // 搜索按鈕點擊
        searchBtn.addEventListener('click', function() {
            performSearch(searchInput.value);
        });

        // 搜索建議點擊
        document.addEventListener('click', function(e) {
            const suggestion = e.target.closest('.suggestion-item');
            if (suggestion) {
                const query = suggestion.getAttribute('data-suggestion');
                searchInput.value = query;
                performSearch(query);
            }
        });

        // 快速搜索標籤點擊
        document.querySelectorAll('.quick-tag').forEach(tag => {
            tag.addEventListener('click', function() {
                const query = this.getAttribute('data-query');
                searchInput.value = query;
                performSearch(query);
            });
        });

        // 搜索結果點擊
        document.addEventListener('click', function(e) {
            const resultItem = e.target.closest('.result-item');
            if (resultItem) {
                const resultId = resultItem.getAttribute('data-id');
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'navigate',
                        page: 'article',
                        articleId: resultId
                    }, '*');
                }
            }
        });

        // 搜索歷史點擊
        document.addEventListener('click', function(e) {
            const historyItem = e.target.closest('.history-item');
            if (historyItem) {
                const query = historyItem.getAttribute('data-query');
                searchInput.value = query;
                performSearch(query);
            }
        });

        // 熱門搜索點擊
        document.addEventListener('click', function(e) {
            const popularItem = e.target.closest('.popular-item');
            if (popularItem) {
                const query = popularItem.getAttribute('data-query');
                searchInput.value = query;
                performSearch(query);
            }
        });

        // 清除搜索歷史
        document.getElementById('clear-history').addEventListener('click', function() {
            if (confirm('確定要清除所有搜索歷史嗎？')) {
                searchHistory = [];
                localStorage.removeItem('search_history');
                renderHistory();
                showNotification('搜索歷史已清除');
            }
        });

        // 篩選器事件
        document.getElementById('category-filter').addEventListener('change', function() {
            const query = searchInput.value;
            if (query) {
                performSearch(query, this.value, document.getElementById('sort-filter').value);
            }
        });

        document.getElementById('sort-filter').addEventListener('change', function() {
            const query = searchInput.value;
            if (query) {
                performSearch(query, document.getElementById('category-filter').value, this.value);
            }
        });

        // 通知函數
        function showNotification(message) {
            if (window.parent !== window) {
                window.parent.postMessage({
                    type: 'notification',
                    message: message,
                    level: 'info'
                }, '*');
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderHistory();
            renderPopularSearches();

            // 從URL參數獲取搜索查詢
            const urlParams = new URLSearchParams(window.location.search);
            const query = urlParams.get('q');
            if (query) {
                searchInput.value = query;
                performSearch(query);
            }

            // 自動聚焦搜索框
            searchInput.focus();
        });

        // 監聽來自父頁面的搜索請求
        window.addEventListener('message', function(event) {
            if (event.data.type === 'search' && event.data.query) {
                searchInput.value = event.data.query;
                performSearch(event.data.query);
            }
        });
    </script>
</body>
</html>