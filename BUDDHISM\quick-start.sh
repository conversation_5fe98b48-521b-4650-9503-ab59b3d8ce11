#!/bin/bash

# ================================
# 🚀 佛教身心療癒網站 - 快速啟動腳本
# ================================

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 圖標定義
ROCKET="🚀"
CHECK="✅"
WARNING="⚠️"
ERROR="❌"
INFO="ℹ️"
GEAR="⚙️"
DATABASE="🗄️"
NETWORK="🌐"

echo -e "${BLUE}================================${NC}"
echo -e "${PURPLE}${ROCKET} 佛教身心療癒網站${NC}"
echo -e "${BLUE}快速啟動腳本${NC}"
echo -e "${BLUE}================================${NC}"
echo

# 檢查系統需求
check_requirements() {
    echo -e "${CYAN}${GEAR} 檢查系統需求...${NC}"
    
    # 檢查 Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${ERROR} Node.js 未安裝"
        echo -e "${INFO} 請安裝 Node.js 16+ 版本"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | sed 's/v//')
    REQUIRED_VERSION="16.0.0"
    
    if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
        echo -e "${ERROR} Node.js 版本過舊 (當前: $NODE_VERSION, 需要: $REQUIRED_VERSION+)"
        exit 1
    fi
    
    echo -e "${CHECK} Node.js $NODE_VERSION 已安裝"
    
    # 檢查 npm
    if ! command -v npm &> /dev/null; then
        echo -e "${ERROR} npm 未安裝"
        exit 1
    fi
    
    NPM_VERSION=$(npm --version)
    echo -e "${CHECK} npm $NPM_VERSION 已安裝"
    
    # 檢查 MongoDB
    if ! command -v mongod &> /dev/null; then
        echo -e "${WARNING} MongoDB 未安裝或未在 PATH 中"
        echo -e "${INFO} 將嘗試使用 Docker 啟動 MongoDB"
        USE_DOCKER_MONGO=true
    else
        echo -e "${CHECK} MongoDB 已安裝"
        USE_DOCKER_MONGO=false
    fi
    
    # 檢查 Docker (如果需要)
    if [ "$USE_DOCKER_MONGO" = true ]; then
        if ! command -v docker &> /dev/null; then
            echo -e "${ERROR} Docker 未安裝"
            echo -e "${INFO} 請安裝 Docker 或 MongoDB"
            exit 1
        fi
        echo -e "${CHECK} Docker 已安裝"
    fi
    
    echo
}

# 安裝依賴
install_dependencies() {
    echo -e "${CYAN}📦 安裝項目依賴...${NC}"
    
    if [ ! -f "package.json" ]; then
        echo -e "${ERROR} package.json 文件不存在"
        exit 1
    fi
    
    # 檢查是否已安裝依賴
    if [ ! -d "node_modules" ]; then
        echo -e "${INFO} 首次安裝依賴，可能需要幾分鐘..."
        npm install
    else
        echo -e "${INFO} 檢查依賴更新..."
        npm install
    fi
    
    echo -e "${CHECK} 依賴安裝完成"
    echo
}

# 設置環境變數
setup_environment() {
    echo -e "${CYAN}${GEAR} 設置環境變數...${NC}"
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            echo -e "${INFO} 複製 .env.example 到 .env"
            cp .env.example .env
            echo -e "${WARNING} 請編輯 .env 文件設置您的配置"
        else
            echo -e "${WARNING} 未找到 .env.example 文件，創建基本 .env"
            cat > .env << EOF
NODE_ENV=development
PORT=3000
MONGODB_URI=mongodb://localhost:27017/buddhist_healing
JWT_SECRET=development_secret_key_change_in_production_minimum_32_chars
JWT_REFRESH_SECRET=development_refresh_secret_key_minimum_32_chars
SMTP_HOST=smtp.ethereal.email
SMTP_PORT=587
SMTP_USER=your_ethereal_user
SMTP_PASS=your_ethereal_pass
SMTP_FROM=<EMAIL>
API_KEY=development_api_key_minimum_32_characters
EOF
        fi
    else
        echo -e "${CHECK} .env 文件已存在"
    fi
    
    echo
}

# 啟動 MongoDB
start_mongodb() {
    echo -e "${CYAN}${DATABASE} 啟動 MongoDB...${NC}"
    
    if [ "$USE_DOCKER_MONGO" = true ]; then
        echo -e "${INFO} 使用 Docker 啟動 MongoDB..."
        
        # 檢查是否已有 MongoDB 容器在運行
        if [ $(docker ps -q -f name=buddhist-healing-mongo) ]; then
            echo -e "${CHECK} MongoDB 容器已在運行"
        else
            # 檢查是否有停止的容器
            if [ $(docker ps -aq -f name=buddhist-healing-mongo) ]; then
                echo -e "${INFO} 啟動現有的 MongoDB 容器..."
                docker start buddhist-healing-mongo
            else
                echo -e "${INFO} 創建新的 MongoDB 容器..."
                docker run -d \
                    --name buddhist-healing-mongo \
                    -p 27017:27017 \
                    -v buddhist-healing-mongo-data:/data/db \
                    mongo:5.0
            fi
        fi
        
        # 等待 MongoDB 準備就緒
        echo -e "${INFO} 等待 MongoDB 啟動..."
        sleep 5
        
        # 檢查連接
        MAX_ATTEMPTS=30
        ATTEMPT=1
        while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
            if docker exec buddhist-healing-mongo mongosh --eval "db.adminCommand('ping')" &> /dev/null; then
                echo -e "${CHECK} MongoDB 已準備就緒"
                break
            fi
            
            if [ $ATTEMPT -eq $MAX_ATTEMPTS ]; then
                echo -e "${ERROR} MongoDB 啟動超時"
                exit 1
            fi
            
            echo -e "${INFO} 等待 MongoDB... ($ATTEMPT/$MAX_ATTEMPTS)"
            sleep 2
            ATTEMPT=$((ATTEMPT + 1))
        done
        
    else
        # 檢查本地 MongoDB 是否運行
        if pgrep mongod > /dev/null; then
            echo -e "${CHECK} MongoDB 已在運行"
        else
            echo -e "${INFO} 啟動本地 MongoDB..."
            # 嘗試使用 systemctl
            if command -v systemctl &> /dev/null; then
                sudo systemctl start mongod || {
                    echo -e "${WARNING} 無法使用 systemctl 啟動 MongoDB"
                    echo -e "${INFO} 請手動啟動 MongoDB"
                }
            else
                echo -e "${WARNING} 請手動啟動 MongoDB"
            fi
        fi
    fi
    
    echo
}

# 創建必要目錄
create_directories() {
    echo -e "${CYAN}📁 創建必要目錄...${NC}"
    
    directories=("logs" "uploads" "temp")
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            echo -e "${CHECK} 創建目錄: $dir"
        else
            echo -e "${INFO} 目錄已存在: $dir"
        fi
    done
    
    echo
}

# 執行健康檢查
health_check() {
    echo -e "${CYAN}🏥 執行健康檢查...${NC}"
    
    # 檢查服務器是否啟動
    MAX_ATTEMPTS=10
    ATTEMPT=1
    
    while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
        if curl -s http://localhost:3000/health &> /dev/null; then
            echo -e "${CHECK} 服務器健康檢查通過"
            break
        fi
        
        if [ $ATTEMPT -eq $MAX_ATTEMPTS ]; then
            echo -e "${WARNING} 服務器健康檢查超時"
            echo -e "${INFO} 請檢查服務器日誌"
        else
            echo -e "${INFO} 等待服務器啟動... ($ATTEMPT/$MAX_ATTEMPTS)"
            sleep 3
        fi
        
        ATTEMPT=$((ATTEMPT + 1))
    done
    
    echo
}

# 顯示啟動信息
show_startup_info() {
    echo -e "${GREEN}================================${NC}"
    echo -e "${GREEN}${CHECK} 佛教身心療癒網站啟動成功！${NC}"
    echo -e "${GREEN}================================${NC}"
    echo
    echo -e "${CYAN}📋 服務信息:${NC}"
    echo -e "  ${NETWORK} 網站首頁: ${BLUE}http://localhost:3000${NC}"
    echo -e "  ${GEAR} API 文檔: ${BLUE}http://localhost:3000/api/docs${NC}"
    echo -e "  ${DATABASE} 健康檢查: ${BLUE}http://localhost:3000/health${NC}"
    echo
    echo -e "${CYAN}🛠️ 開發命令:${NC}"
    echo -e "  ${INFO} 停止服務: ${YELLOW}Ctrl + C${NC}"
    echo -e "  ${INFO} 重啟開發: ${YELLOW}npm run dev${NC}"
    echo -e "  ${INFO} 執行測試: ${YELLOW}npm test${NC}"
    echo -e "  ${INFO} 檢查日誌: ${YELLOW}tail -f logs/combined.log${NC}"
    echo
    echo -e "${CYAN}📚 文檔連結:${NC}"
    echo -e "  ${INFO} 部署指南: DEPLOYMENT_GUIDE.md"
    echo -e "  ${INFO} API 規範: http://localhost:3000/api/docs"
    echo
    echo -e "${PURPLE}🙏 感謝使用佛教身心療癒網站！${NC}"
    echo
}

# 啟動應用
start_application() {
    echo -e "${CYAN}${ROCKET} 啟動應用服務器...${NC}"
    
    # 選擇啟動模式
    if [ "$1" = "--prod" ]; then
        echo -e "${INFO} 生產模式啟動..."
        NODE_ENV=production node app.js
    elif [ "$1" = "--pm2" ]; then
        echo -e "${INFO} PM2 模式啟動..."
        if ! command -v pm2 &> /dev/null; then
            echo -e "${WARNING} PM2 未安裝，使用 npm 安裝..."
            npm install -g pm2
        fi
        pm2 start ecosystem.config.js --env development
        pm2 logs
    else
        echo -e "${INFO} 開發模式啟動..."
        if [ -f "package.json" ] && grep -q "\"dev\"" package.json; then
            npm run dev
        else
            node app.js
        fi
    fi
}

# 清理函數
cleanup() {
    echo
    echo -e "${YELLOW}正在清理...${NC}"
    if [ "$USE_DOCKER_MONGO" = true ]; then
        echo -e "${INFO} 停止 Docker MongoDB 容器..."
        docker stop buddhist-healing-mongo &> /dev/null || true
    fi
    echo -e "${CHECK} 清理完成"
    exit 0
}

# 錯誤處理
error_exit() {
    echo -e "${ERROR} 啟動失敗: $1"
    cleanup
    exit 1
}

# 設置信號處理
trap cleanup SIGINT SIGTERM

# 主執行流程
main() {
    # 解析命令行參數
    case "$1" in
        --help|-h)
            echo "用法: $0 [選項]"
            echo
            echo "選項:"
            echo "  --help, -h     顯示幫助信息"
            echo "  --prod         生產模式啟動"
            echo "  --pm2          使用 PM2 啟動"
            echo "  --docker       使用 Docker Compose 啟動"
            echo "  --check-only   僅執行系統檢查"
            echo
            exit 0
            ;;
        --docker)
            echo -e "${CYAN}🐳 使用 Docker Compose 啟動...${NC}"
            if [ ! -f "docker-compose.yml" ]; then
                echo -e "${ERROR} docker-compose.yml 文件不存在"
                exit 1
            fi
            docker-compose up --build
            exit 0
            ;;
        --check-only)
            check_requirements
            echo -e "${CHECK} 系統檢查完成"
            exit 0
            ;;
    esac
    
    # 執行啟動流程
    check_requirements || error_exit "系統需求檢查失敗"
    install_dependencies || error_exit "依賴安裝失敗"
    setup_environment || error_exit "環境設置失敗"
    create_directories || error_exit "目錄創建失敗"
    start_mongodb || error_exit "MongoDB 啟動失敗"
    
    # 顯示啟動信息（在後台）
    show_startup_info &
    
    # 啟動應用（前台）
    start_application "$1"
}

# 檢查是否在正確的目錄中
if [ ! -f "app.js" ] && [ ! -f "package.json" ]; then
    echo -e "${ERROR} 請在項目根目錄中執行此腳本"
    exit 1
fi

# 執行主函數
main "$@"