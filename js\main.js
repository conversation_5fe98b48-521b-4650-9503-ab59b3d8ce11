/**
 * 澄源閱讀 - 主應用程序
 * 負責應用程序的初始化和全局功能管理
 */

class ChengyuanApp {
    constructor() {
        this.isInitialized = false;
        this.currentPage = null;
        this.theme = 'light';
        this.config = {
            animationDuration: 300,
            debounceDelay: 300,
            scrollThreshold: 100,
            maxRetries: 3,
            cacheTimeout: 5 * 60 * 1000 // 5分鐘
        };
        
        this.cache = new Map();
        this.eventListeners = new Map();
        this.pageHistory = [];
        
        this.init();
    }

    /**
     * 初始化應用程序
     */
    async init() {
        if (this.isInitialized) return;
        
        try {
            // 等待DOM載入完成
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }

            // 初始化各個模組
            await this.initTheme();
            this.initNavigation();
            this.initIframe();
            this.initNotificationSystem();
            this.initBackToTop();
            this.initKeyboardShortcuts();
            this.initErrorHandling();
            this.initPerformanceMonitoring();
            
            // 綁定全局事件
            this.bindGlobalEvents();
            
            // 檢查認證狀態並載入適當頁面
            this.handleInitialRoute();
            
            this.isInitialized = true;
            console.log('ChengyuanApp initialized successfully');
            
            // 隱藏載入動畫
            this.hideLoader();
            
        } catch (error) {
            console.error('Failed to initialize app:', error);
            this.handleInitError(error);
        }
    }

    /**
     * 初始化主題系統
     */
    async initTheme() {
        // 從存儲中獲取主題設置
        this.theme = Utils.Storage.get('theme', 'light');
        
        // 檢查系統主題偏好
        if (this.theme === 'auto') {
            this.theme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        }
        
        // 應用主題
        this.applyTheme(this.theme);
        
        // 監聽系統主題變化
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (Utils.Storage.get('theme') === 'auto') {
                this.theme = e.matches ? 'dark' : 'light';
                this.applyTheme(this.theme);
            }
        });
    }

    /**
     * 應用主題
     * @param {string} theme - 主題名稱
     */
    applyTheme(theme) {
        document.body.setAttribute('data-theme', theme);
        
        // 更新主題切換按鈕圖標
        const themeToggle = Utils.DOM.get('#theme-toggle');
        if (themeToggle) {
            const icon = themeToggle.querySelector('i');
            if (icon) {
                icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        }
        
        // 更新meta主題顏色
        let metaThemeColor = Utils.DOM.get('meta[name="theme-color"]');
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.name = 'theme-color';
            document.head.appendChild(metaThemeColor);
        }
        metaThemeColor.content = theme === 'dark' ? '#1a1a1a' : '#ffffff';
    }

    /**
     * 初始化導航系統
     */
    initNavigation() {
        // 綁定導航點擊事件
        document.addEventListener('click', (e) => {
            const navElement = e.target.closest('[data-page]');
            if (navElement) {
                e.preventDefault();
                const pageName = navElement.getAttribute('data-page');
                this.navigateToPage(pageName);
            }
        });

        // 主題切換
        const themeToggle = Utils.DOM.get('#theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // 移動端導航
        const navToggle = Utils.DOM.get('#nav-toggle');
        const sidebar = Utils.DOM.get('#sidebar');
        const overlay = Utils.DOM.get('#overlay');

        if (navToggle) {
            navToggle.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }

        if (overlay) {
            overlay.addEventListener('click', () => {
                this.closeSidebar();
            });
        }

        // 登出按鈕
        const logoutBtn = Utils.DOM.get('#logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.logout();
            });
        }
    }

    /**
     * 初始化iframe系統
     */
    initIframe() {
        const iframe = Utils.DOM.get('#content-frame');
        if (!iframe) return;

        // 監聽iframe載入事件
        iframe.addEventListener('load', () => {
            this.handleIframeLoad(iframe);
        });

        // 監聽來自iframe的消息
        window.addEventListener('message', (event) => {
            this.handleIframeMessage(event);
        });

        // iframe錯誤處理
        iframe.addEventListener('error', () => {
            this.handleIframeError();
        });
    }

    /**
     * 處理iframe載入完成
     * @param {HTMLIFrameElement} iframe - iframe元素
     */
    handleIframeLoad(iframe) {
        try {
            // 更新載入狀態
            this.hidePageLoader();
            
            // 記錄頁面訪問
            const src = iframe.src;
            if (src) {
                this.trackPageView(src);
            }
            
        } catch (error) {
            console.warn('Error handling iframe load:', error);
        }
    }

    /**
     * 處理來自iframe的消息
     * @param {MessageEvent} event - 消息事件
     */
    handleIframeMessage(event) {
        const { type, data } = event.data;
        
        switch (type) {
            case 'login_success':
                this.handleLoginSuccess(data);
                break;
            case 'navigate':
                this.navigateToPage(data.page, data);
                break;
            case 'notification':
                this.showNotification(data.message, data.level);
                break;
            case 'theme_change':
                this.applyTheme(data.theme);
                break;
            case 'scroll_to_top':
                this.scrollToTop();
                break;
            case 'update_title':
                this.updatePageTitle(data.title);
                break;
            default:
                console.log('Unknown message type:', type, data);
        }
    }

    /**
     * 處理iframe錯誤
     */
    handleIframeError() {
        this.showNotification('頁面載入失敗，請重試', 'error');
        this.hidePageLoader();
    }

    /**
     * 初始化通知系統
     */
    initNotificationSystem() {
        // 確保通知容器存在
        let container = Utils.DOM.get('#notification-container');
        if (!container) {
            container = Utils.DOM.create('div', {
                id: 'notification-container',
                className: 'notification-container'
            });
            document.body.appendChild(container);
        }

        // 全局通知函數
        window.showNotification = (message, type = 'info', duration = 3000) => {
            this.showNotification(message, type, duration);
        };
    }

    /**
     * 顯示通知
     * @param {string} message - 通知消息
     * @param {string} type - 通知類型
     * @param {number} duration - 顯示時長
     */
    showNotification(message, type = 'info', duration = 3000) {
        const container = Utils.DOM.get('#notification-container');
        if (!container) return;

        const notification = Utils.DOM.create('div', {
            className: `notification ${type}`,
            innerHTML: `
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
                <button class="notification-close">
                    <i class="fas fa-times"></i>
                </button>
            `
        });

        // 添加到容器
        container.appendChild(notification);

        // 動畫效果
        requestAnimationFrame(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        });

        // 綁定關閉按鈕
        const closeBtn = notification.querySelector('.notification-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.removeNotification(notification);
            });
        }

        // 自動關閉
        if (duration > 0) {
            setTimeout(() => {
                this.removeNotification(notification);
            }, duration);
        }

        return notification;
    }

    /**
     * 移除通知
     * @param {HTMLElement} notification - 通知元素
     */
    removeNotification(notification) {
        if (!notification || !notification.parentNode) return;

        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    /**
     * 獲取通知圖標
     * @param {string} type - 通知類型
     * @returns {string}
     */
    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-triangle',
            warning: 'exclamation-circle',
            info: 'info-circle'
        };
        return icons[type] || icons.info;
    }

    /**
     * 初始化返回頂部按鈕
     */
    initBackToTop() {
        const backToTopBtn = Utils.DOM.get('#back-to-top');
        if (!backToTopBtn) return;

        // 監聽滾動事件
        const handleScroll = Utils.Performance.throttle(() => {
            const iframe = Utils.DOM.get('#content-frame');
            if (iframe && iframe.contentWindow) {
                try {
                    const scrollTop = iframe.contentWindow.pageYOffset || 
                                    iframe.contentDocument.documentElement.scrollTop || 
                                    iframe.contentDocument.body.scrollTop;
                    
                    if (scrollTop > this.config.scrollThreshold) {
                        backToTopBtn.classList.add('show');
                    } else {
                        backToTopBtn.classList.remove('show');
                    }
                } catch (error) {
                    // 跨域限制時使用主窗口滾動
                    if (window.pageYOffset > this.config.scrollThreshold) {
                        backToTopBtn.classList.add('show');
                    } else {
                        backToTopBtn.classList.remove('show');
                    }
                }
            }
        }, 100);

        window.addEventListener('scroll', handleScroll);
        
        // 點擊返回頂部
        backToTopBtn.addEventListener('click', () => {
            this.scrollToTop();
        });
    }

    /**
     * 初始化鍵盤快捷鍵
     */
    initKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K: 搜索
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                this.navigateToPage('search');
            }
            
            // Escape: 關閉側邊欄/模態框
            if (e.key === 'Escape') {
                this.closeSidebar();
                this.closeModals();
            }
            
            // Alt + 數字: 快速導航
            if (e.altKey && /^[1-9]$/.test(e.key)) {
                e.preventDefault();
                const pages = ['home', 'dharma', 'healing', 'research', 'search', 'favorites'];
                const pageIndex = parseInt(e.key) - 1;
                if (pages[pageIndex]) {
                    this.navigateToPage(pages[pageIndex]);
                }
            }
        });
    }

    /**
     * 初始化錯誤處理
     */
    initErrorHandling() {
        // 全局錯誤處理
        window.addEventListener('error', (e) => {
            console.error('Global error:', e.error);
            this.handleGlobalError(e.error);
        });

        // Promise 拒絕處理
        window.addEventListener('unhandledrejection', (e) => {
            console.error('Unhandled promise rejection:', e.reason);
            this.handleGlobalError(e.reason);
        });
    }

    /**
     * 初始化性能監控
     */
    initPerformanceMonitoring() {
        // 頁面載入性能
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                if (perfData) {
                    console.log('Page load time:', Math.round(perfData.loadEventEnd - perfData.fetchStart));
                }
            }, 0);
        });

        // 內存使用監控（如果支持）
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
                    console.warn('High memory usage detected');
                    this.optimizeMemory();
                }
            }, 30000);
        }
    }

    /**
     * 綁定全局事件
     */
    bindGlobalEvents() {
        // 視窗大小變化
        window.addEventListener('resize', Utils.Performance.debounce(() => {
            this.handleResize();
        }, 250));

        // 網路狀態變化
        window.addEventListener('online', () => {
            this.showNotification('網路連接已恢復', 'success');
        });

        window.addEventListener('offline', () => {
            this.showNotification('網路連接已斷開', 'warning');
        });

        // 頁面可見性變化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.handlePageHide();
            } else {
                this.handlePageShow();
            }
        });
    }

    /**
     * 處理初始路由
     */
    handleInitialRoute() {
        const isLoggedIn = window.Auth && window.Auth.isLoggedIn();
        
        if (isLoggedIn) {
            // 檢查URL參數
            const urlParams = Utils.URL_Utils.parseQuery();
            const targetPage = urlParams.page || 'home';
            this.navigateToPage(targetPage);
        } else {
            this.navigateToPage('login');
        }
    }

    /**
     * 導航到頁面
     * @param {string} pageName - 頁面名稱
     * @param {Object} options - 選項
     */
    navigateToPage(pageName, options = {}) {
        if (!pageName) return;

        // 檢查權限
        if (pageName !== 'login' && !window.Auth?.isLoggedIn()) {
            this.navigateToPage('login');
            return;
        }

        const iframe = Utils.DOM.get('#content-frame');
        if (!iframe) return;

        // 顯示載入狀態
        this.showPageLoader();

        // 構建頁面URL
        const pageUrl = `pages/${pageName}.html`;
        
        // 添加查詢參數
        if (options.query) {
            const queryString = Utils.URL_Utils.buildQuery(options.query);
            pageUrl += '?' + queryString;
        }

        // 載入頁面
        iframe.src = pageUrl;
        
        // 更新導航狀態
        this.updateActiveNav(pageName);
        
        // 記錄頁面歷史
        this.pageHistory.push({
            page: pageName,
            timestamp: Date.now(),
            options: options
        });

        // 更新當前頁面
        this.currentPage = pageName;
        
        // 關閉側邊欄（移動端）
        this.closeSidebar();
        
        // 更新URL（不刷新頁面）
        Utils.URL_Utils.updateParams({ page: pageName }, true);
    }

    /**
     * 更新導航活躍狀態
     * @param {string} pageName - 頁面名稱
     */
    updateActiveNav(pageName) {
        // 清除所有活躍狀態
        Utils.DOM.getAll('.nav-link, .menu-link').forEach(link => {
            link.classList.remove('active');
        });
        
        // 設置當前頁面活躍狀態
        Utils.DOM.getAll(`[data-page="${pageName}"]`).forEach(link => {
            link.classList.add('active');
        });
    }

    /**
     * 切換主題
     */
    toggleTheme() {
        const newTheme = this.theme === 'dark' ? 'light' : 'dark';
        this.theme = newTheme;
        this.applyTheme(newTheme);
        Utils.Storage.set('theme', newTheme);
        
        this.showNotification(`已切換到${newTheme === 'dark' ? '深色' : '淺色'}模式`);
    }

    /**
     * 切換側邊欄
     */
    toggleSidebar() {
        const sidebar = Utils.DOM.get('#sidebar');
        const overlay = Utils.DOM.get('#overlay');
        
        if (sidebar && overlay) {
            sidebar.classList.toggle('active');
            overlay.classList.toggle('active');
        }
    }

    /**
     * 關閉側邊欄
     */
    closeSidebar() {
        const sidebar = Utils.DOM.get('#sidebar');
        const overlay = Utils.DOM.get('#overlay');
        
        if (sidebar && overlay) {
            sidebar.classList.remove('active');
            overlay.classList.remove('active');
        }
    }

    /**
     * 關閉模態框
     */
    closeModals() {
        Utils.DOM.getAll('.modal.show').forEach(modal => {
            modal.classList.remove('show');
        });
    }

    /**
     * 登出
     */
    logout() {
        if (window.Auth) {
            window.Auth.logout();
        }
        this.navigateToPage('login');
        this.showNotification('已成功登出');
    }

    /**
     * 滾動到頂部
     */
    scrollToTop() {
        const iframe = Utils.DOM.get('#content-frame');
        if (iframe && iframe.contentWindow) {
            try {
                iframe.contentWindow.scrollTo({ top: 0, behavior: 'smooth' });
            } catch (error) {
                // 跨域時回退到主窗口滾動
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        }
    }

    /**
     * 處理登錄成功
     * @param {Object} data - 登錄數據
     */
    handleLoginSuccess(data) {
        this.showNotification('登錄成功，歡迎使用澄源閱讀！', 'success');
        this.navigateToPage('home');
    }

    /**
     * 更新頁面標題
     * @param {string} title - 新標題
     */
    updatePageTitle(title) {
        if (title) {
            document.title = `${title} - ${Utils.CONSTANTS.APP_NAME}`;
        }
    }

    /**
     * 顯示頁面載入器
     */
    showPageLoader() {
        // 可以在這裡添加頁面載入動畫
    }

    /**
     * 隱藏頁面載入器
     */
    hidePageLoader() {
        // 可以在這裡隱藏頁面載入動畫
    }

    /**
     * 隱藏主載入器
     */
    hideLoader() {
        const loader = Utils.DOM.get('#loader');
        if (loader) {
            setTimeout(() => {
                loader.style.opacity = '0';
                setTimeout(() => {
                    loader.style.display = 'none';
                }, 300);
            }, 500);
        }
    }

    /**
     * 處理視窗大小變化
     */
    handleResize() {
        // 更新視窗信息
        const viewport = Utils.Device.getViewportSize();
        const breakpoint = Utils.Device.getBreakpoint();
        
        // 在小屏幕時自動關閉側邊欄
        if (breakpoint === 'xs' || breakpoint === 'sm') {
            this.closeSidebar();
        }
    }

    /**
     * 處理頁面隱藏
     */
    handlePageHide() {
        // 暫停非必要的更新
    }

    /**
     * 處理頁面顯示
     */
    handlePageShow() {
        // 恢復更新
        if (window.Auth && window.Auth.isLoggedIn()) {
            window.Auth.updateLastActivity();
        }
    }

    /**
     * 處理全局錯誤
     * @param {Error} error - 錯誤對象
     */
    handleGlobalError(error) {
        console.error('Application error:', error);
        
        // 顯示用戶友好的錯誤消息
        this.showNotification('系統出現異常，請刷新頁面重試', 'error');
    }

    /**
     * 處理初始化錯誤
     * @param {Error} error - 錯誤對象
     */
    handleInitError(error) {
        document.body.innerHTML = `
            <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                <h1>應用程序載入失敗</h1>
                <p>抱歉，澄源閱讀無法正常載入。請刷新頁面重試。</p>
                <button onclick="location.reload()" style="padding: 10px 20px; font-size: 16px; margin-top: 20px;">
                    重新載入
                </button>
            </div>
        `;
    }

    /**
     * 記錄頁面訪問
     * @param {string} url - 頁面URL
     */
    trackPageView(url) {
        // 可以在這裡添加分析代碼
        console.log('Page view:', url);
    }

    /**
     * 優化內存使用
     */
    optimizeMemory() {
        // 清理舊的緩存
        this.cache.clear();
        
        // 清理頁面歷史（只保留最近10個）
        if (this.pageHistory.length > 10) {
            this.pageHistory = this.pageHistory.slice(-10);
        }
    }

    /**
     * 獲取應用狀態
     * @returns {Object}
     */
    getAppState() {
        return {
            isInitialized: this.isInitialized,
            currentPage: this.currentPage,
            theme: this.theme,
            pageHistory: this.pageHistory,
            cacheSize: this.cache.size,
            viewport: Utils.Device.getViewportSize(),
            breakpoint: Utils.Device.getBreakpoint(),
            authStatus: window.Auth ? window.Auth.getAuthSummary() : null
        };
    }
}

// 創建並初始化應用程序
window.ChengyuanApp = new ChengyuanApp();

// 為了方便調試，將應用實例暴露到全局
window.App = window.ChengyuanApp;

console.log('ChengyuanApp main script loaded');