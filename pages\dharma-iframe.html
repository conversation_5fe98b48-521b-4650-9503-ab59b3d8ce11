<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>佛學智慧 - 澄源書坊</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            background: #f8f6f1;
            color: #333;
            overflow-x: hidden;
        }

        /* 主容器 */
        .main-container {
            display: flex;
            min-height: 100vh;
            max-width: 1400px;
            margin: 0 auto;
        }

        /* 左側導航欄 */
        .sidebar {
            width: 320px;
            background: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            overflow-y: auto;
            position: fixed;
            height: 100vh;
            z-index: 100;
            transition: transform 0.3s ease;
        }

        .sidebar.hidden {
            transform: translateX(-100%);
        }

        .sidebar-header {
            background: linear-gradient(135deg, #c9a876, #b8966d);
            color: white;
            padding: 30px 25px;
            text-align: center;
            position: relative;
        }

        .sidebar-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 8px;
            letter-spacing: 2px;
        }

        .sidebar-subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .toggle-btn {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        /* 分類導航 */
        .category-nav {
            padding: 20px 0;
        }

        .nav-section {
            margin-bottom: 15px;
        }

        .nav-title {
            padding: 10px 25px;
            font-size: 0.9rem;
            font-weight: 600;
            color: #888;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .nav-list {
            list-style: none;
        }

        .nav-item {
            border-bottom: 1px solid #f5f5f5;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: #555;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .nav-link:hover {
            background: #f8f9fa;
            color: #c9a876;
            padding-left: 30px;
        }

        .nav-link.active {
            background: linear-gradient(90deg, #c9a876, #b8966d);
            color: white;
            font-weight: 500;
        }

        .nav-link.active:hover {
            padding-left: 25px;
        }

        .nav-icon {
            margin-right: 12px;
            font-size: 1rem;
            width: 20px;
            text-align: center;
        }

        .nav-text {
            flex: 1;
        }

        .nav-count {
            background: #e9ecef;
            color: #6c757d;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .nav-link.active .nav-count {
            background: rgba(255,255,255,0.3);
            color: white;
        }

        /* 搜索框 */
        .search-section {
            padding: 20px 25px;
            border-bottom: 1px solid #f0f0f0;
        }

        .search-box {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 12px 40px 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 0.9rem;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #c9a876;
        }

        .search-btn {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            background: #c9a876;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .search-btn:hover {
            background: #b8966d;
        }

        /* 主內容區域 */
        .main-content {
            flex: 1;
            margin-left: 320px;
            transition: margin-left 0.3s ease;
        }

        .main-content.expanded {
            margin-left: 0;
        }

        /* 頂部工具欄 */
        .toolbar {
            background: white;
            padding: 20px 30px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .mobile-toggle {
            display: none;
            background: #c9a876;
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 8px;
            cursor: pointer;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            color: #666;
        }

        .breadcrumb-item {
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .breadcrumb-item:hover {
            color: #c9a876;
        }

        .breadcrumb-separator {
            color: #ccc;
        }

        .toolbar-right {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .view-toggle {
            display: flex;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            overflow: hidden;
        }

        .view-btn {
            background: white;
            border: none;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #666;
        }

        .view-btn.active {
            background: #c9a876;
            color: white;
        }

        /* iframe 容器 */
        .content-frame-container {
            background: white;
            border-radius: 15px;
            margin: 20px 30px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            min-height: calc(100vh - 200px);
        }

        .content-frame {
            width: 100%;
            height: calc(100vh - 200px);
            border: none;
            display: block;
        }

        /* 載入動畫 */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #c9a876;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .mobile-toggle {
                display: block;
            }

            .content-frame-container {
                margin: 15px;
                border-radius: 10px;
            }

            .toolbar {
                padding: 15px;
            }

            .breadcrumb {
                font-size: 0.8rem;
            }
        }

        @media (max-width: 480px) {
            .sidebar-header {
                padding: 20px 15px;
            }

            .toolbar {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }

            .toolbar-left,
            .toolbar-right {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 左側導航欄 -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2 class="sidebar-title">佛學智慧</h2>
                <p class="sidebar-subtitle">探索古老智慧的寶藏</p>
                <button class="toggle-btn" id="sidebar-toggle">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>

            <!-- 搜索區 -->
            <div class="search-section">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="搜索佛學內容..." id="search-input">
                    <button class="search-btn" id="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>

            <!-- 分類導航 -->
            <div class="category-nav">
                <div class="nav-section">
                    <div class="nav-title">主要分類</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a class="nav-link active" data-category="all" data-page="dharma-articles">
                                <i class="nav-icon fas fa-th-large"></i>
                                <span class="nav-text">全部文章</span>
                                <span class="nav-count" id="count-all">20</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-category="meditation" data-page="dharma-meditation">
                                <i class="nav-icon fas fa-meditation"></i>
                                <span class="nav-text">禪修指導</span>
                                <span class="nav-count" id="count-meditation">3</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-category="philosophy" data-page="dharma-philosophy">
                                <i class="nav-icon fas fa-brain"></i>
                                <span class="nav-text">佛教哲學</span>
                                <span class="nav-count" id="count-philosophy">5</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-category="practice" data-page="dharma-practice">
                                <i class="nav-icon fas fa-hands-praying"></i>
                                <span class="nav-text">修行方法</span>
                                <span class="nav-count" id="count-practice">6</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-category="sutras" data-page="dharma-sutras">
                                <i class="nav-icon fas fa-scroll"></i>
                                <span class="nav-text">經典解讀</span>
                                <span class="nav-count" id="count-sutras">4</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-category="masters" data-page="dharma-masters">
                                <i class="nav-icon fas fa-user-graduate"></i>
                                <span class="nav-text">大師教導</span>
                                <span class="nav-count" id="count-masters">3</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="nav-section">
                    <div class="nav-title">進階分類</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a class="nav-link" data-category="ethics" data-page="dharma-ethics">
                                <i class="nav-icon fas fa-balance-scale"></i>
                                <span class="nav-text">佛教倫理</span>
                                <span class="nav-count" id="count-ethics">2</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-category="tradition" data-page="dharma-tradition">
                                <i class="nav-icon fas fa-university"></i>
                                <span class="nav-text">傳承體系</span>
                                <span class="nav-count" id="count-tradition">1</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-category="stories" data-page="dharma-stories">
                                <i class="nav-icon fas fa-book-open"></i>
                                <span class="nav-text">佛教故事</span>
                                <span class="nav-count" id="count-stories">0</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="nav-section">
                    <div class="nav-title">難度分級</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a class="nav-link" data-difficulty="beginner" data-page="dharma-beginner">
                                <i class="nav-icon fas fa-seedling"></i>
                                <span class="nav-text">入門級</span>
                                <span class="nav-count">7</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-difficulty="intermediate" data-page="dharma-intermediate">
                                <i class="nav-icon fas fa-tree"></i>
                                <span class="nav-text">進階級</span>
                                <span class="nav-count">8</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-difficulty="advanced" data-page="dharma-advanced">
                                <i class="nav-icon fas fa-mountain"></i>
                                <span class="nav-text">高級</span>
                                <span class="nav-count">5</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- 主內容區 -->
        <main class="main-content" id="main-content">
            <!-- 頂部工具欄 -->
            <div class="toolbar">
                <div class="toolbar-left">
                    <button class="mobile-toggle" id="mobile-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="breadcrumb">
                        <span class="breadcrumb-item" data-page="dharma-articles">佛學智慧</span>
                        <span class="breadcrumb-separator">></span>
                        <span class="breadcrumb-item active" id="current-category">全部文章</span>
                    </div>
                </div>
                <div class="toolbar-right">
                    <div class="view-toggle">
                        <button class="view-btn active" data-view="grid">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="view-btn" data-view="list">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- iframe 內容區 -->
            <div class="content-frame-container">
                <iframe class="content-frame" id="content-frame" src="dharma-articles.html"></iframe>
                <div class="loading-overlay" id="loading-overlay">
                    <div class="loading-spinner"></div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 全局變量
        let currentPage = 'dharma-articles';
        let currentCategory = 'all';
        let currentView = 'grid';

        // DOM 元素
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');
        const contentFrame = document.getElementById('content-frame');
        const loadingOverlay = document.getElementById('loading-overlay');
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const mobileToggle = document.getElementById('mobile-toggle');

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeNavigation();
            initializeSearch();
            initializeResponsive();
            loadDefaultPage();
        });

        // 導航初始化
        function initializeNavigation() {
            // 分類導航點擊
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 移除所有活躍狀態
                    document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 獲取頁面信息
                    const page = this.getAttribute('data-page');
                    const category = this.getAttribute('data-category') || '';
                    const difficulty = this.getAttribute('data-difficulty') || '';
                    
                    // 更新麵包屑
                    const categoryText = this.querySelector('.nav-text').textContent;
                    document.getElementById('current-category').textContent = categoryText;
                    
                    // 載入頁面
                    if (page) {
                        loadPage(page, { category, difficulty });
                    }
                });
            });

            // 側邊欄切換
            sidebarToggle.addEventListener('click', toggleSidebar);
            mobileToggle.addEventListener('click', toggleMobileSidebar);

            // 視圖切換
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    
                    currentView = this.getAttribute('data-view');
                    updateViewMode();
                });
            });
        }

        // 載入頁面
        function loadPage(pageName, options = {}) {
            showLoading();
            
            let url = `${pageName}.html`;
            
            // 添加查詢參數
            const params = new URLSearchParams();
            if (options.category) params.append('category', options.category);
            if (options.difficulty) params.append('difficulty', options.difficulty);
            if (options.search) params.append('search', options.search);
            if (currentView) params.append('view', currentView);
            
            if (params.toString()) {
                url += '?' + params.toString();
            }
            
            currentPage = pageName;
            contentFrame.src = url;
        }

        // 搜索功能
        function initializeSearch() {
            const searchInput = document.getElementById('search-input');
            const searchBtn = document.getElementById('search-btn');
            
            function performSearch() {
                const query = searchInput.value.trim();
                if (query) {
                    loadPage('dharma-search', { search: query });
                    
                    // 更新導航狀態
                    document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                    document.getElementById('current-category').textContent = `搜索: ${query}`;
                }
            }
            
            searchBtn.addEventListener('click', performSearch);
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        }

        // 響應式處理
        function initializeResponsive() {
            // 檢查屏幕大小
            function checkScreenSize() {
                if (window.innerWidth <= 768) {
                    sidebar.classList.add('hidden');
                    mainContent.classList.add('expanded');
                } else {
                    sidebar.classList.remove('mobile-open');
                }
            }
            
            window.addEventListener('resize', checkScreenSize);
            checkScreenSize();
        }

        // 側邊欄切換
        function toggleSidebar() {
            if (window.innerWidth > 768) {
                sidebar.classList.toggle('hidden');
                mainContent.classList.toggle('expanded');
                
                const icon = sidebarToggle.querySelector('i');
                if (sidebar.classList.contains('hidden')) {
                    icon.className = 'fas fa-chevron-right';
                } else {
                    icon.className = 'fas fa-chevron-left';
                }
            }
        }

        function toggleMobileSidebar() {
            sidebar.classList.toggle('mobile-open');
        }

        // 載入狀態
        function showLoading() {
            loadingOverlay.style.display = 'flex';
        }

        function hideLoading() {
            loadingOverlay.style.display = 'none';
        }

        // iframe 載入完成
        contentFrame.addEventListener('load', function() {
            hideLoading();
            
            // 設置 iframe 內容的響應式高度
            try {
                const iframeDoc = contentFrame.contentDocument || contentFrame.contentWindow.document;
                const height = Math.max(
                    iframeDoc.body.scrollHeight,
                    iframeDoc.documentElement.scrollHeight
                );
                contentFrame.style.height = Math.max(height, 600) + 'px';
            } catch (e) {
                // 跨域限制時的處理
                contentFrame.style.height = 'calc(100vh - 200px)';
            }
        });

        // 監聽來自 iframe 的消息
        window.addEventListener('message', function(event) {
            const data = event.data;
            
            switch(data.type) {
                case 'navigate':
                    if (data.page) {
                        loadPage(data.page, data.options || {});
                    }
                    break;
                    
                case 'article_view':
                    // 處理文章查看
                    if (window.parent !== window) {
                        window.parent.postMessage({
                            type: 'navigate',
                            page: 'article',
                            articleId: data.articleId
                        }, '*');
                    }
                    break;
                    
                case 'update_counts':
                    // 更新分類計數
                    if (data.counts) {
                        Object.keys(data.counts).forEach(category => {
                            const countElement = document.getElementById(`count-${category}`);
                            if (countElement) {
                                countElement.textContent = data.counts[category];
                            }
                        });
                    }
                    break;
            }
        });

        // 更新視圖模式
        function updateViewMode() {
            contentFrame.contentWindow.postMessage({
                type: 'change_view',
                view: currentView
            }, '*');
        }

        // 載入預設頁面
        function loadDefaultPage() {
            loadPage('dharma-articles');
        }

        // 點擊遮罩關閉側邊欄（移動端）
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768 && 
                sidebar.classList.contains('mobile-open') && 
                !sidebar.contains(e.target) && 
                !mobileToggle.contains(e.target)) {
                sidebar.classList.remove('mobile-open');
            }
        });
    </script>
</body>
</html>