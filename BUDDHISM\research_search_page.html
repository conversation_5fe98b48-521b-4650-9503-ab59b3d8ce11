<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>研究搜尋 - 佛教身心療癒網站</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2C3E50;
            --secondary-color: #F39C12;
            --accent-color: #E8F4F8;
            --text-color: #2C3E50;
            --light-bg: #F8F9FA;
            --success-color: #27AE60;
            --info-color: #3498DB;
        }

        body {
            font-family: 'Noto Sans CJK TC', 'Microsoft JhengHei', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            padding: 120px 0 80px;
        }

        .navbar-custom {
            background: rgba(44, 62, 80, 0.95);
            backdrop-filter: blur(10px);
        }

        .nav-link {
            color: white !important;
            font-weight: 500;
            margin: 0 10px;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--secondary-color) !important;
        }

        .search-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-top: -60px;
            position: relative;
            z-index: 10;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .search-input {
            border: 2px solid #ddd;
            border-radius: 25px;
            padding: 15px 60px 15px 25px;
            font-size: 1.1rem;
            width: 100%;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(243, 156, 18, 0.25);
            outline: none;
        }

        .search-btn {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--secondary-color);
            border: none;
            border-radius: 20px;
            width: 50px;
            height: 40px;
            color: white;
            transition: all 0.3s ease;
        }

        .research-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid var(--info-color);
        }

        .research-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .research-title {
            color: var(--primary-color);
            font-weight: bold;
            font-size: 1.2rem;
            margin-bottom: 10px;
        }

        .research-meta {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            font-size: 0.9rem;
            color: #666;
            flex-wrap: wrap;
        }

        .research-abstract {
            background: var(--light-bg);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 3px solid var(--info-color);
        }

        .research-tags {
            margin-top: 15px;
        }

        .tag {
            background: var(--accent-color);
            color: var(--primary-color);
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            margin-right: 8px;
            margin-bottom: 5px;
            display: inline-block;
        }

        .filter-section {
            background: var(--light-bg);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .filter-btn {
            background: white;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            padding: 8px 20px;
            border-radius: 20px;
            margin: 5px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            cursor: pointer;
        }

        .filter-btn.active,
        .filter-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .category-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .category-card:hover {
            transform: translateY(-5px);
            border-color: var(--secondary-color);
        }

        .category-icon {
            font-size: 2.5rem;
            color: var(--secondary-color);
            margin-bottom: 15px;
        }

        .results-summary {
            background: var(--info-color);
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            margin-bottom: 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .citation-format {
            background: var(--light-bg);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            border-left: 3px solid var(--success-color);
        }

        @media (max-width: 768px) {
            .page-header {
                padding: 100px 0 60px;
            }
            .search-container {
                margin-top: -40px;
                padding: 25px;
            }
            .research-meta {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html" style="color: var(--secondary-color);">
                <i class="fas fa-lotus me-2"></i>佛教身心療癒
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">主頁</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">關於我們</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            課程
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="courses-goals.html">課程目標</a></li>
                            <li><a class="dropdown-item" href="courses-weekly.html">每週主題</a></li>
                            <li><a class="dropdown-item" href="courses-methods.html">進行方式</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            經文選讀
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="scriptures-search.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="scriptures-methods.html">療癒方法</a></li>
                            <li><a class="dropdown-item" href="scriptures-theory.html">療癒理論</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown">
                            身心療癒研究
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="research-search.html">搜尋</a></li>
                            <li><a class="dropdown-item" href="research-theory.html">療癒理論</a></li>
                            <li><a class="dropdown-item" href="research-methods.html">療癒方法</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            藏傳佛教
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="tibetan-theory.html">藏醫理論</a></li>
                            <li><a class="dropdown-item" href="tibetan-practice.html">藏醫實習</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            海外實習
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="overseas-members.html">成員</a></li>
                            <li><a class="dropdown-item" href="overseas-research.html">研究成果</a></li>
                            <li><a class="dropdown-item" href="overseas-future.html">未來計畫</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="workshop.html">工作坊</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="membership.html">加入會員</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 頁面標題 -->
    <section class="page-header">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-4">研究搜尋</h1>
                    <p class="lead">探索佛教身心療癒的學術研究與科學證據</p>
                </div>
            </div>
        </div>
    </section>

    <main>
        <!-- 搜尋容器 -->
        <section class="py-5">
            <div class="container">
                <div class="search-container">
                    <div class="position-relative mb-3">
                        <input type="text" class="search-input" placeholder="搜尋研究文獻、作者、關鍵字..." id="searchInput">
                        <button class="search-btn" onclick="performSearch()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- 研究分類 -->
                <div class="category-grid">
                    <div class="category-card" onclick="quickSearch('正念冥想')">
                        <div class="category-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h6 class="fw-bold">正念冥想研究</h6>
                        <p class="small text-muted mb-0">136 篇研究</p>
                    </div>
                    <div class="category-card" onclick="quickSearch('慈悲療法')">
                        <div class="category-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <h6 class="fw-bold">慈悲療法研究</h6>
                        <p class="small text-muted mb-0">89 篇研究</p>
                    </div>
                    <div class="category-card" onclick="quickSearch('禪修神經科學')">
                        <div class="category-icon">
                            <i class="fas fa-head-side-brain"></i>
                        </div>
                        <h6 class="fw-bold">禪修神經科學</h6>
                        <p class="small text-muted mb-0">124 篇研究</p>
                    </div>
                    <div class="category-card" onclick="quickSearch('佛教心理學')">
                        <div class="category-icon">
                            <i class="fas fa-user-md"></i>
                        </div>
                        <h6 class="fw-bold">佛教心理學</h6>
                        <p class="small text-muted mb-0">78 篇研究</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 搜尋結果 -->
        <section class="py-3" id="searchResults" style="display: none;">
            <div class="container">
                <!-- 篩選器 -->
                <div class="filter-section">
                    <h6 class="fw-bold mb-3">篩選研究類型</h6>
                    <div class="d-flex flex-wrap">
                        <button class="filter-btn active" data-filter="all">全部</button>
                        <button class="filter-btn" data-filter="randomized">隨機對照試驗</button>
                        <button class="filter-btn" data-filter="meta-analysis">統合分析</button>
                        <button class="filter-btn" data-filter="case-study">個案研究</button>
                        <button class="filter-btn" data-filter="review">文獻回顧</button>
                    </div>
                </div>

                <!-- 結果摘要 -->
                <div class="results-summary">
                    <span>找到 <strong id="resultCount">58</strong> 篇相關研究</span>
                    <select class="form-select w-auto" style="background: rgba(255,255,255,0.2); border: none; color: white;">
                        <option>按相關性排序</option>
                        <option>按發表時間排序</option>
                        <option>按引用次數排序</option>
                        <option>按影響因子排序</option>
                    </select>
                </div>

                <!-- 研究列表 -->
                <div id="resultsList">
                    <!-- 研究項目 1 -->
                    <div class="research-card">
                        <div class="research-title">正念認知療法對憂鬱症復發預防的隨機對照研究</div>
                        <div class="research-meta">
                            <span><i class="fas fa-user me-1"></i>Segal, Z. V., et al.</span>
                            <span><i class="fas fa-calendar me-1"></i>2020</span>
                            <span><i class="fas fa-book me-1"></i>Journal of Consulting and Clinical Psychology</span>
                            <span><i class="fas fa-quote-right me-1"></i>引用 342 次</span>
                            <span><i class="fas fa-file-alt me-1"></i>隨機對照試驗</span>
                        </div>
                        <div class="research-abstract">
                            <strong>摘要：</strong>本研究採用隨機對照設計，比較正念認知療法(MBCT)與標準藥物治療對憂鬱症復發預防的效果。結果顯示，MBCT組的復發率顯著低於對照組(23% vs 47%)，且在18個月追蹤期間維持穩定效果。
                        </div>
                        <p class="text-muted">研究證實正念認知療法能有效降低憂鬱症復發風險，特別適用於有多次發作病史的患者。該療法結合認知療法技巧與正念修習，幫助患者建立對負面思維的覺察能力。</p>
                        <div class="research-tags">
                            <span class="tag">正念</span>
                            <span class="tag">憂鬱症</span>
                            <span class="tag">認知療法</span>
                            <span class="tag">復發預防</span>
                        </div>
                        <div class="citation-format">
                            <strong>APA引用格式：</strong><br>
                            Segal, Z. V., Anderson, P., Gunaratana, B., & Kabat-Zinn, J. (2020). Mindfulness-based cognitive therapy for depression relapse prevention: A randomized controlled trial. <i>Journal of Consulting and Clinical Psychology</i>, 88(4), 325-337.
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-primary btn-sm me-2">閱讀全文</button>
                            <button class="btn btn-outline-secondary btn-sm me-2">下載PDF</button>
                            <button class="btn btn-outline-info btn-sm">引用</button>
                        </div>
                    </div>

                    <!-- 研究項目 2 -->
                    <div class="research-card">
                        <div class="research-title">慈悲聚焦療法對創傷後壓力症候群的治療效果：統合分析</div>
                        <div class="research-meta">
                            <span><i class="fas fa-user me-1"></i>Gilbert, P., et al.</span>
                            <span><i class="fas fa-calendar me-1"></i>2021</span>
                            <span><i class="fas fa-book me-1"></i>Clinical Psychology Review</span>
                            <span><i class="fas fa-quote-right me-1"></i>引用 198 次</span>
                            <span><i class="fas fa-file-alt me-1"></i>統合分析</span>
                        </div>
                        <div class="research-abstract">
                            <strong>摘要：</strong>本統合分析納入12項隨機對照試驗，共876名PTSD患者，評估慈悲聚焦療法的治療效果。結果顯示中到大的效果量(d=0.72)，且在追蹤期間效果維持穩定。
                        </div>
                        <p class="text-muted">慈悲聚焦療法透過培養自我慈悲與對他人的慈悲心，有效改善PTSD症狀，特別適合伴有羞恥感與自我批評的患者。</p>
                        <div class="research-tags">
                            <span class="tag">慈悲療法</span>
                            <span class="tag">PTSD</span>
                            <span class="tag">統合分析</span>
                            <span class="tag">創傷治療</span>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-primary btn-sm me-2">閱讀全文</button>
                            <button class="btn btn-outline-secondary btn-sm me-2">下載PDF</button>
                            <button class="btn btn-outline-info btn-sm">引用</button>
                        </div>
                    </div>

                    <!-- 研究項目 3 -->
                    <div class="research-card">
                        <div class="research-title">禪修對大腦結構與功能影響的神經影像學研究</div>
                        <div class="research-meta">
                            <span><i class="fas fa-user me-1"></i>Lutz, A., et al.</span>
                            <span><i class="fas fa-calendar me-1"></i>2019</span>
                            <span><i class="fas fa-book me-1"></i>Nature Neuroscience</span>
                            <span><i class="fas fa-quote-right me-1"></i>引用 567 次</span>
                            <span><i class="fas fa-file-alt me-1"></i>神經影像研究</span>
                        </div>
                        <div class="research-abstract">
                            <strong>摘要：</strong>使用功能性磁振造影(fMRI)研究長期禪修者與對照組的大腦差異。發現禪修者在前額葉皮質、島葉和後扣帶皮質區域有顯著的結構與功能改變。
                        </div>
                        <p class="text-muted">研究證實長期禪修練習能夠改變大腦結構，增強注意力調節、情緒調節和自我覺察相關腦區的功能。</p>
                        <div class="research-tags">
                            <span class="tag">禪修</span>
                            <span class="tag">神經科學</span>
                            <span class="tag">fMRI</span>
                            <span class="tag">大腦可塑性</span>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-primary btn-sm me-2">閱讀全文</button>
                            <button class="btn btn-outline-secondary btn-sm me-2">下載PDF</button>
                            <button class="btn btn-outline-info btn-sm">引用</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 熱門研究 -->
        <section class="py-5" style="background: var(--light-bg);">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h3 class="text-center fw-bold mb-5">本月熱門研究</h3>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-4 mb-4">
                        <div class="category-card h-100">
                            <div class="category-icon">
                                <i class="fas fa-trending-up"></i>
                            </div>
                            <h6 class="fw-bold">MBSR在醫院的應用</h6>
                            <p class="small text-muted">最新的臨床應用研究報告</p>
                            <button class="btn btn-outline-primary btn-sm">查看研究</button>
                        </div>
                    </div>
                    <div class="col-lg-4 mb-4">
                        <div class="category-card h-100">
                            <div class="category-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <h6 class="fw-bold">正念與免疫系統</h6>
                            <p class="small text-muted">探討正念練習對免疫功能的影響</p>
                            <button class="btn btn-outline-primary btn-sm">查看研究</button>
                        </div>
                    </div>
                    <div class="col-lg-4 mb-4">
                        <div class="category-card h-100">
                            <div class="category-icon">
                                <i class="fas fa-fire"></i>
                            </div>
                            <h6 class="fw-bold">青少年禪修研究</h6>
                            <p class="small text-muted">年輕族群的身心健康改善效果</p>
                            <button class="btn btn-outline-primary btn-sm">查看研究</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // 導航欄滾動效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar-custom');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // 執行搜尋
        function performSearch() {
            const searchInput = document.getElementById('searchInput').value;
            if (searchInput.trim() !== '') {
                showSearchResults();
            }
        }

        // 快速搜尋
        function quickSearch(keyword) {
            document.getElementById('searchInput').value = keyword;
            performSearch();
        }

        // 顯示搜尋結果
        function showSearchResults() {
            const resultsSection = document.getElementById('searchResults');
            resultsSection.style.display = 'block';
            resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }

        // 篩選功能
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 搜尋框回車事件
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // 卡片動畫
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.category-card, .research-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = `all 0.6s ease ${index * 0.1}s`;
                observer.observe(card);
            });
        });
    </script>
</body>
</html>