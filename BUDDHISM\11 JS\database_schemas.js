// 佛教身心療癒網站 - 完整資料庫 Schema 設計
// 基於 Mongoose 的 MongoDB 資料模型

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// ========== 用戶模型 ==========

const userSchema = new mongoose.Schema({
  // 基本信息
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email'],
    index: true
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [8, 'Password must be at least 8 characters'],
    validate: {
      validator: function(password) {
        // 密碼強度驗證：至少包含大小寫字母和數字
        return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/.test(password);
      },
      message: 'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    }
  },
  
  // 個人資料
  profile: {
    firstName: {
      type: String,
      required: [true, 'First name is required'],
      trim: true,
      maxlength: [50, 'First name cannot exceed 50 characters']
    },
    lastName: {
      type: String,
      required: [true, 'Last name is required'],
      trim: true,
      maxlength: [50, 'Last name cannot exceed 50 characters']
    },
    displayName: {
      type: String,
      trim: true,
      maxlength: [100, 'Display name cannot exceed 100 characters']
    },
    avatar: {
      type: String,
      default: '',
      validate: {
        validator: function(url) {
          return !url || /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i.test(url);
        },
        message: 'Avatar must be a valid image URL'
      }
    },
    phone: {
      type: String,
      trim: true,
      validate: {
        validator: function(phone) {
          return !phone || /^[\+]?[0-9\-\s\(\)]{10,20}$/.test(phone);
        },
        message: 'Please enter a valid phone number'
      }
    },
    dateOfBirth: {
      type: Date,
      validate: {
        validator: function(date) {
          return !date || date < new Date();
        },
        message: 'Date of birth cannot be in the future'
      }
    },
    gender: {
      type: String,
      enum: ['male', 'female', 'other', 'prefer_not_to_say'],
      default: 'prefer_not_to_say'
    },
    location: {
      country: String,
      state: String,
      city: String,
      timezone: {
        type: String,
        default: 'Asia/Taipei'
      }
    },
    bio: {
      type: String,
      maxlength: [500, 'Bio cannot exceed 500 characters']
    },
    interests: [{
      type: String,
      enum: ['meditation', 'mindfulness', 'tibetan_medicine', 'scripture_study', 'healing', 'philosophy']
    }]
  },

  // 會員系統
  membership: {
    plan: {
      type: String,
      enum: ['basic', 'premium', 'vip'],
      default: 'basic',
      index: true
    },
    status: {
      type: String,
      enum: ['active', 'cancelled', 'expired', 'suspended'],
      default: 'active',
      index: true
    },
    startDate: {
      type: Date,
      default: Date.now
    },
    endDate: Date,
    autoRenewal: {
      type: Boolean,
      default: false
    },
    paymentMethod: {
      stripeCustomerId: String,
      defaultPaymentMethodId: String
    },
    billingHistory: [{
      amount: Number,
      currency: {
        type: String,
        default: 'TWD'
      },
      invoiceId: String,
      paymentDate: Date,
      status: {
        type: String,
        enum: ['paid', 'pending', 'failed', 'refunded']
      }
    }]
  },

  // 偏好設置
  preferences: {
    language: {
      type: String,
      enum: ['zh-TW', 'zh-CN', 'en', 'ja', 'ko'],
      default: 'zh-TW'
    },
    notifications: {
      email: {
        type: Boolean,
        default: true
      },
      push: {
        type: Boolean,
        default: true
      },
      studyReminders: {
        type: Boolean,
        default: true
      },
      courseUpdates: {
        type: Boolean,
        default: true
      },
      weeklyDigest: {
        type: Boolean,
        default: true
      }
    },
    privacy: {
      profilePublic: {
        type: Boolean,
        default: false
      },
      progressPublic: {
        type: Boolean,
        default: false
      },
      allowDataCollection: {
        type: Boolean,
        default: true
      }
    },
    accessibility: {
      fontSize: {
        type: String,
        enum: ['small', 'medium', 'large'],
        default: 'medium'
      },
      highContrast: {
        type: Boolean,
        default: false
      },
      screenReader: {
        type: Boolean,
        default: false
      }
    }
  },

  // 學習進度
  progress: {
    coursesCompleted: [{
      courseId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Course'
      },
      completedAt: {
        type: Date,
        default: Date.now
      },
      finalScore: Number,
      certificateId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Certificate'
      }
    }],
    currentCourses: [{
      courseId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Course'
      },
      enrolledAt: {
        type: Date,
        default: Date.now
      },
      progress: {
        type: Number,
        min: 0,
        max: 100,
        default: 0
      },
      lastModuleId: String,
      timeSpent: {
        type: Number,
        default: 0
      } // 分鐘
    }],
    certificates: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Certificate'
    }],
    achievements: [{
      type: {
        type: String,
        enum: ['course_completion', 'study_streak', 'meditation_hours', 'scripture_reader']
      },
      title: String,
      description: String,
      earnedAt: {
        type: Date,
        default: Date.now
      },
      icon: String
    }],
    statistics: {
      totalStudyTime: {
        type: Number,
        default: 0
      }, // 分鐘
      consecutiveStudyDays: {
        type: Number,
        default: 0
      },
      totalScripturesRead: {
        type: Number,
        default: 0
      },
      totalMeditationTime: {
        type: Number,
        default: 0
      }, // 分鐘
      lastActiveDate: {
        type: Date,
        default: Date.now
      }
    }
  },

  // 收藏和書籤
  bookmarks: {
    scriptures: [{
      scriptureId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Scripture'
      },
      bookmarkedAt: {
        type: Date,
        default: Date.now
      },
      notes: String
    }],
    courses: [{
      courseId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Course'
      },
      bookmarkedAt: {
        type: Date,
        default: Date.now
      }
    }],
    research: [{
      researchId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Research'
      },
      bookmarkedAt: {
        type: Date,
        default: Date.now
      }
    }]
  },

  // 社交功能
  social: {
    following: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }],
    followers: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }],
    studyGroups: [{
      groupId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'StudyGroup'
      },
      role: {
        type: String,
        enum: ['member', 'moderator', 'admin']
      },
      joinedAt: {
        type: Date,
        default: Date.now
      }
    }]
  },

  // 系統字段
  role: {
    type: String,
    enum: ['user', 'instructor', 'admin', 'super_admin'],
    default: 'user',
    index: true
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'deleted'],
    default: 'active',
    index: true
  },
  emailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: String,
  passwordResetToken: String,
  passwordResetExpires: Date,
  lastLoginAt: Date,
  loginCount: {
    type: Number,
    default: 0
  },
  
  // 時間戳記
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.password;
      delete ret.emailVerificationToken;
      delete ret.passwordResetToken;
      return ret;
    }
  }
});

// 索引設置
userSchema.index({ email: 1 }, { unique: true });
userSchema.index({ 'membership.plan': 1, 'membership.status': 1 });
userSchema.index({ 'progress.statistics.lastActiveDate': 1 });
userSchema.index({ role: 1, status: 1 });
userSchema.index({ createdAt: 1 });

// 密碼加密中間件
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// 更新時間中間件
userSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// 實例方法
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

userSchema.methods.generateAuthToken = function() {
  return jwt.sign(
    {
      sub: this._id,
      email: this.email,
      role: this.role,
      membership: this.membership.plan
    },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
};

userSchema.methods.generateRefreshToken = function() {
  return jwt.sign(
    { sub: this._id },
    process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d' }
  );
};

// ========== 課程模型 ==========

const courseSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Course title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters'],
    index: true
  },
  slug: {
    type: String,
    unique: true,
    lowercase: true,
    trim: true
  },
  description: {
    type: String,
    required: [true, 'Course description is required'],
    maxlength: [2000, 'Description cannot exceed 2000 characters']
  },
  shortDescription: {
    type: String,
    maxlength: [500, 'Short description cannot exceed 500 characters']
  },
  
  // 分類和標籤
  category: {
    type: String,
    required: [true, 'Course category is required'],
    enum: [
      'meditation', 'mindfulness', 'buddhist_philosophy', 'scripture_study',
      'tibetan_medicine', 'healing_practices', 'teacher_training', 'beginner_course'
    ],
    index: true
  },
  subcategory: {
    type: String,
    trim: true
  },
  difficulty: {
    type: String,
    enum: ['beginner', 'intermediate', 'advanced'],
    required: [true, 'Difficulty level is required'],
    index: true
  },
  
  // 課程內容
  duration: {
    type: Number,
    required: [true, 'Course duration is required'],
    min: [1, 'Duration must be at least 1 minute']
  }, // 總時長（分鐘）
  
  instructor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Instructor is required'],
    index: true
  },
  
  coInstructors: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  
  content: {
    overview: {
      type: String,
      maxlength: [1000, 'Overview cannot exceed 1000 characters']
    },
    objectives: [{
      type: String,
      maxlength: [200, 'Objective cannot exceed 200 characters']
    }],
    requirements: [{
      type: String,
      maxlength: [200, 'Requirement cannot exceed 200 characters']
    }],
    modules: [{
      title: {
        type: String,
        required: [true, 'Module title is required'],
        maxlength: [200, 'Module title cannot exceed 200 characters']
      },
      description: {
        type: String,
        maxlength: [1000, 'Module description cannot exceed 1000 characters']
      },
      duration: {
        type: Number,
        required: [true, 'Module duration is required'],
        min: [1, 'Module duration must be at least 1 minute']
      }, // 分鐘
      order: {
        type: Number,
        required: true
      },
      lessons: [{
        title: {
          type: String,
          required: [true, 'Lesson title is required']
        },
        description: String,
        type: {
          type: String,
          enum: ['video', 'audio', 'text', 'interactive', 'quiz'],
          required: true
        },
        content: {
          videoUrl: String,
          audioUrl: String,
          textContent: String,
          resourceUrls: [String],
          quiz: {
            questions: [{
              question: String,
              type: {
                type: String,
                enum: ['multiple_choice', 'true_false', 'essay']
              },
              options: [String], // for multiple choice
              correctAnswer: mongoose.Schema.Types.Mixed,
              explanation: String
            }]
          }
        },
        duration: {
          type: Number,
          required: true
        }, // 分鐘
        order: {
          type: Number,
          required: true
        },
        isPreview: {
          type: Boolean,
          default: false
        }
      }],
      assignments: [{
        title: String,
        description: String,
        instructions: String,
        dueDate: Date,
        points: Number
      }]
    }]
  },
  
  // 先修條件
  prerequisites: [{
    courseId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Course'
    },
    required: {
      type: Boolean,
      default: true
    }
  }],
  
  // 標籤和關鍵字
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  
  // 定價和會員資格
  pricing: {
    isFree: {
      type: Boolean,
      default: false
    },
    price: {
      type: Number,
      min: 0,
      default: 0
    },
    currency: {
      type: String,
      default: 'TWD'
    },
    membershipRequired: {
      type: String,
      enum: ['none', 'basic', 'premium', 'vip'],
      default: 'none'
    },
    discounts: [{
      type: {
        type: String,
        enum: ['percentage', 'fixed_amount']
      },
      value: Number,
      validFrom: Date,
      validTo: Date,
      code: String
    }]
  },
  
  // 媒體資源
  media: {
    thumbnail: {
      type: String,
      validate: {
        validator: function(url) {
          return !url || /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i.test(url);
        },
        message: 'Thumbnail must be a valid image URL'
      }
    },
    trailer: {
      type: String,
      validate: {
        validator: function(url) {
          return !url || /^https?:\/\/.+\.(mp4|webm|ogg)$/i.test(url);
        },
        message: 'Trailer must be a valid video URL'
      }
    },
    images: [String],
    documents: [{
      title: String,
      url: String,
      type: {
        type: String,
        enum: ['pdf', 'doc', 'ppt', 'other']
      }
    }]
  },
  
  // 統計數據
  statistics: {
    enrollments: {
      type: Number,
      default: 0,
      min: 0
    },
    completions: {
      type: Number,
      default: 0,
      min: 0
    },
    averageRating: {
      type: Number,
      min: 0,
      max: 5,
      default: 0
    },
    totalRatings: {
      type: Number,
      default: 0,
      min: 0
    },
    views: {
      type: Number,
      default: 0,
      min: 0
    },
    completionRate: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    }
  },
  
  // 評價系統
  reviews: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    rating: {
      type: Number,
      required: true,
      min: 1,
      max: 5
    },
    title: {
      type: String,
      maxlength: [100, 'Review title cannot exceed 100 characters']
    },
    comment: {
      type: String,
      maxlength: [1000, 'Review comment cannot exceed 1000 characters']
    },
    helpful: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      isHelpful: Boolean
    }],
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // 發布狀態
  status: {
    type: String,
    enum: ['draft', 'review', 'published', 'archived', 'suspended'],
    default: 'draft',
    index: true
  },
  
  publishedAt: Date,
  
  // SEO 和元數據
  seo: {
    metaTitle: String,
    metaDescription: String,
    keywords: [String],
    canonicalUrl: String
  },
  
  // 設置
  settings: {
    allowComments: {
      type: Boolean,
      default: true
    },
    allowRatings: {
      type: Boolean,
      default: true
    },
    isGroupCourse: {
      type: Boolean,
      default: false
    },
    maxGroupSize: Number,
    autoEnroll: {
      type: Boolean,
      default: false
    }
  }
}, {
  timestamps: true,
  versionKey: false
});

// 課程索引
courseSchema.index({ title: 'text', description: 'text', tags: 'text' });
courseSchema.index({ category: 1, difficulty: 1 });
courseSchema.index({ 'pricing.membershipRequired': 1 });
courseSchema.index({ status: 1, publishedAt: -1 });
courseSchema.index({ instructor: 1 });
courseSchema.index({ 'statistics.enrollments': -1 });
courseSchema.index({ 'statistics.averageRating': -1 });

// 課程方法
courseSchema.methods.calculateCompletionRate = function() {
  if (this.statistics.enrollments === 0) return 0;
  return Math.round((this.statistics.completions / this.statistics.enrollments) * 100);
};

courseSchema.methods.updateStatistics = async function() {
  // 這裡可以添加更新統計數據的邏輯
  this.statistics.completionRate = this.calculateCompletionRate();
  return this.save();
};

// ========== 經文模型 ==========

const scriptureSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Scripture title is required'],
    trim: true,
    maxlength: [300, 'Title cannot exceed 300 characters'],
    index: true
  },
  originalTitle: {
    type: String,
    trim: true,
    maxlength: [300, 'Original title cannot exceed 300 characters']
  },
  slug: {
    type: String,
    unique: true,
    lowercase: true,
    trim: true
  },
  
  // 分類系統
  category: {
    type: String,
    required: [true, 'Scripture category is required'],
    enum: [
      'sutra', 'vinaya', 'abhidharma', 'tantric', 'commentary',
      'prayer', 'mantra', 'meditation_guide', 'healing_text'
    ],
    index: true
  },
  subcategory: {
    type: String,
    trim: true
  },
  tradition: {
    type: String,
    enum: ['theravada', 'mahayana', 'vajrayana', 'zen', 'pure_land', 'tibetan'],
    index: true
  },
  
  // 內容
  content: {
    originalText: {
      type: String,
      required: [true, 'Original text is required']
    },
    translation: {
      type: String,
      required: [true, 'Translation is required']
    },
    transliteration: String, // 音譯
    commentary: {
      type: String,
      maxlength: [10000, 'Commentary cannot exceed 10000 characters']
    },
    interpretation: {
      type: String,
      maxlength: [5000, 'Interpretation cannot exceed 5000 characters']
    },
    keyPoints: [String], // 重點摘要
    audioUrl: {
      type: String,
      validate: {
        validator: function(url) {
          return !url || /^https?:\/\/.+\.(mp3|wav|ogg|m4a)$/i.test(url);
        },
        message: 'Audio must be a valid audio URL'
      }
    },
    videoUrl: {
      type: String,
      validate: {
        validator: function(url) {
          return !url || /^https?:\/\/.+\.(mp4|webm|ogg)$/i.test(url);
        },
        message: 'Video must be a valid video URL'
      }
    }
  },
  
  // 元數據
  metadata: {
    author: {
      type: String,
      trim: true
    },
    translator: {
      type: String,
      trim: true
    },
    editor: {
      type: String,
      trim: true
    },
    source: {
      type: String,
      trim: true
    },
    sourceUrl: String,
    language: {
      original: {
        type: String,
        default: 'Sanskrit'
      },
      translation: {
        type: String,
        default: 'zh-TW'
      }
    },
    period: {
      type: String,
      trim: true
    },
    historicalContext: String,
    references: [String] // 參考文獻
  },
  
  // 療癒應用
  healing: {
    applications: [{
      condition: {
        type: String,
        enum: [
          'anxiety', 'depression', 'stress', 'grief', 'anger',
          'physical_pain', 'insomnia', 'addiction', 'trauma',
          'spiritual_seeking', 'general_wellbeing'
        ]
      },
      description: String,
      method: String
    }],
    techniques: [{
      name: String,
      description: String,
      instructions: [String]
    }],
    benefits: [String],
    precautions: [String],
    recommendedPractice: {
      frequency: String, // 例：daily, weekly
      duration: String,  // 例：10-15 minutes
      timeOfDay: [String] // 例：morning, evening
    }
  },
  
  // 學習資源
  studyMaterials: {
    difficulty: {
      type: String,
      enum: ['beginner', 'intermediate', 'advanced'],
      default: 'beginner',
      index: true
    },
    readingTime: {
      type: Number,
      min: 1
    }, // 分鐘
    studyGuide: String,
    questions: [{
      question: String,
      answer: String,
      difficulty: {
        type: String,
        enum: ['easy', 'medium', 'hard']
      }
    }],
    vocabulary: [{
      term: String,
      definition: String,
      pronunciation: String
    }]
  },
  
  // 標籤和搜尋
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  searchKeywords: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  
  // 統計和互動
  statistics: {
    views: {
      type: Number,
      default: 0,
      min: 0
    },
    bookmarks: {
      type: Number,
      default: 0,
      min: 0
    },
    shares: {
      type: Number,
      default: 0,
      min: 0
    },
    popularity: {
      type: Number,
      default: 0,
      min: 0
    }, // 綜合熱度分數
    averageRating: {
      type: Number,
      min: 0,
      max: 5,
      default: 0
    },
    totalRatings: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  
  // 評價
  ratings: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    rating: {
      type: Number,
      required: true,
      min: 1,
      max: 5
    },
    review: String,
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // 相關內容
  relatedScriptures: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Scripture'
  }],
  relatedCourses: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course'
  }],
  
  // 訪問控制
  accessLevel: {
    type: String,
    enum: ['public', 'members', 'premium', 'vip'],
    default: 'public',
    index: true
  },
  
  // 狀態
  status: {
    type: String,
    enum: ['draft', 'review', 'published', 'archived'],
    default: 'draft',
    index: true
  },
  
  // SEO
  seo: {
    metaTitle: String,
    metaDescription: String,
    keywords: [String]
  }
}, {
  timestamps: true,
  versionKey: false
});

// 經文索引
scriptureSchema.index({ 
  title: 'text', 
  'content.translation': 'text', 
  'content.commentary': 'text',
  tags: 'text',
  searchKeywords: 'text'
});
scriptureSchema.index({ category: 1, tradition: 1 });
scriptureSchema.index({ 'studyMaterials.difficulty': 1 });
scriptureSchema.index({ 'statistics.popularity': -1 });
scriptureSchema.index({ accessLevel: 1, status: 1 });

// ========== 研究文獻模型 ==========

const researchSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Research title is required'],
    trim: true,
    maxlength: [500, 'Title cannot exceed 500 characters'],
    index: true
  },
  abstract: {
    type: String,
    required: [true, 'Abstract is required'],
    maxlength: [2000, 'Abstract cannot exceed 2000 characters']
  },
  
  // 作者信息
  authors: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    affiliation: String,
    email: String,
    isCorresponding: {
      type: Boolean,
      default: false
    }
  }],
  
  // 發表信息
  publication: {
    journal: {
      type: String,
      trim: true
    },
    year: {
      type: Number,
      required: true,
      min: 1900,
      max: new Date().getFullYear() + 1
    },
    volume: String,
    issue: String,
    pages: String,
    doi: {
      type: String,
      unique: true,
      sparse: true
    },
    pmid: String, // PubMed ID
    isbn: String,
    publisher: String,
    conferenceId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Conference'
    }
  },
  
  // 分類
  category: {
    type: String,
    required: [true, 'Research category is required'],
    enum: [
      'clinical_study', 'systematic_review', 'meta_analysis',
      'theoretical_research', 'case_study', 'experimental',
      'observational', 'qualitative', 'mixed_methods'
    ],
    index: true
  },
  
  researchArea: {
    type: String,
    enum: [
      'meditation_neuroscience', 'mindfulness_therapy', 'tibetan_medicine',
      'acupuncture', 'herbal_medicine', 'mind_body_medicine',
      'consciousness_studies', 'spiritual_psychology', 'integrative_medicine'
    ],
    index: true
  },
  
  keywords: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  
  // 研究內容
  content: {
    introduction: String,
    methodology: {
      type: String,
      maxlength: [5000, 'Methodology cannot exceed 5000 characters']
    },
    results: String,
    discussion: String,
    conclusion: String,
    limitations: String,
    futureResearch: String
  },
  
  // 數據和統計
  studyDetails: {
    sampleSize: Number,
    studyDuration: String, // 例：6 months
    studyDesign: {
      type: String,
      enum: ['rct', 'cohort', 'case_control', 'cross_sectional', 'qualitative', 'other']
    },
    participants: {
      ageRange: String,
      gender: String,
      criteria: String
    },
    interventions: [String],
    outcomes: [String],
    statisticalMethods: [String]
  },
  
  // 文件和資源
  files: {
    pdfUrl: String,
    supplementaryMaterials: [String],
    datasets: [String],
    multimedia: [String]
  },
  
  // 引用和影響
  citations: {
    count: {
      type: Number,
      default: 0,
      min: 0
    },
    hIndex: Number,
    impactFactor: Number
  },
  
  references: [String], // 參考文獻列表
  
  // 相關研究
  relatedStudies: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Research'
  }],
  
  // 訊問控制
  accessibility: {
    type: String,
    enum: ['open_access', 'subscription', 'members_only', 'premium_only'],
    default: 'open_access',
    index: true
  },
  
  // 質量評估
  qualityScore: {
    type: Number,
    min: 0,
    max: 10
  },
  
  peerReview: {
    status: {
      type: String,
      enum: ['pending', 'under_review', 'accepted', 'rejected', 'revision_required']
    },
    reviewers: [{
      reviewer: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      score: {
        type: Number,
        min: 1,
        max: 10
      },
      comments: String,
      recommendation: {
        type: String,
        enum: ['accept', 'minor_revision', 'major_revision', 'reject']
      },
      reviewDate: Date
    }]
  },
  
  // 統計
  statistics: {
    views: {
      type: Number,
      default: 0
    },
    downloads: {
      type: Number,
      default: 0
    },
    bookmarks: {
      type: Number,
      default: 0
    },
    shares: {
      type: Number,
      default: 0
    }
  },
  
  // 狀態
  status: {
    type: String,
    enum: ['draft', 'submitted', 'under_review', 'published', 'archived'],
    default: 'draft',
    index: true
  }
}, {
  timestamps: true,
  versionKey: false
});

// 研究索引
researchSchema.index({ 
  title: 'text', 
  abstract: 'text', 
  keywords: 'text',
  'authors.name': 'text'
});
researchSchema.index({ category: 1, researchArea: 1 });
researchSchema.index({ 'publication.year': -1 });
researchSchema.index({ 'citations.count': -1 });
researchSchema.index({ accessibility: 1, status: 1 });

// ========== 分析事件模型 ==========

const analyticsEventSchema = new mongoose.Schema({
  type: {
    type: String,
    required: true,
    enum: [
      'page_view', 'user_login', 'user_logout', 'user_register',
      'course_enrollment', 'course_completion', 'lesson_start', 'lesson_complete',
      'scripture_view', 'scripture_bookmark', 'search', 'download',
      'payment', 'subscription', 'error', 'custom'
    ],
    index: true
  },
  
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    index: true
  },
  
  sessionId: {
    type: String,
    index: true
  },
  
  // 事件數據
  data: {
    page: String,
    referrer: String,
    userAgent: String,
    ip: String,
    country: String,
    city: String,
    device: {
      type: String,
      enum: ['desktop', 'mobile', 'tablet']
    },
    browser: String,
    os: String,
    
    // 特定事件數據
    courseId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Course'
    },
    scriptureId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Scripture'
    },
    lessonId: String,
    searchQuery: String,
    searchResults: Number,
    errorMessage: String,
    customData: mongoose.Schema.Types.Mixed
  },
  
  // 時間和持續時間
  timestamp: {
    type: Date,
    default: Date.now,
    index: true
  },
  duration: Number, // 毫秒
  
  // 地理位置
  location: {
    country: String,
    region: String,
    city: String,
    coordinates: {
      type: [Number], // [longitude, latitude]
      index: '2dsphere'
    }
  }
}, {
  versionKey: false
});

// 分析事件索引
analyticsEventSchema.index({ type: 1, timestamp: -1 });
analyticsEventSchema.index({ userId: 1, timestamp: -1 });
analyticsEventSchema.index({ timestamp: -1 }); // TTL 索引，可設置過期時間

// ========== 導出模型 ==========

const User = mongoose.model('User', userSchema);
const Course = mongoose.model('Course', courseSchema);
const Scripture = mongoose.model('Scripture', scriptureSchema);
const Research = mongoose.model('Research', researchSchema);
const AnalyticsEvent = mongoose.model('AnalyticsEvent', analyticsEventSchema);

module.exports = {
  User,
  Course,
  Scripture,
  Research,
  AnalyticsEvent
};