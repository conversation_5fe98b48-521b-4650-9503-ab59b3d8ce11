const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const authenticate = require('../middleware/auth');

// 模擬研究文章數據結構
const ResearchArticle = {
  // 實際應該是 MongoDB 模型，這裡用物件模擬
};

// 驗證錯誤處理中間件
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// 模擬研究數據
const mockResearchData = {
  articles: [
    {
      id: '1',
      title: '正念冥想對焦慮症狀的療效研究',
      abstract: '本研究探討正念冥想練習對焦慮症狀改善的效果，透過隨機對照試驗分析...',
      category: 'clinical',
      subcategory: 'anxiety',
      authors: ['陳博士', '林教授', '王研究員'],
      institution: '台灣佛教醫學研究中心',
      publishDate: '2023-10-15',
      keywords: ['正念', '冥想', '焦慮症', '心理治療'],
      methodology: 'randomized_controlled_trial',
      sampleSize: 120,
      duration: '8週',
      findings: [
        '正念冥想組焦慮指數顯著下降35%',
        '對照組僅下降12%',
        '效果持續至追蹤期3個月'
      ],
      implications: '正念冥想可作為焦慮症的輔助治療方法',
      pdfUrl: '/research/files/mindfulness-anxiety-2023.pdf',
      citationCount: 45,
      downloadCount: 1250,
      rating: 4.8,
      tags: ['實證研究', '臨床試驗', '心理健康']
    },
    {
      id: '2',
      title: '藏傳佛教修持對憂鬱症康復的影響',
      abstract: '研究藏傳佛教特有的修持方法對憂鬱症患者康復過程的正面影響...',
      category: 'clinical',
      subcategory: 'depression',
      authors: ['洛桑醫師', '張心理師'],
      institution: '喜馬拉雅醫學院',
      publishDate: '2023-09-20',
      keywords: ['藏傳佛教', '憂鬱症', '康復', '修持'],
      methodology: 'case_study',
      sampleSize: 45,
      duration: '12週',
      findings: [
        '87%參與者憂鬱症狀明顯改善',
        '修持頻率與康復程度呈正相關',
        '復發率較傳統治療低30%'
      ],
      implications: '藏傳佛教修持具有顯著的抗憂鬱效果',
      pdfUrl: '/research/files/tibetan-depression-2023.pdf',
      citationCount: 28,
      downloadCount: 890,
      rating: 4.6,
      tags: ['個案研究', '憂鬱症', '藏傳佛教']
    },
    {
      id: '3',
      title: '佛教音樂療法在慢性疼痛管理中的應用',
      abstract: '探討傳統佛教音樂與現代音樂療法結合對慢性疼痛患者的治療效果...',
      category: 'therapeutic',
      subcategory: 'pain_management',
      authors: ['釋慧音', '李醫師', '音樂治療師王老師'],
      institution: '慈濟醫學中心',
      publishDate: '2023-11-02',
      keywords: ['佛教音樂', '音樂療法', '慢性疼痛', '疼痛管理'],
      methodology: 'mixed_methods',
      sampleSize: 80,
      duration: '6週',
      findings: [
        '疼痛強度平均降低42%',
        '睡眠品質顯著改善',
        '藥物使用量減少25%'
      ],
      implications: '佛教音樂療法可納入慢性疼痛的綜合治療方案',
      pdfUrl: '/research/files/buddhist-music-pain-2023.pdf',
      citationCount: 19,
      downloadCount: 567,
      rating: 4.4,
      tags: ['音樂療法', '疼痛管理', '整合醫學']
    }
  ],
  categories: {
    clinical: {
      name: '臨床研究',
      description: '佛教療法在臨床醫學中的應用研究',
      subcategories: {
        anxiety: '焦慮症研究',
        depression: '憂鬱症研究',
        ptsd: '創傷後壓力症候群',
        addiction: '成癮治療'
      }
    },
    therapeutic: {
      name: '治療方法',
      description: '佛教療癒技術和方法的研究',
      subcategories: {
        meditation: '冥想療法',
        mindfulness: '正念訓練',
        music_therapy: '音樂療法',
        pain_management: '疼痛管理'
      }
    },
    neuroscience: {
      name: '神經科學',
      description: '佛教修持對大腦和神經系統的影響',
      subcategories: {
        brain_imaging: '腦部影像學',
        neuroplasticity: '神經可塑性',
        consciousness: '意識研究'
      }
    },
    psychology: {
      name: '心理學',
      description: '佛教心理學理論與實踐研究',
      subcategories: {
        cognitive: '認知心理學',
        behavioral: '行為心理學',
        positive_psychology: '正向心理學'
      }
    }
  }
};

// 獲取研究文章列表
router.get('/articles', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      category,
      subcategory,
      search,
      author,
      institution,
      methodology,
      dateFrom,
      dateTo,
      sort = 'newest'
    } = req.query;

    const skip = (page - 1) * limit;
    let filteredArticles = [...mockResearchData.articles];

    // 應用篩選條件
    if (category) {
      filteredArticles = filteredArticles.filter(article => 
        article.category === category
      );
    }

    if (subcategory) {
      filteredArticles = filteredArticles.filter(article => 
        article.subcategory === subcategory
      );
    }

    if (search) {
      const searchLower = search.toLowerCase();
      filteredArticles = filteredArticles.filter(article =>
        article.title.toLowerCase().includes(searchLower) ||
        article.abstract.toLowerCase().includes(searchLower) ||
        article.keywords.some(keyword => keyword.toLowerCase().includes(searchLower)) ||
        article.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    if (author) {
      filteredArticles = filteredArticles.filter(article =>
        article.authors.some(a => a.toLowerCase().includes(author.toLowerCase()))
      );
    }

    if (institution) {
      filteredArticles = filteredArticles.filter(article =>
        article.institution.toLowerCase().includes(institution.toLowerCase())
      );
    }

    if (methodology) {
      filteredArticles = filteredArticles.filter(article =>
        article.methodology === methodology
      );
    }

    // 日期範圍篩選
    if (dateFrom || dateTo) {
      filteredArticles = filteredArticles.filter(article => {
        const articleDate = new Date(article.publishDate);
        if (dateFrom && articleDate < new Date(dateFrom)) return false;
        if (dateTo && articleDate > new Date(dateTo)) return false;
        return true;
      });
    }

    // 排序
    const sortOptions = {
      'newest': (a, b) => new Date(b.publishDate) - new Date(a.publishDate),
      'oldest': (a, b) => new Date(a.publishDate) - new Date(b.publishDate),
      'most_cited': (a, b) => b.citationCount - a.citationCount,
      'most_downloaded': (a, b) => b.downloadCount - a.downloadCount,
      'highest_rated': (a, b) => b.rating - a.rating,
      'title': (a, b) => a.title.localeCompare(b.title)
    };

    if (sortOptions[sort]) {
      filteredArticles.sort(sortOptions[sort]);
    }

    // 分頁
    const paginatedArticles = filteredArticles.slice(skip, skip + parseInt(limit));

    // 統計信息
    const statistics = {
      totalArticles: filteredArticles.length,
      categoryCounts: {},
      methodologyCounts: {},
      recentPublications: filteredArticles.filter(a => 
        new Date(a.publishDate) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      ).length
    };

    filteredArticles.forEach(article => {
      statistics.categoryCounts[article.category] = 
        (statistics.categoryCounts[article.category] || 0) + 1;
      statistics.methodologyCounts[article.methodology] = 
        (statistics.methodologyCounts[article.methodology] || 0) + 1;
    });

    res.json({
      message: 'Research articles retrieved successfully',
      articles: paginatedArticles,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: filteredArticles.length,
        pages: Math.ceil(filteredArticles.length / limit)
      },
      statistics,
      filters: {
        categories: mockResearchData.categories,
        methodologies: ['randomized_controlled_trial', 'case_study', 'mixed_methods', 'systematic_review', 'meta_analysis'],
        sortOptions: Object.keys(sortOptions)
      }
    });
  } catch (error) {
    console.error('Get research articles error:', error);
    res.status(500).json({ error: 'Failed to fetch research articles' });
  }
});

// 獲取特定研究文章詳情
router.get('/articles/:id', async (req, res) => {
  try {
    const articleId = req.params.id;
    const article = mockResearchData.articles.find(a => a.id === articleId);

    if (!article) {
      return res.status(404).json({ error: 'Research article not found' });
    }

    // 增加下載計數（模擬）
    article.downloadCount += 1;

    // 獲取相關文章
    const relatedArticles = mockResearchData.articles
      .filter(a => a.id !== articleId)
      .filter(a => 
        a.category === article.category ||
        a.subcategory === article.subcategory ||
        a.keywords.some(k => article.keywords.includes(k))
      )
      .slice(0, 5);

    // 獲取引用信息（模擬）
    const citationInfo = {
      apa: `${article.authors.join(', ')} (${new Date(article.publishDate).getFullYear()}). ${article.title}. ${article.institution}.`,
      mla: `${article.authors.join(', ')}. "${article.title}." ${article.institution}, ${article.publishDate}.`,
      chicago: `${article.authors.join(', ')}. "${article.title}." ${article.institution}. ${article.publishDate}.`
    };

    res.json({
      message: 'Research article details retrieved successfully',
      article,
      relatedArticles,
      citationInfo,
      downloadCount: article.downloadCount
    });
  } catch (error) {
    console.error('Get article details error:', error);
    res.status(500).json({ error: 'Failed to fetch article details' });
  }
});

// 下載研究文件
router.get('/articles/:id/download', authenticate, async (req, res) => {
  try {
    const articleId = req.params.id;
    const article = mockResearchData.articles.find(a => a.id === articleId);

    if (!article) {
      return res.status(404).json({ error: 'Research article not found' });
    }

    // 檢查會員權限
    const membershipLevels = { basic: 1, premium: 2, vip: 3 };
    const userLevel = membershipLevels[req.user.membership.plan];

    // 某些高級研究需要premium以上會員
    if (article.category === 'neuroscience' && userLevel < 2) {
      return res.status(403).json({ 
        error: 'Premium membership required for neuroscience research downloads',
        required: 'premium',
        current: req.user.membership.plan
      });
    }

    // 記錄下載（實際應記錄到數據庫）
    console.log(`User ${req.user._id} downloaded article ${articleId}`);

    // 模擬文件下載
    res.json({
      message: 'Download initiated successfully',
      downloadUrl: article.pdfUrl,
      fileName: `${article.title.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`,
      fileSize: '2.3 MB',
      downloadExpiry: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小時有效
    });
  } catch (error) {
    console.error('Download article error:', error);
    res.status(500).json({ error: 'Failed to initiate download' });
  }
});

// 收藏研究文章
router.post('/articles/:id/bookmark', authenticate, async (req, res) => {
  try {
    const articleId = req.params.id;
    const userId = req.user._id;

    const article = mockResearchData.articles.find(a => a.id === articleId);
    if (!article) {
      return res.status(404).json({ error: 'Research article not found' });
    }

    // 這裡應該更新用戶的收藏列表
    // 暫時模擬成功響應
    res.json({
      message: 'Research article bookmarked successfully',
      articleId: articleId,
      title: article.title,
      bookmarkedAt: new Date()
    });
  } catch (error) {
    console.error('Bookmark article error:', error);
    res.status(500).json({ error: 'Failed to bookmark article' });
  }
});

// 評價研究文章
router.post('/articles/:id/rate', authenticate, [
  body('rating').isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
  body('comment').optional().isLength({ max: 500 }).withMessage('Comment must be less than 500 characters')
], handleValidationErrors, async (req, res) => {
  try {
    const articleId = req.params.id;
    const { rating, comment } = req.body;

    const article = mockResearchData.articles.find(a => a.id === articleId);
    if (!article) {
      return res.status(404).json({ error: 'Research article not found' });
    }

    // 模擬評分更新
    const currentRating = article.rating;
    const ratingCount = article.citationCount; // 假設引用數等於評分數
    const newRating = ((currentRating * ratingCount) + rating) / (ratingCount + 1);
    article.rating = Math.round(newRating * 10) / 10;

    res.json({
      message: 'Article rated successfully',
      articleId: articleId,
      userRating: rating,
      newAverageRating: article.rating,
      comment: comment,
      ratedAt: new Date()
    });
  } catch (error) {
    console.error('Rate article error:', error);
    res.status(500).json({ error: 'Failed to rate article' });
  }
});

// 搜尋研究文章
router.get('/search', async (req, res) => {
  try {
    const {
      q: query,
      category,
      methodology,
      author,
      institution,
      keywords,
      dateFrom,
      dateTo,
      page = 1,
      limit = 20,
      sort = 'relevance'
    } = req.query;

    let results = [...mockResearchData.articles];

    // 文本搜尋
    if (query) {
      const searchTerms = query.toLowerCase().split(' ');
      results = results.filter(article => {
        const searchableText = `${article.title} ${article.abstract} ${article.keywords.join(' ')} ${article.tags.join(' ')}`.toLowerCase();
        return searchTerms.every(term => searchableText.includes(term));
      });
    }

    // 其他篩選條件
    if (category) results = results.filter(a => a.category === category);
    if (methodology) results = results.filter(a => a.methodology === methodology);
    if (author) results = results.filter(a => a.authors.some(au => au.toLowerCase().includes(author.toLowerCase())));
    if (institution) results = results.filter(a => a.institution.toLowerCase().includes(institution.toLowerCase()));

    // 關鍵詞篩選
    if (keywords) {
      const keywordArray = keywords.split(',').map(k => k.trim().toLowerCase());
      results = results.filter(article =>
        keywordArray.some(keyword =>
          article.keywords.some(k => k.toLowerCase().includes(keyword))
        )
      );
    }

    // 日期篩選
    if (dateFrom || dateTo) {
      results = results.filter(article => {
        const articleDate = new Date(article.publishDate);
        if (dateFrom && articleDate < new Date(dateFrom)) return false;
        if (dateTo && articleDate > new Date(dateTo)) return false;
        return true;
      });
    }

    // 排序
    const sortOptions = {
      'relevance': (a, b) => b.rating - a.rating, // 簡化的相關性排序
      'newest': (a, b) => new Date(b.publishDate) - new Date(a.publishDate),
      'citations': (a, b) => b.citationCount - a.citationCount,
      'downloads': (a, b) => b.downloadCount - a.downloadCount
    };

    if (sortOptions[sort]) {
      results.sort(sortOptions[sort]);
    }

    // 分頁
    const skip = (page - 1) * limit;
    const paginatedResults = results.slice(skip, skip + parseInt(limit));

    // 搜尋統計
    const searchStats = {
      totalResults: results.length,
      queryProcessingTime: '0.15s',
      topCategories: {},
      topMethodologies: {},
      dateRange: {
        earliest: results.length > 0 ? Math.min(...results.map(a => new Date(a.publishDate))) : null,
        latest: results.length > 0 ? Math.max(...results.map(a => new Date(a.publishDate))) : null
      }
    };

    results.forEach(article => {
      searchStats.topCategories[article.category] = (searchStats.topCategories[article.category] || 0) + 1;
      searchStats.topMethodologies[article.methodology] = (searchStats.topMethodologies[article.methodology] || 0) + 1;
    });

    res.json({
      message: 'Search completed successfully',
      query: {
        text: query,
        filters: { category, methodology, author, institution, keywords, dateFrom, dateTo }
      },
      results: paginatedResults,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: results.length,
        pages: Math.ceil(results.length / limit)
      },
      statistics: searchStats
    });
  } catch (error) {
    console.error('Search research error:', error);
    res.status(500).json({ error: 'Failed to search research articles' });
  }
});

// 獲取研究統計數據
router.get('/statistics', async (req, res) => {
  try {
    const articles = mockResearchData.articles;
    
    const stats = {
      overview: {
        totalArticles: articles.length,
        totalDownloads: articles.reduce((sum, a) => sum + a.downloadCount, 0),
        totalCitations: articles.reduce((sum, a) => sum + a.citationCount, 0),
        averageRating: Math.round((articles.reduce((sum, a) => sum + a.rating, 0) / articles.length) * 10) / 10
      },
      categories: {},
      methodologies: {},
      institutions: {},
      recentTrends: {
        lastMonth: articles.filter(a => 
          new Date(a.publishDate) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        ).length,
        lastQuarter: articles.filter(a => 
          new Date(a.publishDate) > new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
        ).length,
        lastYear: articles.filter(a => 
          new Date(a.publishDate) > new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)
        ).length
      },
      topResearchers: {},
      popularKeywords: {}
    };

    // 計算各種統計
    articles.forEach(article => {
      // 分類統計
      if (!stats.categories[article.category]) {
        stats.categories[article.category] = {
          count: 0,
          totalCitations: 0,
          totalDownloads: 0,
          averageRating: 0
        };
      }
      stats.categories[article.category].count++;
      stats.categories[article.category].totalCitations += article.citationCount;
      stats.categories[article.category].totalDownloads += article.downloadCount;

      // 方法統計
      stats.methodologies[article.methodology] = (stats.methodologies[article.methodology] || 0) + 1;

      // 機構統計
      stats.institutions[article.institution] = (stats.institutions[article.institution] || 0) + 1;

      // 研究者統計
      article.authors.forEach(author => {
        if (!stats.topResearchers[author]) {
          stats.topResearchers[author] = {
            articleCount: 0,
            totalCitations: 0,
            averageRating: 0
          };
        }
        stats.topResearchers[author].articleCount++;
        stats.topResearchers[author].totalCitations += article.citationCount;
      });

      // 關鍵詞統計
      article.keywords.forEach(keyword => {
        stats.popularKeywords[keyword] = (stats.popularKeywords[keyword] || 0) + 1;
      });
    });

    // 計算平均評分
    Object.keys(stats.categories).forEach(category => {
      const categoryArticles = articles.filter(a => a.category === category);
      stats.categories[category].averageRating = 
        Math.round((categoryArticles.reduce((sum, a) => sum + a.rating, 0) / categoryArticles.length) * 10) / 10;
    });

    res.json({
      message: 'Research statistics retrieved successfully',
      statistics: stats,
      generatedAt: new Date()
    });
  } catch (error) {
    console.error('Get research statistics error:', error);
    res.status(500).json({ error: 'Failed to fetch research statistics' });
  }
});

// 獲取研究分類
router.get('/categories', async (req, res) => {
  try {
    const categories = mockResearchData.categories;
    
    // 為每個分類添加文章計數
    const categoriesWithCounts = {};
    Object.keys(categories).forEach(categoryKey => {
      const category = categories[categoryKey];
      const articleCount = mockResearchData.articles.filter(a => a.category === categoryKey).length;
      
      categoriesWithCounts[categoryKey] = {
        ...category,
        articleCount,
        subcategoriesWithCounts: {}
      };

      // 為子分類添加計數
      Object.keys(category.subcategories).forEach(subKey => {
        const subArticleCount = mockResearchData.articles.filter(a => 
          a.category === categoryKey && a.subcategory === subKey
        ).length;
        
        categoriesWithCounts[categoryKey].subcategoriesWithCounts[subKey] = {
          name: category.subcategories[subKey],
          articleCount: subArticleCount
        };
      });
    });

    res.json({
      message: 'Research categories retrieved successfully',
      categories: categoriesWithCounts,
      totalCategories: Object.keys(categories).length,
      totalSubcategories: Object.values(categories).reduce((sum, cat) => sum + Object.keys(cat.subcategories).length, 0)
    });
  } catch (error) {
    console.error('Get research categories error:', error);
    res.status(500).json({ error: 'Failed to fetch research categories' });
  }
});

// 提交研究建議或請求
router.post('/suggestions', authenticate, [
  body('type').isIn(['topic_suggestion', 'collaboration_request', 'resource_request']).withMessage('Invalid suggestion type'),
  body('title').isLength({ min: 10, max: 200 }).withMessage('Title must be 10-200 characters'),
  body('description').isLength({ min: 50, max: 2000 }).withMessage('Description must be 50-2000 characters'),
  body('category').optional().isIn(['clinical', 'therapeutic', 'neuroscience', 'psychology']).withMessage('Invalid category'),
  body('priority').optional().isIn(['low', 'medium', 'high']).withMessage('Invalid priority')
], handleValidationErrors, async (req, res) => {
  try {
    const { type, title, description, category, priority = 'medium', contactInfo } = req.body;
    const userId = req.user._id;

    // 創建建議記錄（實際應儲存到數據庫）
    const suggestion = {
      id: Date.now().toString(),
      userId: userId,
      type: type,
      title: title,
      description: description,
      category: category,
      priority: priority,
      contactInfo: contactInfo,
      status: 'submitted',
      submittedAt: new Date(),
      updatedAt: new Date()
    };

    console.log('New research suggestion:', suggestion);

    res.json({
      message: 'Research suggestion submitted successfully',
      suggestion: {
        id: suggestion.id,
        type: suggestion.type,
        title: suggestion.title,
        status: suggestion.status,
        submittedAt: suggestion.submittedAt
      },
      nextSteps: [
        '我們會在3個工作日內審核您的建議',
        '相關研究團隊會評估可行性',
        '您會收到進度更新通知'
      ]
    });
  } catch (error) {
    console.error('Submit suggestion error:', error);
    res.status(500).json({ error: 'Failed to submit research suggestion' });
  }
});

module.exports = router;