<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>經典解讀 - 佛學智慧</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.6;
            color: #333;
            background: transparent;
            padding: 20px;
        }

        /* 分類介紹區 */
        .category-intro {
            background: linear-gradient(135deg, #2e7d32, #4caf50);
            color: white;
            padding: 40px 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .category-intro::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="white" opacity="0.1"/><circle cx="80" cy="80" r="1.5" fill="white" opacity="0.1"/><circle cx="50" cy="60" r="0.8" fill="white" opacity="0.1"/></svg>');
            background-size: 50px 50px;
            animation: sutras-drift 25s linear infinite;
        }

        @keyframes sutras-drift {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .intro-content {
            position: relative;
            z-index: 2;
        }

        .intro-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            animation: sutra-pulse 3s infinite;
        }

        @keyframes sutra-pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .intro-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .intro-description {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.8;
        }

        /* 經典分類 */
        .sutras-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .sutra-category {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .sutra-category:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .category-header {
            background: linear-gradient(135deg, #388e3c, #66bb6a);
            color: white;
            padding: 25px;
            text-align: center;
            position: relative;
        }

        .category-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .category-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .category-subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .category-content {
            padding: 25px;
        }

        .sutra-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sutra-item:last-child {
            border-bottom: none;
        }

        .sutra-item:hover {
            background: #f8f9fa;
            padding-left: 10px;
        }

        .sutra-icon {
            color: #4caf50;
            margin-right: 12px;
            font-size: 1.1rem;
        }

        .sutra-info {
            flex: 1;
        }

        .sutra-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }

        .sutra-desc {
            font-size: 0.85rem;
            color: #666;
            line-height: 1.4;
        }

        .sutra-meta {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 0.8rem;
            color: #999;
            margin-top: 8px;
        }

        /* 重點經典推薦 */
        .featured-sutras {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .featured-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2e7d32;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .featured-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .featured-item {
            background: linear-gradient(135deg, #e8f5e8, #f1f8e9);
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #4caf50;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .featured-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.2);
        }

        .featured-name {
            font-weight: 600;
            color: #2e7d32;
            margin-bottom: 8px;
        }

        .featured-summary {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.5;
            margin-bottom: 10px;
        }

        .featured-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .featured-tag {
            background: #4caf50;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        /* 學習指導 */
        .study-guide {
            background: linear-gradient(135deg, #fff8e1, #fffde7);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid #ffcc02;
        }

        .guide-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #f57f17;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .guide-steps {
            list-style: none;
            counter-reset: step-counter;
        }

        .guide-step {
            counter-increment: step-counter;
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .step-number {
            background: #ffc107;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
            flex-shrink: 0;
        }

        .step-number::before {
            content: counter(step-counter);
        }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-weight: 500;
            color: #f57f17;
            margin-bottom: 5px;
        }

        .step-desc {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.5;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }
            
            .category-intro {
                padding: 30px 20px;
            }
            
            .intro-title {
                font-size: 1.5rem;
            }
            
            .sutras-categories {
                grid-template-columns: 1fr;
            }
            
            .featured-grid {
                grid-template-columns: 1fr;
            }
            
            .featured-sutras,
            .study-guide {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- 分類介紹 -->
    <section class="category-intro">
        <div class="intro-content">
            <div class="intro-icon">
                <i class="fas fa-scroll"></i>
            </div>
            <h1 class="intro-title">經典解讀</h1>
            <p class="intro-description">
                佛教經典是佛陀智慧的結晶，是修行者的指路明燈<br>
                深入研讀經典，能幫助我們理解佛法的真諦，指導修行實踐
            </p>
        </div>
    </section>

    <!-- 經典分類 -->
    <section class="sutras-categories">
        <div class="sutra-category" data-category="agama">
            <div class="category-header">
                <div class="category-icon">
                    <i class="fas fa-book-open"></i>
                </div>
                <h3 class="category-title">阿含經典</h3>
                <p class="category-subtitle">根本佛教 · 四聖諦 · 八正道</p>
            </div>
            <div class="category-content">
                <div class="sutra-item" data-sutra="1">
                    <i class="fas fa-circle sutra-icon"></i>
                    <div class="sutra-info">
                        <div class="sutra-name">《長阿含經》</div>
                        <div class="sutra-desc">記錄佛陀的長篇開示，包含宇宙觀、修行法門等</div>
                        <div class="sutra-meta">
                            <span><i class="fas fa-clock"></i> 22卷</span>
                            <span><i class="fas fa-star"></i> 基礎</span>
                        </div>
                    </div>
                </div>
                <div class="sutra-item" data-sutra="2">
                    <i class="fas fa-circle sutra-icon"></i>
                    <div class="sutra-info">
                        <div class="sutra-name">《中阿含經》</div>
                        <div class="sutra-desc">中等長度的經典，涵蓋禪修、戒律、智慧</div>
                        <div class="sutra-meta">
                            <span><i class="fas fa-clock"></i> 60卷</span>
                            <span><i class="fas fa-star"></i> 進階</span>
                        </div>
                    </div>
                </div>
                <div class="sutra-item" data-sutra="3">
                    <i class="fas fa-circle sutra-icon"></i>
                    <div class="sutra-info">
                        <div class="sutra-name">《雜阿含經》</div>
                        <div class="sutra-desc">內容豐富多樣，是研究原始佛教的重要資料</div>
                        <div class="sutra-meta">
                            <span><i class="fas fa-clock"></i> 50卷</span>
                            <span><i class="fas fa-star"></i> 進階</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="sutra-category" data-category="prajnaparamita">
            <div class="category-header">
                <div class="category-icon">
                    <i class="fas fa-gem"></i>
                </div>
                <h3 class="category-title">般若經典</h3>
                <p class="category-subtitle">空性智慧 · 般若波羅蜜</p>
            </div>
            <div class="category-content">
                <div class="sutra-item" data-sutra="4">
                    <i class="fas fa-circle sutra-icon"></i>
                    <div class="sutra-info">
                        <div class="sutra-name">《般若波羅蜜多心經》</div>
                        <div class="sutra-desc">般若經的精華，260字濃縮空性智慧</div>
                        <div class="sutra-meta">
                            <span><i class="fas fa-clock"></i> 260字</span>
                            <span><i class="fas fa-star"></i> 必讀</span>
                        </div>
                    </div>
                </div>
                <div class="sutra-item" data-sutra="5">
                    <i class="fas fa-circle sutra-icon"></i>
                    <div class="sutra-info">
                        <div class="sutra-name">《金剛般若波羅蜜經》</div>
                        <div class="sutra-desc">金剛經，講述無住生心的般若智慧</div>
                        <div class="sutra-meta">
                            <span><i class="fas fa-clock"></i> 32分</span>
                            <span><i class="fas fa-star"></i> 熱門</span>
                        </div>
                    </div>
                </div>
                <div class="sutra-item" data-sutra="6">
                    <i class="fas fa-circle sutra-icon"></i>
                    <div class="sutra-info">
                        <div class="sutra-name">《大般若波羅蜜多經》</div>
                        <div class="sutra-desc">般若部的根本大經，內容極為豐富</div>
                        <div class="sutra-meta">
                            <span><i class="fas fa-clock"></i> 600卷</span>
                            <span><i class="fas fa-star"></i> 深入</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="sutra-category" data-category="lotus">
            <div class="category-header">
                <div class="category-icon">
                    <i class="fas fa-lotus"></i>
                </div>
                <h3 class="category-title">法華涅槃</h3>
                <p class="category-subtitle">一乘妙法 · 佛性常住</p>
            </div>
            <div class="category-content">
                <div class="sutra-item" data-sutra="7">
                    <i class="fas fa-circle sutra-icon"></i>
                    <div class="sutra-info">
                        <div class="sutra-name">《妙法蓮華經》</div>
                        <div class="sutra-desc">法華經，闡述一乘妙法，人人皆可成佛</div>
                        <div class="sutra-meta">
                            <span><i class="fas fa-clock"></i> 28品</span>
                            <span><i class="fas fa-star"></i> 重要</span>
                        </div>
                    </div>
                </div>
                <div class="sutra-item" data-sutra="8">
                    <i class="fas fa-circle sutra-icon"></i>
                    <div class="sutra-info">
                        <div class="sutra-name">《大般涅槃經》</div>
                        <div class="sutra-desc">涅槃經，講述如來常住不變，眾生皆有佛性</div>
                        <div class="sutra-meta">
                            <span><i class="fas fa-clock"></i> 40卷</span>
                            <span><i class="fas fa-star"></i> 深入</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="sutra-category" data-category="avatamsaka">
            <div class="category-header">
                <div class="category-icon">
                    <i class="fas fa-flower"></i>
                </div>
                <h3 class="category-title">華嚴經典</h3>
                <p class="category-subtitle">圓融無礙 · 法界觀</p>
            </div>
            <div class="category-content">
                <div class="sutra-item" data-sutra="9">
                    <i class="fas fa-circle sutra-icon"></i>
                    <div class="sutra-info">
                        <div class="sutra-name">《大方廣佛華嚴經》</div>
                        <div class="sutra-desc">華嚴經，描述毘盧遮那佛的境界</div>
                        <div class="sutra-meta">
                            <span><i class="fas fa-clock"></i> 80卷</span>
                            <span><i class="fas fa-star"></i> 高級</span>
                        </div>
                    </div>
                </div>
                <div class="sutra-item" data-sutra="10">
                    <i class="fas fa-circle sutra-icon"></i>
                    <div class="sutra-info">
                        <div class="sutra-name">《普賢行願品》</div>
                        <div class="sutra-desc">普賢菩薩的十大願王，菩薩行的典範</div>
                        <div class="sutra-meta">
                            <span><i class="fas fa-clock"></i> 1卷</span>
                            <span><i class="fas fa-star"></i> 實修</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 重點經典推薦 -->
    <section class="featured-sutras">
        <h2 class="featured-title">
            <i class="fas fa-star"></i>
            重點經典推薦
        </h2>
        <div class="featured-grid">
            <div class="featured-item" data-sutra="4">
                <div class="featured-name">《心經》</div>
                <div class="featured-summary">般若智慧的精華，短小精深，是學習空性的入門經典</div>
                <div class="featured-tags">
                    <span class="featured-tag">空性</span>
                    <span class="featured-tag">般若</span>
                    <span class="featured-tag">入門</span>
                </div>
            </div>
            <div class="featured-item" data-sutra="5">
                <div class="featured-name">《金剛經》</div>
                <div class="featured-summary">應無所住而生其心，破除我執法執的經典教授</div>
                <div class="featured-tags">
                    <span class="featured-tag">無住</span>
                    <span class="featured-tag">般若</span>
                    <span class="featured-tag">熱門</span>
                </div>
            </div>
            <div class="featured-item" data-sutra="7">
                <div class="featured-name">《法華經》</div>
                <div class="featured-summary">一乘妙法，開權顯實，人人皆可成佛的圓教經典</div>
                <div class="featured-tags">
                    <span class="featured-tag">一乘</span>
                    <span class="featured-tag">成佛</span>
                    <span class="featured-tag">圓教</span>
                </div>
            </div>
            <div class="featured-item" data-sutra="10">
                <div class="featured-name">《普賢行願品》</div>
                <div class="featured-summary">普賢十大願王，菩薩行者的修行指南</div>
                <div class="featured-tags">
                    <span class="featured-tag">行願</span>
                    <span class="featured-tag">菩薩行</span>
                    <span class="featured-tag">實修</span>
                </div>
            </div>
        </div>
    </section>

    <!-- 學習指導 -->
    <section class="study-guide">
        <h2 class="guide-title">
            <i class="fas fa-graduation-cap"></i>
            經典學習指導
        </h2>
        <ol class="guide-steps">
            <li class="guide-step">
                <div class="step-number"></div>
                <div class="step-content">
                    <div class="step-title">建立基礎認知</div>
                    <div class="step-desc">先了解佛教基本概念：四聖諦、八正道、因緣法等，建立正確的學習基礎</div>
                </div>
            </li>
            <li class="guide-step">
                <div class="step-number"></div>
                <div class="step-content">
                    <div class="step-title">從短經入手</div>
                    <div class="step-desc">建議從《心經》、《金剛經》等短小經典開始，培養讀經的習慣和興趣</div>
                </div>
            </li>
            <li class="guide-step">
                <div class="step-number"></div>
                <div class="step-content">
                    <div class="step-title">參考註解指導</div>
                    <div class="step-desc">閱讀祖師大德的註解和現代法師的講解，幫助理解經典的深層含義</div>
                </div>
            </li>
            <li class="guide-step">
                <div class="step-number"></div>
                <div class="step-content">
                    <div class="step-title">結合修行實踐</div>
                    <div class="step-desc">將經典教理應用到日常修行中，理論與實踐相結合</div>
                </div>
            </li>
            <li class="guide-step">
                <div class="step-number"></div>
                <div class="step-content">
                    <div class="step-title">循序漸進深入</div>
                    <div class="step-desc">逐步學習更深層的經典，如《法華經》、《華嚴經》等大部經典</div>
                </div>
            </li>
        </ol>
    </section>

    <script>
        // 經典點擊事件
        document.addEventListener('click', function(e) {
            const sutraItem = e.target.closest('.sutra-item, .featured-item');
            if (sutraItem) {
                const sutraId = sutraItem.getAttribute('data-sutra');
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'navigate',
                        page: 'article',
                        articleId: sutraId,
                        category: 'sutras'
                    }, '*');
                }
            }
        });

        // 分類點擊事件
        document.addEventListener('click', function(e) {
            const category = e.target.closest('.sutra-category');
            if (category) {
                const categoryType = category.getAttribute('data-category');
                // 可以在這裡處理分類篩選邏輯
                console.log('Selected category:', categoryType);
            }
        });

        // 添加載入動畫
        document.addEventListener('DOMContentLoaded', function() {
            const categories = document.querySelectorAll('.sutra-category');
            categories.forEach((category, index) => {
                category.style.animationDelay = `${index * 0.1}s`;
                category.style.animation = 'fadeInUp 0.6s ease forwards';
            });
        });

        // CSS 動畫
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            .sutra-category {
                opacity: 0;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>