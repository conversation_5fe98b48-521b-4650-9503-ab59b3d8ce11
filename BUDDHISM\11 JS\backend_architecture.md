# 佛教身心療癒網站 - 後端系統架構設計

## 🎯 後端開發概述

基於完整的16頁前端系統，我將設計一個現代化、可擴展的後端架構，支撐所有核心功能。

## 🏗️ 技術架構設計

### 核心技術棧
```
📦 後端框架: Node.js + Express.js
🗄️ 資料庫: MongoDB + Redis (快取)
🔐 身份驗證: JWT + OAuth 2.0
☁️ 雲端服務: AWS / Google Cloud
📡 API設計: RESTful API + GraphQL
🔍 搜尋引擎: Elasticsearch
📧 通訊服務: Socket.io (即時通訊)
📊 分析工具: Google Analytics + 自建分析
```

## 📋 API端點設計

### 1. 用戶管理系統
```javascript
// 用戶註冊登入
POST   /api/auth/register        // 用戶註冊
POST   /api/auth/login           // 用戶登入
POST   /api/auth/logout          // 用戶登出
POST   /api/auth/refresh         // 刷新token
POST   /api/auth/forgot-password // 忘記密碼
POST   /api/auth/reset-password  // 重置密碼

// 用戶資料管理
GET    /api/users/profile        // 獲取用戶資料
PUT    /api/users/profile        // 更新用戶資料
GET    /api/users/progress       // 學習進度
PUT    /api/users/progress       // 更新進度
GET    /api/users/certificates   // 用戶證書
```

### 2. 會員系統
```javascript
// 會員方案管理
GET    /api/membership/plans     // 獲取會員方案
POST   /api/membership/subscribe // 訂閱會員
PUT    /api/membership/upgrade   // 升級會員
DELETE /api/membership/cancel    // 取消會員
GET    /api/membership/benefits  // 會員權益
GET    /api/membership/history   // 訂閱歷史
```

### 3. 課程管理系統
```javascript
// 課程內容
GET    /api/courses              // 獲取課程列表
GET    /api/courses/:id          // 獲取特定課程
POST   /api/courses              // 創建課程(管理員)
PUT    /api/courses/:id          // 更新課程
DELETE /api/courses/:id          // 刪除課程

// 學習進度
POST   /api/courses/:id/enroll   // 報名課程
GET    /api/courses/:id/progress // 課程進度
PUT    /api/courses/:id/progress // 更新進度
POST   /api/courses/:id/complete // 完成課程
```

### 4. 經文搜尋系統
```javascript
// 經文管理
GET    /api/scriptures/search    // 搜尋經文
GET    /api/scriptures/:id       // 獲取特定經文
GET    /api/scriptures/categories // 經文分類
POST   /api/scriptures/bookmark  // 收藏經文
DELETE /api/scriptures/bookmark/:id // 取消收藏
GET    /api/scriptures/bookmarks // 我的收藏

// 療癒方法
GET    /api/healing/methods      // 療癒方法列表
GET    /api/healing/theory       // 療癒理論
POST   /api/healing/practice     // 記錄修習
GET    /api/healing/history      // 修習歷史
```

### 5. 研究文獻系統
```javascript
// 研究文獻
GET    /api/research/search      // 搜尋研究
GET    /api/research/:id         // 獲取研究詳情
GET    /api/research/categories  // 研究分類
POST   /api/research/cite        // 引用文獻
GET    /api/research/trending    // 熱門研究
```

### 6. 藏醫專題系統
```javascript
// 藏醫理論
GET    /api/tibetan/theory       // 三根理論
GET    /api/tibetan/diagnosis    // 診斷方法
GET    /api/tibetan/medicine     // 藏藥知識

// 藏醫實習
POST   /api/tibetan/apply        // 申請實習
GET    /api/tibetan/applications // 實習申請狀態
PUT    /api/tibetan/applications/:id // 更新申請
```

### 7. 海外實習系統
```javascript
// 實習成員管理
GET    /api/overseas/members     // 實習成員列表
POST   /api/overseas/join        // 加入實習
GET    /api/overseas/programs    // 實習計畫
POST   /api/overseas/apply       // 申請實習

// 研究成果
GET    /api/overseas/research    // 研究成果
POST   /api/overseas/publish     // 發表成果
GET    /api/overseas/statistics  // 統計數據
```

### 8. 工作坊系統
```javascript
// 工作坊管理
GET    /api/workshops            // 工作坊列表
GET    /api/workshops/:id        // 工作坊詳情
POST   /api/workshops/register   // 報名工作坊
GET    /api/workshops/calendar   // 活動行事曆
PUT    /api/workshops/cancel     // 取消報名
```

### 9. 內容管理系統
```javascript
// CMS管理
GET    /api/cms/articles         // 文章管理
POST   /api/cms/articles         // 創建文章
PUT    /api/cms/articles/:id     // 更新文章
DELETE /api/cms/articles/:id     // 刪除文章
GET    /api/cms/media           // 媒體管理
POST   /api/cms/upload          // 檔案上傳
```

## 🗄️ 資料庫設計

### MongoDB 集合結構

#### Users Collection
```javascript
{
  _id: ObjectId,
  email: String,
  password: String (hashed),
  profile: {
    firstName: String,
    lastName: String,
    avatar: String,
    phone: String,
    location: String,
    timezone: String
  },
  membership: {
    plan: String, // basic, premium, vip
    status: String, // active, cancelled, expired
    startDate: Date,
    endDate: Date,
    autoRenewal: Boolean
  },
  preferences: {
    language: String,
    notifications: Object,
    privacy: Object
  },
  progress: {
    coursesCompleted: [ObjectId],
    currentCourses: [ObjectId],
    certificates: [ObjectId],
    totalStudyTime: Number,
    lastActiveDate: Date
  },
  created: Date,
  updated: Date
}
```

#### Courses Collection
```javascript
{
  _id: ObjectId,
  title: String,
  description: String,
  category: String,
  difficulty: String, // beginner, intermediate, advanced
  duration: Number, // minutes
  instructor: ObjectId,
  content: {
    modules: [{
      title: String,
      description: String,
      videos: [String],
      documents: [String],
      assignments: [Object]
    }]
  },
  prerequisites: [ObjectId],
  tags: [String],
  pricing: {
    free: Boolean,
    membershipRequired: String,
    price: Number
  },
  statistics: {
    enrollments: Number,
    completions: Number,
    averageRating: Number,
    reviews: [ObjectId]
  },
  status: String, // draft, published, archived
  created: Date,
  updated: Date
}
```

#### Scriptures Collection
```javascript
{
  _id: ObjectId,
  title: String,
  originalTitle: String,
  category: String,
  subcategory: String,
  content: {
    text: String,
    translation: String,
    commentary: String,
    audioUrl: String
  },
  metadata: {
    author: String,
    translator: String,
    source: String,
    language: String,
    period: String
  },
  healing: {
    applications: [String],
    techniques: [String],
    benefits: [String]
  },
  tags: [String],
  searchKeywords: [String],
  difficulty: String,
  readingTime: Number,
  popularity: Number,
  created: Date,
  updated: Date
}
```

#### Research Collection
```javascript
{
  _id: ObjectId,
  title: String,
  abstract: String,
  authors: [String],
  publication: {
    journal: String,
    year: Number,
    volume: String,
    pages: String,
    doi: String
  },
  category: String,
  keywords: [String],
  methodology: String,
  findings: String,
  citations: Number,
  pdfUrl: String,
  accessibility: String, // public, members, vip
  relatedStudies: [ObjectId],
  created: Date,
  updated: Date
}
```

#### Workshops Collection
```javascript
{
  _id: ObjectId,
  title: String,
  description: String,
  instructor: ObjectId,
  type: String, // meditation, healing, tibetan, weekend
  format: String, // online, offline, hybrid
  schedule: {
    startDate: Date,
    endDate: Date,
    duration: Number,
    timezone: String
  },
  location: {
    type: String, // online, physical
    address: String,
    coordinates: Object,
    onlineLink: String
  },
  capacity: {
    total: Number,
    registered: Number,
    waitlist: Number
  },
  pricing: {
    regular: Number,
    member: Number,
    vip: Number
  },
  requirements: [String],
  materials: [String],
  status: String, // upcoming, ongoing, completed, cancelled
  participants: [ObjectId],
  feedback: [ObjectId],
  created: Date,
  updated: Date
}
```

## 🔐 安全性設計

### 身份驗證與授權
```javascript
// JWT Token 結構
{
  "sub": "user_id",
  "email": "<EMAIL>",
  "role": "member",
  "membership": "premium",
  "exp": timestamp,
  "iat": timestamp
}

// 權限控制中間件
const authorize = (roles = []) => {
  return (req, res, next) => {
    const user = req.user;
    if (!user || !roles.includes(user.role)) {
      return res.status(403).json({
        error: 'Insufficient permissions'
      });
    }
    next();
  };
};
```

### 資料驗證
```javascript
// 使用 Joi 進行資料驗證
const userRegistrationSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(8).pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/).required(),
  firstName: Joi.string().min(2).max(50).required(),
  lastName: Joi.string().min(2).max(50).required(),
  phone: Joi.string().pattern(/^\+?[0-9\-\s]+$/),
  agreeToTerms: Joi.boolean().valid(true).required()
});
```

## 📡 API 速率限制
```javascript
const rateLimit = require("express-rate-limit");

// 一般API限制
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分鐘
  max: 100, // 限制每個IP 100次請求
  message: "Too many requests from this IP"
});

// 登入API限制
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 5, // 限制登入嘗試
  skipSuccessfulRequests: true
});
```

## 🔍 搜尋系統 (Elasticsearch)

### 經文搜尋索引
```javascript
// Elasticsearch 索引設定
{
  "mappings": {
    "properties": {
      "title": {
        "type": "text",
        "analyzer": "cjk",
        "fields": {
          "keyword": {
            "type": "keyword"
          }
        }
      },
      "content": {
        "type": "text",
        "analyzer": "cjk"
      },
      "category": {
        "type": "keyword"
      },
      "tags": {
        "type": "keyword"
      },
      "difficulty": {
        "type": "keyword"
      },
      "popularity": {
        "type": "integer"
      },
      "created": {
        "type": "date"
      }
    }
  }
}
```

### 智能搜尋算法
```javascript
const searchScriptures = async (query, filters = {}) => {
  const searchBody = {
    query: {
      bool: {
        must: [
          {
            multi_match: {
              query: query,
              fields: ["title^3", "content^2", "tags"],
              type: "best_fields",
              fuzziness: "AUTO"
            }
          }
        ],
        filter: []
      }
    },
    highlight: {
      fields: {
        title: {},
        content: {}
      }
    },
    sort: [
      { _score: { order: "desc" } },
      { popularity: { order: "desc" } }
    ]
  };

  // 添加篩選條件
  if (filters.category) {
    searchBody.query.bool.filter.push({
      term: { category: filters.category }
    });
  }

  return await elasticsearchClient.search({
    index: 'scriptures',
    body: searchBody
  });
};
```

## 📊 分析與監控系統

### 用戶行為追蹤
```javascript
// 學習進度追蹤
const trackLearningProgress = async (userId, courseId, moduleId, progress) => {
  await ProgressTracking.findOneAndUpdate(
    { userId, courseId },
    {
      $set: {
        lastAccessed: new Date(),
        currentModule: moduleId,
        overallProgress: progress
      },
      $inc: {
        totalStudyTime: calculateStudyTime(),
        moduleCompletions: progress === 100 ? 1 : 0
      }
    },
    { upsert: true }
  );

  // 觸發成就系統
  await checkAchievements(userId);
};
```

### 系統監控
```javascript
// 健康檢查端點
app.get('/health', async (req, res) => {
  const healthCheck = {
    uptime: process.uptime(),
    timestamp: Date.now(),
    status: 'OK',
    services: {
      database: await checkDatabaseConnection(),
      redis: await checkRedisConnection(),
      elasticsearch: await checkElasticsearchConnection()
    }
  };

  res.status(200).json(healthCheck);
});
```

## 🚀 部署架構

### Docker 容器化
```dockerfile
# Dockerfile
FROM node:16-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000

CMD ["npm", "start"]
```

### Docker Compose
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URL=mongodb://mongo:27017/buddhist_healing
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mongo
      - redis
      - elasticsearch

  mongo:
    image: mongo:5.0
    volumes:
      - mongo_data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password

  redis:
    image: redis:6.2-alpine
    volumes:
      - redis_data:/data

  elasticsearch:
    image: elasticsearch:7.14.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

volumes:
  mongo_data:
  redis_data:
  elasticsearch_data:
```

## 📧 通知系統

### 電子郵件服務
```javascript
// 郵件模板
const emailTemplates = {
  welcome: {
    subject: '歡迎加入佛教身心療癒社群',
    template: 'welcome.html'
  },
  courseCompletion: {
    subject: '恭喜完成課程！',
    template: 'course-completion.html'
  },
  membershipExpiring: {
    subject: '會員即將到期提醒',
    template: 'membership-expiring.html'
  }
};

// 發送郵件
const sendEmail = async (to, templateName, data) => {
  const template = emailTemplates[templateName];
  const html = await renderTemplate(template.template, data);
  
  await transporter.sendMail({
    from: process.env.SMTP_FROM,
    to: to,
    subject: template.subject,
    html: html
  });
};
```

## 💳 支付系統整合

### Stripe 支付處理
```javascript
// 創建訂閱
const createSubscription = async (userId, planId) => {
  const user = await User.findById(userId);
  
  // 創建或獲取客戶
  let customer = await stripe.customers.retrieve(user.stripeCustomerId);
  if (!customer) {
    customer = await stripe.customers.create({
      email: user.email,
      name: `${user.profile.firstName} ${user.profile.lastName}`
    });
    
    await User.findByIdAndUpdate(userId, {
      stripeCustomerId: customer.id
    });
  }

  // 創建訂閱
  const subscription = await stripe.subscriptions.create({
    customer: customer.id,
    items: [{ price: planId }],
    payment_behavior: 'default_incomplete',
    expand: ['latest_invoice.payment_intent']
  });

  return subscription;
};
```

## 🔔 即時通知 (Socket.io)

### WebSocket 事件處理
```javascript
// Socket.io 事件
io.on('connection', (socket) => {
  // 用戶加入個人房間
  socket.on('join', (userId) => {
    socket.join(`user_${userId}`);
  });

  // 學習進度更新
  socket.on('progress_update', (data) => {
    // 更新資料庫
    updateLearningProgress(data);
    
    // 通知其他相關用戶
    socket.to(`course_${data.courseId}`).emit('peer_progress', {
      userId: data.userId,
      progress: data.progress
    });
  });

  // 即時聊天
  socket.on('chat_message', (data) => {
    io.to(`workshop_${data.workshopId}`).emit('new_message', {
      user: data.user,
      message: data.message,
      timestamp: new Date()
    });
  });
});
```

## 📈 效能優化

### Redis 快取策略
```javascript
// 快取熱門內容
const getCachedScriptures = async (category) => {
  const cacheKey = `scriptures:${category}`;
  
  // 嘗試從快取獲取
  let scriptures = await redis.get(cacheKey);
  
  if (!scriptures) {
    // 從資料庫查詢
    scriptures = await Scripture.find({ category })
      .sort({ popularity: -1 })
      .limit(20);
    
    // 存入快取，TTL 1小時
    await redis.setex(cacheKey, 3600, JSON.stringify(scriptures));
  } else {
    scriptures = JSON.parse(scriptures);
  }
  
  return scriptures;
};
```

### 資料庫查詢優化
```javascript
// 建立索引
db.users.createIndex({ email: 1 }, { unique: true });
db.courses.createIndex({ category: 1, difficulty: 1 });
db.scriptures.createIndex({ tags: 1, popularity: -1 });
db.workshops.createIndex({ "schedule.startDate": 1, status: 1 });

// 分頁查詢
const getPaginatedCourses = async (page = 1, limit = 20, filters = {}) => {
  const skip = (page - 1) * limit;
  
  const query = Course.find(filters)
    .sort({ created: -1 })
    .skip(skip)
    .limit(limit)
    .populate('instructor', 'profile.firstName profile.lastName');
    
  const [courses, total] = await Promise.all([
    query.exec(),
    Course.countDocuments(filters)
  ]);
  
  return {
    courses,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  };
};
```

## 🧪 測試策略

### 單元測試範例
```javascript
// Jest 測試範例
describe('User Authentication', () => {
  test('should register new user successfully', async () => {
    const userData = {
      email: '<EMAIL>',
      password: 'Password123!',
      firstName: 'Test',
      lastName: 'User'
    };

    const response = await request(app)
      .post('/api/auth/register')
      .send(userData)
      .expect(201);

    expect(response.body).toHaveProperty('token');
    expect(response.body.user.email).toBe(userData.email);
  });

  test('should not register user with invalid email', async () => {
    const userData = {
      email: 'invalid-email',
      password: 'Password123!',
      firstName: 'Test',
      lastName: 'User'
    };

    await request(app)
      .post('/api/auth/register')
      .send(userData)
      .expect(400);
  });
});
```

## 📝 API 文檔

### Swagger/OpenAPI 規範
```yaml
openapi: 3.0.0
info:
  title: 佛教身心療癒網站 API
  version: 1.0.0
  description: 完整的後端API文檔

paths:
  /api/auth/register:
    post:
      summary: 用戶註冊
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  minLength: 8
                firstName:
                  type: string
                lastName:
                  type: string
      responses:
        '201':
          description: 註冊成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                  user:
                    $ref: '#/components/schemas/User'
```

## 🔄 CI/CD 流程

### GitHub Actions
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
      - name: Install dependencies
        run: npm ci
      - name: Run tests
        run: npm test
      - name: Run security audit
        run: npm audit

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to AWS
        run: |
          # 部署腳本
          aws ecs update-service --cluster production --service api --force-new-deployment
```

---

## 🚀 下一步行動

### 立即可執行的開發任務

1. **建立開發環境**
   ```bash
   mkdir buddhist-healing-backend
   cd buddhist-healing-backend
   npm init -y
   npm install express mongoose redis socket.io jsonwebtoken bcryptjs
   ```

2. **創建基礎項目結構**
   ```
   src/
   ├── controllers/
   ├── models/
   ├── routes/
   ├── middleware/
   ├── services/
   ├── utils/
   └── app.js
   ```

3. **優先開發模組**
   - 用戶註冊登入系統
   - 會員管理系統
   - 課程管理API
   - 經文搜尋功能

### 預期開發時程

- **第1週**: 基礎架構 + 用戶系統
- **第2週**: 會員系統 + 支付整合
- **第3週**: 課程管理 + 內容系統
- **第4週**: 搜尋功能 + 優化

**準備開始後端開發了嗎？** 🚀

我可以協助您：
1. 創建具體的 API 端點代碼
2. 設計資料庫 Schema
3. 實現特定功能模組
4. 配置部署環境

請告訴我您希望從哪個模組開始！