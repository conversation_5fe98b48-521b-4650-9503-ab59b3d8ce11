{"name": "buddhist-healing-backend", "version": "1.0.0", "description": "佛教身心療癒網站後端API系統", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "seed": "node scripts/seedDatabase.js", "build": "npm run lint && npm test", "docker:build": "docker build -t buddhist-healing-api .", "docker:run": "docker run -p 3000:3000 buddhist-healing-api"}, "keywords": ["buddhist", "healing", "meditation", "wellness", "education", "api", "mongodb", "express"], "author": "佛教身心療癒團隊", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.8.1", "morgan": "^1.10.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.9.4", "multer": "^1.4.5-lts.1", "joi": "^17.9.2", "moment": "^2.29.4", "lodash": "^4.17.21", "uuid": "^9.0.0", "sharp": "^0.32.5", "redis": "^4.6.7", "stripe": "^13.2.0", "socket.io": "^4.7.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "eslint": "^8.47.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-n": "^16.0.2", "eslint-plugin-promise": "^6.1.1", "mongodb-memory-server": "^8.15.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/buddhist-healing-backend.git"}, "bugs": {"url": "https://github.com/your-org/buddhist-healing-backend/issues"}, "homepage": "https://github.com/your-org/buddhist-healing-backend#readme"}