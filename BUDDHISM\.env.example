# 佛教身心療癒網站 - 環境變數範本
# 複製此文件為 .env 並填入實際值

# ================================
# 🌍 基本服務器配置
# ================================
NODE_ENV=development
PORT=3000

# ================================
# 🗄️ 資料庫配置
# ================================
# MongoDB 連接字串
MONGODB_URI=mongodb://localhost:27017/buddhist_healing

# Redis 連接字串 (可選，用於緩存)
REDIS_URL=redis://localhost:6379

# ================================
# 🔐 JWT 認證配置
# ================================
# JWT 密鑰 (生產環境請使用強密碼，至少32個字符)
JWT_SECRET=your_super_secret_jwt_key_change_in_production_minimum_32_characters

# JWT 刷新密鑰
JWT_REFRESH_SECRET=your_refresh_secret_key_also_minimum_32_characters

# JWT 過期時間
JWT_EXPIRES_IN=7d

# ================================
# 📧 郵件服務配置
# ================================

# 開發環境 (使用 Ethereal Email 測試)
SMTP_HOST=smtp.ethereal.email
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=your_ethereal_username
SMTP_PASS=your_ethereal_password

# 生產環境示例 (SendGrid)
# SMTP_HOST=smtp.sendgrid.net
# SMTP_PORT=587
# SMTP_SECURE=false
# SMTP_USER=apikey
# SMTP_PASS=your_sendgrid_api_key

# 生產環境示例 (Gmail)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_SECURE=false
# SMTP_USER=your_gmail_address
# SMTP_PASS=your_app_specific_password

# 寄件者郵件地址
SMTP_FROM=<EMAIL>

# ================================
# 🔑 API 金鑰配置
# ================================
# 內部 API 金鑰 (用於系統間調用)
API_KEY=your_internal_api_key_minimum_32_characters

# ================================
# 💳 支付服務配置 (可選)
# ================================
# Stripe 配置
STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# ================================
# ☁️ 雲端服務配置 (可選)
# ================================
# AWS S3 (用於文件上傳)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=ap-northeast-1
AWS_S3_BUCKET=buddhist-healing-uploads

# ================================
# 📊 監控與分析配置 (可選)
# ================================
# Sentry 錯誤追蹤
SENTRY_DSN=https://<EMAIL>/project_id

# Google Analytics
GA_TRACKING_ID=GA-XXXXXXXXX

# ================================
# 🔒 SSL 配置 (生產環境)
# ================================
# SSL 證書路徑
SSL_CERT_PATH=/path/to/ssl/certificate.crt
SSL_KEY_PATH=/path/to/ssl/private.key

# ================================
# 📱 推送通知配置 (可選)
# ================================
# Firebase Cloud Messaging
FCM_SERVER_KEY=your_fcm_server_key

# ================================
# 🌐 CORS 配置
# ================================
# 允許的前端域名 (生產環境)
ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com

# ================================
# 📈 日誌配置
# ================================
# 日誌等級 (error, warn, info, debug)
LOG_LEVEL=info

# ================================
# ⚡ 性能配置
# ================================
# 速率限制 (每15分鐘的請求次數)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 文件上傳大小限制 (MB)
MAX_FILE_SIZE=10

# ================================
# 🔧 開發工具配置
# ================================
# 啟用 API 文檔 (開發環境)
ENABLE_API_DOCS=true

# 啟用詳細錯誤信息 (開發環境)
ENABLE_ERROR_DETAILS=true

# ================================
# 💾 快取配置
# ================================
# 快取過期時間 (秒)
CACHE_TTL=3600

# 快取前綴
CACHE_PREFIX=bh:

# ================================
# 🕐 時區配置
# ================================
# 預設時區
DEFAULT_TIMEZONE=Asia/Taipei

# ================================
# 📚 資料庫選項
# ================================
# MongoDB 連接選項
MONGODB_MAX_POOL_SIZE=10
MONGODB_MIN_POOL_SIZE=5
MONGODB_MAX_IDLE_TIME=30000

# ================================
# 🛡️ 安全選項
# ================================
# CORS 配置
CORS_ORIGIN=*
CORS_CREDENTIALS=true

# Cookie 安全設置
COOKIE_SECURE=false
COOKIE_HTTP_ONLY=true
COOKIE_SAME_SITE=lax

# 密碼重置 token 過期時間 (毫秒)
PASSWORD_RESET_EXPIRES=600000

# 郵件驗證 token 過期時間 (毫秒)
EMAIL_VERIFICATION_EXPIRES=********

# 帳戶鎖定設置
MAX_LOGIN_ATTEMPTS=5
ACCOUNT_LOCK_TIME=7200000

# ================================
# 📧 郵件模板配置
# ================================
# 郵件模板目錄
EMAIL_TEMPLATES_DIR=./templates/emails

# 網站基礎 URL (用於郵件中的連結)
BASE_URL=http://localhost:3000

# ================================
# 🎨 UI 配置
# ================================
# 主題色彩
PRIMARY_COLOR=#2C3E50
SECONDARY_COLOR=#F39C12

# 網站名稱
SITE_NAME=佛教身心療癒網站

# 聯絡郵件
CONTACT_EMAIL=<EMAIL>

# ================================
# 📱 推送通知設置
# ================================
# 啟用推送通知
ENABLE_PUSH_NOTIFICATIONS=false

# Vapid 金鑰 (用於 Web Push)
VAPID_PUBLIC_KEY=your_vapid_public_key
VAPID_PRIVATE_KEY=your_vapid_private_key
VAPID_SUBJECT=mailto:<EMAIL>