// 監控與分析系統
// 包含效能監控、用戶行為分析、系統健康檢查等功能

const promClient = require('prom-client');
const express = require('express');
const EventEmitter = require('events');

// ========== Prometheus 指標設定 ==========

// 創建預設指標註冊表
const register = new promClient.register();
promClient.collectDefaultMetrics({ register });

// 自定義指標
const httpRequestDuration = new promClient.Histogram({
  name: 'http_request_duration_seconds',
  help: 'HTTP request duration in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.5, 1, 2, 5]
});

const httpRequestTotal = new promClient.Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code']
});

const activeUsers = new promClient.Gauge({
  name: 'active_users_total',
  help: 'Number of currently active users'
});

const courseEnrollments = new promClient.Counter({
  name: 'course_enrollments_total',
  help: 'Total number of course enrollments',
  labelNames: ['course_id', 'membership_type']
});

const scriptureViews = new promClient.Counter({
  name: 'scripture_views_total',
  help: 'Total number of scripture views',
  labelNames: ['scripture_id', 'category']
});

const databaseConnections = new promClient.Gauge({
  name: 'database_connections_active',
  help: 'Number of active database connections'
});

const memoryUsage = new promClient.Gauge({
  name: 'nodejs_memory_usage_bytes',
  help: 'Node.js memory usage',
  labelNames: ['type']
});

// 註冊指標
register.registerMetric(httpRequestDuration);
register.registerMetric(httpRequestTotal);
register.registerMetric(activeUsers);
register.registerMetric(courseEnrollments);
register.registerMetric(scriptureViews);
register.registerMetric(databaseConnections);
register.registerMetric(memoryUsage);

// ========== 監控中間件 ==========

const metricsMiddleware = (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    const route = req.route ? req.route.path : req.path;
    
    httpRequestDuration
      .labels(req.method, route, res.statusCode)
      .observe(duration);
    
    httpRequestTotal
      .labels(req.method, route, res.statusCode)
      .inc();
  });
  
  next();
};

// 記憶體使用監控
setInterval(() => {
  const usage = process.memoryUsage();
  memoryUsage.labels('rss').set(usage.rss);
  memoryUsage.labels('heapUsed').set(usage.heapUsed);
  memoryUsage.labels('heapTotal').set(usage.heapTotal);
  memoryUsage.labels('external').set(usage.external);
}, 10000);

// ========== 用戶行為分析系統 ==========

class AnalyticsService extends EventEmitter {
  constructor() {
    super();
    this.activeUserSessions = new Map();
    this.userBehaviorQueue = [];
    this.processingInterval = null;
    
    this.startProcessing();
  }

  // 記錄用戶登入
  trackUserLogin(userId, userAgent, ipAddress) {
    const sessionData = {
      userId,
      userAgent,
      ipAddress,
      loginTime: new Date(),
      lastActivity: new Date(),
      pageViews: [],
      actions: []
    };
    
    this.activeUserSessions.set(userId, sessionData);
    activeUsers.set(this.activeUserSessions.size);
    
    this.emit('user_login', {
      userId,
      timestamp: new Date(),
      userAgent,
      ipAddress
    });
  }

  // 記錄用戶登出
  trackUserLogout(userId) {
    if (this.activeUserSessions.has(userId)) {
      const session = this.activeUserSessions.get(userId);
      const sessionDuration = new Date() - session.loginTime;
      
      this.emit('user_logout', {
        userId,
        sessionDuration,
        pageViews: session.pageViews.length,
        actions: session.actions.length,
        timestamp: new Date()
      });
      
      this.activeUserSessions.delete(userId);
      activeUsers.set(this.activeUserSessions.size);
    }
  }

  // 記錄頁面瀏覽
  trackPageView(userId, page, referrer = null) {
    if (this.activeUserSessions.has(userId)) {
      const session = this.activeUserSessions.get(userId);
      session.lastActivity = new Date();
      session.pageViews.push({
        page,
        referrer,
        timestamp: new Date()
      });
    }
    
    this.queueEvent('page_view', {
      userId,
      page,
      referrer,
      timestamp: new Date()
    });
  }

  // 記錄用戶行為
  trackUserAction(userId, action, data = {}) {
    if (this.activeUserSessions.has(userId)) {
      const session = this.activeUserSessions.get(userId);
      session.lastActivity = new Date();
      session.actions.push({
        action,
        data,
        timestamp: new Date()
      });
    }
    
    this.queueEvent('user_action', {
      userId,
      action,
      data,
      timestamp: new Date()
    });

    // 特定行為的指標更新
    switch (action) {
      case 'course_enrollment':
        courseEnrollments
          .labels(data.courseId, data.membershipType)
          .inc();
        break;
      case 'scripture_view':
        scriptureViews
          .labels(data.scriptureId, data.category)
          .inc();
        break;
    }
  }

  // 記錄學習進度
  trackLearningProgress(userId, courseId, moduleId, progress, timeSpent) {
    this.queueEvent('learning_progress', {
      userId,
      courseId,
      moduleId,
      progress,
      timeSpent,
      timestamp: new Date()
    });
  }

  // 記錄搜尋行為
  trackSearch(userId, query, category, results) {
    this.queueEvent('search', {
      userId,
      query,
      category,
      resultCount: results.length,
      timestamp: new Date()
    });
  }

  // 將事件加入佇列
  queueEvent(eventType, data) {
    this.userBehaviorQueue.push({
      eventType,
      data,
      timestamp: new Date()
    });
  }

  // 開始處理佇列
  startProcessing() {
    this.processingInterval = setInterval(() => {
      this.processQueue();
    }, 5000); // 每5秒處理一次
  }

  // 處理佇列事件
  async processQueue() {
    if (this.userBehaviorQueue.length === 0) return;

    const events = this.userBehaviorQueue.splice(0, 100); // 一次處理100個事件
    
    try {
      // 批量寫入資料庫
      await this.saveEventsToDatabase(events);
      
      // 即時分析
      await this.performRealTimeAnalysis(events);
      
    } catch (error) {
      console.error('Error processing analytics queue:', error);
      // 如果失敗，將事件重新加入佇列
      this.userBehaviorQueue.unshift(...events);
    }
  }

  // 儲存事件到資料庫
  async saveEventsToDatabase(events) {
    const AnalyticsEvent = require('../models/AnalyticsEvent');
    
    const documents = events.map(event => ({
      type: event.eventType,
      userId: event.data.userId,
      data: event.data,
      timestamp: event.timestamp
    }));
    
    await AnalyticsEvent.insertMany(documents);
  }

  // 即時分析
  async performRealTimeAnalysis(events) {
    // 檢測異常行為
    this.detectAnomalousActivity(events);
    
    // 更新即時統計
    this.updateRealTimeStats(events);
    
    // 個性化推薦更新
    this.updateRecommendations(events);
  }

  // 檢測異常行為
  detectAnomalousActivity(events) {
    const suspiciousEvents = events.filter(event => {
      // 檢測可疑的搜尋模式
      if (event.eventType === 'search' && event.data.query.length > 100) {
        return true;
      }
      
      // 檢測短時間內大量請求
      const userEvents = events.filter(e => e.data.userId === event.data.userId);
      if (userEvents.length > 50) {
        return true;
      }
      
      return false;
    });
    
    if (suspiciousEvents.length > 0) {
      this.emit('suspicious_activity', suspiciousEvents);
    }
  }

  // 更新即時統計
  updateRealTimeStats(events) {
    const stats = {
      totalEvents: events.length,
      uniqueUsers: new Set(events.map(e => e.data.userId)).size,
      eventTypes: {}
    };
    
    events.forEach(event => {
      stats.eventTypes[event.eventType] = 
        (stats.eventTypes[event.eventType] || 0) + 1;
    });
    
    this.emit('real_time_stats', stats);
  }

  // 更新推薦系統
  updateRecommendations(events) {
    const userLearningEvents = events.filter(e => 
      e.eventType === 'learning_progress' || 
      e.eventType === 'course_enrollment' ||
      e.eventType === 'scripture_view'
    );
    
    userLearningEvents.forEach(event => {
      this.emit('update_recommendations', {
        userId: event.data.userId,
        activity: event
      });
    });
  }

  // 清理過期會話
  cleanupExpiredSessions() {
    const now = new Date();
    const expiredSessions = [];
    
    for (const [userId, session] of this.activeUserSessions) {
      const timeSinceLastActivity = now - session.lastActivity;
      
      // 30分鐘無活動視為過期
      if (timeSinceLastActivity > 30 * 60 * 1000) {
        expiredSessions.push(userId);
      }
    }
    
    expiredSessions.forEach(userId => {
      this.trackUserLogout(userId);
    });
  }
}

// ========== 效能監控服務 ==========

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      requestCounts: new Map(),
      responseTimes: new Map(),
      errorRates: new Map(),
      systemLoad: {
        cpu: 0,
        memory: 0,
        diskIO: 0
      }
    };
    
    this.startSystemMonitoring();
  }

  // 開始系統監控
  startSystemMonitoring() {
    setInterval(() => {
      this.collectSystemMetrics();
    }, 10000); // 每10秒收集一次

    setInterval(() => {
      this.analyzePerformance();
    }, 60000); // 每分鐘分析一次
  }

  // 收集系統指標
  collectSystemMetrics() {
    const usage = process.cpuUsage();
    const memUsage = process.memoryUsage();
    
    this.metrics.systemLoad = {
      cpu: (usage.user + usage.system) / 1000000, // 轉換為秒
      memory: memUsage.rss / 1024 / 1024, // 轉換為 MB
      heapUsed: memUsage.heapUsed / 1024 / 1024
    };
    
    // 更新資料庫連接數
    const mongoose = require('mongoose');
    if (mongoose.connection.readyState === 1) {
      databaseConnections.set(mongoose.connection.connections?.length || 1);
    }
  }

  // 記錄請求指標
  recordRequest(endpoint, method, responseTime, statusCode) {
    const key = `${method}:${endpoint}`;
    
    // 記錄請求數量
    if (!this.metrics.requestCounts.has(key)) {
      this.metrics.requestCounts.set(key, 0);
    }
    this.metrics.requestCounts.set(key, this.metrics.requestCounts.get(key) + 1);
    
    // 記錄回應時間
    if (!this.metrics.responseTimes.has(key)) {
      this.metrics.responseTimes.set(key, []);
    }
    this.metrics.responseTimes.get(key).push(responseTime);
    
    // 記錄錯誤率
    if (statusCode >= 400) {
      if (!this.metrics.errorRates.has(key)) {
        this.metrics.errorRates.set(key, 0);
      }
      this.metrics.errorRates.set(key, this.metrics.errorRates.get(key) + 1);
    }
  }

  // 分析效能
  analyzePerformance() {
    const alerts = [];
    
    // 檢查回應時間
    for (const [endpoint, times] of this.metrics.responseTimes) {
      if (times.length > 0) {
        const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
        const p95Time = this.calculatePercentile(times, 95);
        
        if (avgTime > 2000) { // 2秒
          alerts.push({
            type: 'SLOW_RESPONSE',
            endpoint,
            avgTime,
            p95Time,
            severity: 'HIGH'
          });
        }
      }
    }
    
    // 檢查錯誤率
    for (const [endpoint, errorCount] of this.metrics.errorRates) {
      const totalRequests = this.metrics.requestCounts.get(endpoint) || 1;
      const errorRate = (errorCount / totalRequests) * 100;
      
      if (errorRate > 5) { // 5%
        alerts.push({
          type: 'HIGH_ERROR_RATE',
          endpoint,
          errorRate,
          errorCount,
          totalRequests,
          severity: 'CRITICAL'
        });
      }
    }
    
    // 檢查系統負載
    if (this.metrics.systemLoad.memory > 1024) { // 1GB
      alerts.push({
        type: 'HIGH_MEMORY_USAGE',
        memoryUsage: this.metrics.systemLoad.memory,
        severity: 'HIGH'
      });
    }
    
    if (alerts.length > 0) {
      this.sendAlerts(alerts);
    }
    
    // 清理舊數據
    this.cleanupOldMetrics();
  }

  // 計算百分位數
  calculatePercentile(values, percentile) {
    const sorted = values.slice().sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[index];
  }

  // 發送警報
  sendAlerts(alerts) {
    console.warn('Performance Alerts:', alerts);
    
    // 這裡可以整合 Slack、Email 等通知系統
    alerts.forEach(alert => {
      if (alert.severity === 'CRITICAL') {
        this.sendCriticalAlert(alert);
      }
    });
  }

  // 發送緊急警報
  sendCriticalAlert(alert) {
    // 實作緊急警報邏輯
    console.error('CRITICAL ALERT:', alert);
  }

  // 清理舊指標
  cleanupOldMetrics() {
    // 只保留最近1000個數據點
    for (const [key, times] of this.metrics.responseTimes) {
      if (times.length > 1000) {
        this.metrics.responseTimes.set(key, times.slice(-1000));
      }
    }
  }

  // 獲取效能報告
  getPerformanceReport() {
    const report = {
      timestamp: new Date(),
      systemLoad: this.metrics.systemLoad,
      endpoints: {}
    };
    
    for (const [endpoint, times] of this.metrics.responseTimes) {
      if (times.length > 0) {
        const requestCount = this.metrics.requestCounts.get(endpoint) || 0;
        const errorCount = this.metrics.errorRates.get(endpoint) || 0;
        
        report.endpoints[endpoint] = {
          requestCount,
          errorCount,
          errorRate: requestCount > 0 ? (errorCount / requestCount) * 100 : 0,
          avgResponseTime: times.reduce((sum, time) => sum + time, 0) / times.length,
          p50ResponseTime: this.calculatePercentile(times, 50),
          p95ResponseTime: this.calculatePercentile(times, 95),
          p99ResponseTime: this.calculatePercentile(times, 99)
        };
      }
    }
    
    return report;
  }
}

// ========== 健康檢查服務 ==========

class HealthCheckService {
  constructor() {
    this.checks = new Map();
    this.registerDefaultChecks();
  }

  // 註冊預設檢查
  registerDefaultChecks() {
    this.register('database', this.checkDatabase);
    this.register('redis', this.checkRedis);
    this.register('elasticsearch', this.checkElasticsearch);
    this.register('memory', this.checkMemory);
    this.register('disk', this.checkDisk);
  }

  // 註冊健康檢查
  register(name, checkFunction) {
    this.checks.set(name, checkFunction);
  }

  // 執行所有檢查
  async runAllChecks() {
    const results = {};
    const promises = [];

    for (const [name, checkFn] of this.checks) {
      promises.push(
        this.runSingleCheck(name, checkFn)
          .then(result => ({ name, result }))
          .catch(error => ({ name, result: { status: 'ERROR', error: error.message } }))
      );
    }

    const checkResults = await Promise.all(promises);
    checkResults.forEach(({ name, result }) => {
      results[name] = result;
    });

    const overallStatus = Object.values(results).every(r => r.status === 'OK') ? 'OK' : 'ERROR';

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      checks: results,
      uptime: process.uptime(),
      version: process.env.APP_VERSION || '1.0.0'
    };
  }

  // 執行單一檢查
  async runSingleCheck(name, checkFn, timeout = 5000) {
    return new Promise(async (resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Health check timeout: ${name}`));
      }, timeout);

      try {
        const result = await checkFn();
        clearTimeout(timer);
        resolve(result);
      } catch (error) {
        clearTimeout(timer);
        reject(error);
      }
    });
  }

  // 資料庫檢查
  async checkDatabase() {
    const mongoose = require('mongoose');
    
    if (mongoose.connection.readyState !== 1) {
      return { status: 'ERROR', message: 'Database not connected' };
    }

    try {
      // 簡單的 ping 測試
      await mongoose.connection.db.admin().ping();
      
      const stats = await mongoose.connection.db.stats();
      return {
        status: 'OK',
        responseTime: Date.now(),
        details: {
          collections: stats.collections,
          dataSize: stats.dataSize,
          storageSize: stats.storageSize
        }
      };
    } catch (error) {
      return {
        status: 'ERROR',
        message: error.message
      };
    }
  }

  // Redis 檢查
  async checkRedis() {
    const redis = require('redis');
    
    try {
      const client = redis.createClient({
        url: process.env.REDIS_URL || 'redis://localhost:6379'
      });
      
      await client.connect();
      const start = Date.now();
      await client.ping();
      const responseTime = Date.now() - start;
      await client.quit();
      
      return {
        status: 'OK',
        responseTime,
        message: 'Redis connection successful'
      };
    } catch (error) {
      return {
        status: 'ERROR',
        message: error.message
      };
    }
  }

  // Elasticsearch 檢查
  async checkElasticsearch() {
    try {
      const { Client } = require('@elastic/elasticsearch');
      const client = new Client({
        node: process.env.ELASTICSEARCH_URL || 'http://localhost:9200'
      });
      
      const start = Date.now();
      const health = await client.cluster.health();
      const responseTime = Date.now() - start;
      
      return {
        status: health.body.status === 'red' ? 'ERROR' : 'OK',
        responseTime,
        details: {
          clusterStatus: health.body.status,
          numberOfNodes: health.body.number_of_nodes,
          activeShards: health.body.active_shards
        }
      };
    } catch (error) {
      return {
        status: 'ERROR',
        message: error.message
      };
    }
  }

  // 記憶體檢查
  async checkMemory() {
    const usage = process.memoryUsage();
    const totalMemory = usage.rss + usage.heapUsed + usage.external;
    const maxMemory = 1024 * 1024 * 1024; // 1GB 限制
    
    const status = totalMemory > maxMemory ? 'WARNING' : 'OK';
    
    return {
      status,
      details: {
        rss: Math.round(usage.rss / 1024 / 1024),
        heapUsed: Math.round(usage.heapUsed / 1024 / 1024),
        heapTotal: Math.round(usage.heapTotal / 1024 / 1024),
        external: Math.round(usage.external / 1024 / 1024),
        unit: 'MB'
      }
    };
  }

  // 磁碟檢查
  async checkDisk() {
    const fs = require('fs').promises;
    
    try {
      const stats = await fs.stat('./');
      // 簡單的磁碟檢查
      return {
        status: 'OK',
        message: 'Disk accessible'
      };
    } catch (error) {
      return {
        status: 'ERROR',
        message: 'Disk not accessible'
      };
    }
  }
}

// ========== 日誌服務 ==========

class LoggingService {
  constructor() {
    this.logLevels = {
      ERROR: 0,
      WARN: 1,
      INFO: 2,
      DEBUG: 3
    };
    
    this.currentLevel = this.logLevels[process.env.LOG_LEVEL || 'INFO'];
  }

  // 結構化日誌
  log(level, message, meta = {}) {
    if (this.logLevels[level] > this.currentLevel) {
      return;
    }

    const logEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      meta,
      pid: process.pid,
      hostname: require('os').hostname()
    };

    // 輸出到控制台
    console.log(JSON.stringify(logEntry));

    // 發送到外部日誌系統（如 ELK Stack）
    this.sendToExternalLogger(logEntry);
  }

  error(message, meta = {}) {
    this.log('ERROR', message, meta);
  }

  warn(message, meta = {}) {
    this.log('WARN', message, meta);
  }

  info(message, meta = {}) {
    this.log('INFO', message, meta);
  }

  debug(message, meta = {}) {
    this.log('DEBUG', message, meta);
  }

  // 發送到外部日誌系統
  sendToExternalLogger(logEntry) {
    // 這裡可以整合 Logstash、Fluentd 等
    // 暫時省略實作
  }

  // 記錄 API 請求
  logApiRequest(req, res, duration) {
    this.info('API Request', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: req.user?.id
    });
  }

  // 記錄錯誤
  logError(error, context = {}) {
    this.error('Application Error', {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      context
    });
  }
}

// ========== 服務實例化 ==========

const analyticsService = new AnalyticsService();
const performanceMonitor = new PerformanceMonitor();
const healthCheckService = new HealthCheckService();
const loggingService = new LoggingService();

// ========== Express 整合 ==========

const setupMonitoring = (app) => {
  // 監控中間件
  app.use(metricsMiddleware);

  // 效能監控中間件
  app.use((req, res, next) => {
    const start = Date.now();
    
    res.on('finish', () => {
      const duration = Date.now() - start;
      performanceMonitor.recordRequest(
        req.route?.path || req.path,
        req.method,
        duration,
        res.statusCode
      );
      
      loggingService.logApiRequest(req, res, duration);
    });
    
    next();
  });

  // 分析追蹤中間件
  app.use((req, res, next) => {
    if (req.user) {
      analyticsService.trackPageView(
        req.user.id,
        req.path,
        req.get('Referer')
      );
    }
    next();
  });

  // Prometheus 指標端點
  app.get('/metrics', async (req, res) => {
    try {
      res.set('Content-Type', register.contentType);
      res.end(await register.metrics());
    } catch (ex) {
      res.status(500).end(ex);
    }
  });

  // 健康檢查端點
  app.get('/health', async (req, res) => {
    try {
      const health = await healthCheckService.runAllChecks();
      const statusCode = health.status === 'OK' ? 200 : 503;
      res.status(statusCode).json(health);
    } catch (error) {
      res.status(500).json({
        status: 'ERROR',
        message: 'Health check failed',
        error: error.message
      });
    }
  });

  // 效能報告端點
  app.get('/performance', (req, res) => {
    const report = performanceMonitor.getPerformanceReport();
    res.json(report);
  });

  // 分析數據端點
  app.get('/analytics/realtime', (req, res) => {
    const stats = {
      activeUsers: analyticsService.activeUserSessions.size,
      sessions: Array.from(analyticsService.activeUserSessions.values())
        .map(session => ({
          userId: session.userId,
          loginTime: session.loginTime,
          lastActivity: session.lastActivity,
          pageViews: session.pageViews.length,
          actions: session.actions.length
        }))
    };
    
    res.json(stats);
  });

  // 錯誤處理中間件
  app.use((error, req, res, next) => {
    loggingService.logError(error, {
      url: req.url,
      method: req.method,
      userId: req.user?.id,
      userAgent: req.get('User-Agent')
    });
    
    res.status(500).json({
      error: 'Internal server error',
      requestId: req.id
    });
  });
};

// ========== 定期任務 ==========

// 清理過期會話
setInterval(() => {
  analyticsService.cleanupExpiredSessions();
}, 5 * 60 * 1000); // 每5分鐘

// 生成分析報告
setInterval(async () => {
  try {
    const report = await generateDailyReport();
    loggingService.info('Daily report generated', { report });
  } catch (error) {
    loggingService.error('Failed to generate daily report', { error });
  }
}, 24 * 60 * 60 * 1000); // 每24小時

// 生成每日報告
async function generateDailyReport() {
  const today = new Date();
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
  
  const AnalyticsEvent = require('../models/AnalyticsEvent');
  
  const [
    pageViews,
    userLogins,
    courseEnrollments,
    scriptureViews
  ] = await Promise.all([
    AnalyticsEvent.countDocuments({
      type: 'page_view',
      timestamp: { $gte: yesterday, $lt: today }
    }),
    AnalyticsEvent.countDocuments({
      type: 'user_login',
      timestamp: { $gte: yesterday, $lt: today }
    }),
    AnalyticsEvent.countDocuments({
      type: 'user_action',
      'data.action': 'course_enrollment',
      timestamp: { $gte: yesterday, $lt: today }
    }),
    AnalyticsEvent.countDocuments({
      type: 'user_action',
      'data.action': 'scripture_view',
      timestamp: { $gte: yesterday, $lt: today }
    })
  ]);
  
  return {
    date: yesterday.toISOString().split('T')[0],
    pageViews,
    userLogins,
    courseEnrollments,
    scriptureViews,
    performance: performanceMonitor.getPerformanceReport()
  };
}

// ========== 匯出 ==========

module.exports = {
  analyticsService,
  performanceMonitor,
  healthCheckService,
  loggingService,
  setupMonitoring,
  generateDailyReport
};