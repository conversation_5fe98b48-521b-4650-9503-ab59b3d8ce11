<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文章詳情 - 澄源閱讀</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans TC', sans-serif;
            line-height: 1.8;
            color: #333;
            background: #fafbfc;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 文章頭部 */
        .article-header {
            background: white;
            border-radius: 15px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .article-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .article-category {
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            margin-bottom: 20px;
        }

        .article-title {
            font-size: 2.2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 20px;
            line-height: 1.3;
        }

        .article-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: center;
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 25px;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .article-author {
            font-weight: 500;
            color: #333;
        }

        .article-excerpt {
            font-size: 1.1rem;
            color: #555;
            line-height: 1.6;
            font-style: italic;
            border-left: 4px solid #667eea;
            padding-left: 20px;
            margin-bottom: 25px;
        }

        .article-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .article-tag {
            background: #f0f0f0;
            color: #666;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
        }

        /* 文章內容 */
        .article-content {
            background: white;
            border-radius: 15px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .article-content h2 {
            font-size: 1.6rem;
            font-weight: 600;
            color: #333;
            margin: 30px 0 15px 0;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }

        .article-content h3 {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin: 25px 0 12px 0;
        }

        .article-content p {
            margin-bottom: 20px;
            line-height: 1.8;
            color: #444;
        }

        .article-content blockquote {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 25px 0;
            font-style: italic;
            border-radius: 0 8px 8px 0;
        }

        .article-content ul, .article-content ol {
            margin: 20px 0;
            padding-left: 30px;
        }

        .article-content li {
            margin-bottom: 8px;
        }

        .highlight-box {
            background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
            border: 1px solid #b3d9ff;
            border-radius: 10px;
            padding: 20px;
            margin: 25px 0;
            position: relative;
        }

        .highlight-box::before {
            content: '\f0eb';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            top: 15px;
            right: 15px;
            color: #667eea;
            font-size: 1.2rem;
        }

        .practice-exercise {
            background: linear-gradient(135deg, #f0fff4 0%, #e6ffe6 100%);
            border: 1px solid #b3ffb3;
            border-radius: 10px;
            padding: 20px;
            margin: 25px 0;
        }

        .practice-title {
            font-weight: 600;
            color: #2e7d32;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 文章操作欄 */
        .article-actions {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            background: white;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .action-btn:hover {
            border-color: #667eea;
            color: #667eea;
            transform: translateY(-2px);
        }

        .action-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .share-options {
            display: flex;
            gap: 10px;
        }

        .share-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .share-btn.facebook { background: #1877f2; }
        .share-btn.twitter { background: #1da1f2; }
        .share-btn.line { background: #00c300; }
        .share-btn.copy { background: #666; }

        .share-btn:hover {
            transform: scale(1.1);
        }

        /* 相關文章 */
        .related-articles {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .related-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .related-card {
            border: 1px solid #f0f0f0;
            border-radius: 10px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .related-card:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .related-title {
            font-size: 1rem;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .related-excerpt {
            font-size: 0.8rem;
            color: #666;
            line-height: 1.5;
            margin-bottom: 10px;
        }

        .related-meta {
            font-size: 0.7rem;
            color: #999;
            display: flex;
            justify-content: space-between;
        }

        /* 評論區域 */
        .comments-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .comment-form {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .comment-textarea {
            width: 100%;
            min-height: 100px;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-family: inherit;
            font-size: 0.9rem;
            resize: vertical;
            margin-bottom: 15px;
        }

        .comment-textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .comment-submit {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .comment-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .comments-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .comment-item {
            padding: 20px;
            border: 1px solid #f0f0f0;
            border-radius: 10px;
        }

        .comment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .comment-author {
            font-weight: 500;
            color: #333;
        }

        .comment-date {
            font-size: 0.8rem;
            color: #999;
        }

        .comment-content {
            color: #555;
            line-height: 1.6;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .article-header,
            .article-content,
            .related-articles,
            .comments-section {
                padding: 25px;
            }
            
            .article-title {
                font-size: 1.8rem;
            }
            
            .article-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .article-actions {
                flex-direction: column;
                align-items: stretch;
            }
            
            .action-buttons {
                justify-content: center;
            }
            
            .related-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .article-header,
            .article-content,
            .related-articles,
            .comments-section {
                padding: 20px;
            }
            
            .article-title {
                font-size: 1.5rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }

        /* 閱讀進度條 */
        .reading-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(102, 126, 234, 0.2);
            z-index: 1000;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        /* 返回頂部按鈕 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
        }

        .back-to-top.visible {
            opacity: 1;
            visibility: visible;
        }

        .back-to-top:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <!-- 閱讀進度條 -->
    <div class="reading-progress">
        <div class="progress-bar" id="progress-bar"></div>
    </div>

    <div class="container">
        <!-- 文章頭部 -->
        <header class="article-header">
            <div class="article-category" id="article-category">佛學智慧</div>
            <h1 class="article-title" id="article-title">藏傳佛教中的慈悲修持法門</h1>
            <div class="article-meta">
                <div class="meta-item">
                    <i class="fas fa-user"></i>
                    <span class="article-author" id="article-author">釋智慧</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-calendar"></i>
                    <span id="article-date">2025-01-10</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-clock"></i>
                    <span id="read-time">約 8 分鐘</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-eye"></i>
                    <span id="view-count">1,245 次瀏覽</span>
                </div>
            </div>
            <div class="article-excerpt" id="article-excerpt">
                慈悲心是藏傳佛教修行的核心，也是成就菩薩道的根本。本文將深入探討如何在日常生活中培養真正的慈悲心，包括四無量心的修持方法、慈悲禪修的具體步驟，以及如何將慈悲心運用到人際關係中。
            </div>
            <div class="article-tags" id="article-tags">
                <span class="article-tag">慈悲修持</span>
                <span class="article-tag">四無量心</span>
                <span class="article-tag">禪修</span>
                <span class="article-tag">藏傳佛教</span>
            </div>
        </header>

        <!-- 文章內容 -->
        <main class="article-content" id="article-content">
            <h2>慈悲心的深層意義</h2>
            <p>在藏傳佛教的傳統中，慈悲心不僅僅是一種情感，更是一種深刻的智慧體現。真正的慈悲心源於對眾生苦難的深切理解，以及幫助他們解脫痛苦的強烈願望。這種慈悲心與世俗的同情心不同，它不帶有任何條件或期待，是一種無私無我的愛。</p>

            <div class="highlight-box">
                <strong>重要提醒：</strong>慈悲心的培養需要循序漸進，從對親近的人開始，逐漸擴展到中性的人，最終包含所有眾生，甚至是曾經傷害過我們的人。
            </div>

            <h2>四無量心的修持方法</h2>
            <p>四無量心包括慈心、悲心、喜心和捨心，這是培養慈悲心的經典方法：</p>

            <h3>1. 慈心 (Metta)</h3>
            <p>慈心是希望所有眾生都能獲得快樂和快樂的因。修持慈心時，我們首先對自己生起慈愛之心，然後依次將這種愛擴展到親人、朋友、中性的人，最後包含敵人。</p>

            <div class="practice-exercise">
                <div class="practice-title">
                    <i class="fas fa-leaf"></i>
                    實修練習：慈心冥想
                </div>
                <ol>
                    <li>找一個安靜的地方，採取舒適的坐姿</li>
                    <li>首先對自己默念：「願我快樂，願我平安，願我遠離痛苦」</li>
                    <li>將這個祝願擴展到親近的人</li>
                    <li>逐漸包含更多的人，直到所有眾生</li>
                    <li>每次練習15-30分鐘</li>
                </ol>
            </div>

            <h3>2. 悲心 (Compassion)</h3>
            <p>悲心是希望眾生遠離痛苦和痛苦的因。這不是消極的同情，而是積極的行動意願。當我們看到他人的痛苦時，悲心促使我們想要幫助他們。</p>

            <h3>3. 喜心 (Sympathetic Joy)</h3>
            <p>喜心是為他人的快樂和成功感到由衷的歡喜。這種心態能夠消除嫉妒和競爭心理，讓我們在他人的幸福中找到自己的快樂。</p>

            <h3>4. 捨心 (Equanimity)</h3>
            <p>捨心是對所有眾生保持平等無別的心態，不會因為個人的喜好而有所偏頗。這是一種超越愛憎的平靜狀態。</p>

            <blockquote>
                「慈悲心是心靈的甘露，能夠滋潤乾涸的心田，讓智慧之花綻放。當我們真正理解眾生一體的道理時，慈悲心自然會在心中升起。」 — 達賴喇嘛
            </blockquote>

            <h2>日常生活中的慈悲實踐</h2>
            <p>慈悲心的培養不僅在禪修座上，更重要的是在日常生活中的實踐：</p>

            <ul>
                <li><strong>善語善行：</strong>用溫和的語言與人交流，避免傷害他人的言行</li>
                <li><strong>耐心包容：</strong>對他人的過錯保持理解和寬容</li>
                <li><strong>助人為樂：</strong>在能力範圍內幫助需要幫助的人</li>
                <li><strong>感恩惜福：</strong>珍惜現有的一切，對生活保持感恩之心</li>
            </ul>

            <h2>克服修持中的障礙</h2>
            <p>在慈悲心的修持過程中，我們可能會遇到一些障礙：</p>

            <h3>憤怒和怨恨</h3>
            <p>當我們對某些人懷有怨恨時，很難對他們生起慈悲心。這時候可以運用「自他相換」的修持方法，設身處地為對方著想，理解他們行為背後的痛苦。</p>

            <h3>冷漠和麻木</h3>
            <p>有時我們可能對他人的痛苦感到麻木。這時需要重新喚醒我們內在的敏感性，通過觀修眾生皆有佛性來培養慈悲心。</p>

            <h3>執著和偏愛</h3>
            <p>我們常常對親近的人偏愛有加，而忽略其他眾生。真正的慈悲心應該是平等無別的，需要逐漸擴大我們的慈悲範圍。</p>

            <div class="highlight-box">
                <strong>智慧啟示：</strong>慈悲心的最高境界是「無緣大慈，同體大悲」，即不需要任何條件或理由，自然流露出對所有眾生的慈愛。
            </div>

            <h2>結語</h2>
            <p>慈悲心的培養是一個終生的修持過程。它不僅能夠淨化我們的心靈，還能為周圍的人帶來溫暖和光明。當我們真正體會到慈悲心的力量時，就會發現這是通往內在平靜和究竟解脫的必經之路。</p>

            <p>願所有眾生都能培養起真正的慈悲心，在慈悲中找到生命的意義，在愛中實現心靈的覺醒。</p>
        </main>

        <!-- 文章操作欄 -->
        <section class="article-actions">
            <div class="action-buttons">
                <button class="action-btn" id="like-btn">
                    <i class="fas fa-heart"></i>
                    <span>收藏 (89)</span>
                </button>
                <button class="action-btn" id="bookmark-btn">
                    <i class="fas fa-bookmark"></i>
                    <span>書籤</span>
                </button>
                <button class="action-btn" id="print-btn">
                    <i class="fas fa-print"></i>
                    <span>列印</span>
                </button>
            </div>
            <div class="share-options">
                <button class="share-btn facebook" title="分享到 Facebook">
                    <i class="fab fa-facebook-f"></i>
                </button>
                <button class="share-btn twitter" title="分享到 Twitter">
                    <i class="fab fa-twitter"></i>
                </button>
                <button class="share-btn line" title="分享到 LINE">
                    <i class="fab fa-line"></i>
                </button>
                <button class="share-btn copy" title="複製連結">
                    <i class="fas fa-link"></i>
                </button>
            </div>
        </section>

        <!-- 相關文章 -->
        <section class="related-articles">
            <h2 class="section-title">
                <i class="fas fa-lightbulb"></i>
                相關閱讀
            </h2>
            <div class="related-grid" id="related-grid">
                <!-- 相關文章將通過 JavaScript 動態載入 -->
            </div>
        </section>

        <!-- 評論區域 -->
        <section class="comments-section">
            <h2 class="section-title">
                <i class="fas fa-comments"></i>
                讀者分享 (5)
            </h2>
            
            <div class="comment-form">
                <textarea class="comment-textarea" placeholder="分享您的心得體會..."></textarea>
                <button class="comment-submit">發表分享</button>
            </div>

            <div class="comments-list" id="comments-list">
                <!-- 評論將通過 JavaScript 動態載入 -->
            </div>
        </section>
    </div>

    <!-- 返回頂部按鈕 -->
    <button class="back-to-top" id="back-to-top">
        <i class="fas fa-arrow-up"></i>
    </button>

    <script>
        // 模擬相關文章數據
        const relatedArticles = [
            {
                title: "菩提心的培養與實修",
                excerpt: "菩提心是成佛的種子，學習如何在日常生活中培養這顆珍貴的心...",
                category: "修行方法",
                date: "2025-01-05"
            },
            {
                title: "正念冥想的科學驗證",
                excerpt: "現代科學研究證實了正念冥想對大腦和身心健康的積極影響...",
                category: "科學研究",
                date: "2025-01-03"
            },
            {
                title: "藏傳佛教的修行次第",
                excerpt: "了解藏傳佛教從初學到成就的完整修行體系和階段性目標...",
                category: "佛學智慧",
                date: "2024-12-28"
            }
        ];

        // 模擬評論數據
        const comments = [
            {
                author: "覺心",
                date: "2025-01-12",
                content: "這篇文章寫得很深入，特別是關於四無量心的實修方法。我按照文章中的指導練習了一週，確實感受到內心的平靜和慈愛。感謝分享！"
            },
            {
                author: "智明",
                date: "2025-01-11",
                content: "慈悲心的培養確實需要長期的練習。我發現最大的挑戰是對曾經傷害過自己的人生起慈悲心，但透過不斷的練習，漸漸能夠理解他們的痛苦。"
            },
            {
                author: "蓮花",
                date: "2025-01-10",
                content: "文中提到的自他相換修持法很有啟發性。當我站在對方的角度思考問題時，很多怨恨和不理解都消失了，取而代之的是深深的慈悲。"
            }
        ];

        // 渲染相關文章
        function renderRelatedArticles() {
            const grid = document.getElementById('related-grid');
            grid.innerHTML = relatedArticles.map(article => `
                <div class="related-card">
                    <h3 class="related-title">${article.title}</h3>
                    <p class="related-excerpt">${article.excerpt}</p>
                    <div class="related-meta">
                        <span>${article.category}</span>
                        <span>${article.date}</span>
                    </div>
                </div>
            `).join('');
        }

        // 渲染評論
        function renderComments() {
            const list = document.getElementById('comments-list');
            list.innerHTML = comments.map(comment => `
                <div class="comment-item">
                    <div class="comment-header">
                        <span class="comment-author">${comment.author}</span>
                        <span class="comment-date">${comment.date}</span>
                    </div>
                    <div class="comment-content">${comment.content}</div>
                </div>
            `).join('');
        }

        // 閱讀進度條
        function updateReadingProgress() {
            const article = document.getElementById('article-content');
            const scrollTop = window.pageYOffset;
            const articleTop = article.offsetTop;
            const articleHeight = article.offsetHeight;
            const windowHeight = window.innerHeight;
            
            const progress = Math.max(0, Math.min(100, 
                ((scrollTop - articleTop + windowHeight) / articleHeight) * 100
            ));
            
            document.getElementById('progress-bar').style.width = progress + '%';
        }

        // 返回頂部按鈕
        function toggleBackToTop() {
            const btn = document.getElementById('back-to-top');
            if (window.pageYOffset > 300) {
                btn.classList.add('visible');
            } else {
                btn.classList.remove('visible');
            }
        }

        // 文章操作
        document.getElementById('like-btn').addEventListener('click', function() {
            this.classList.toggle('active');
            const span = this.querySelector('span');
            const count = parseInt(span.textContent.match(/\d+/)[0]);
            
            if (this.classList.contains('active')) {
                span.textContent = `收藏 (${count + 1})`;
                showNotification('已加入收藏');
            } else {
                span.textContent = `收藏 (${count - 1})`;
                showNotification('已取消收藏');
            }
        });

        document.getElementById('bookmark-btn').addEventListener('click', function() {
            this.classList.toggle('active');
            if (this.classList.contains('active')) {
                showNotification('已加入書籤');
            } else {
                showNotification('已取消書籤');
            }
        });

        document.getElementById('print-btn').addEventListener('click', function() {
            window.print();
        });

        // 分享功能
        document.querySelectorAll('.share-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const platform = this.className.split(' ')[1];
                const title = document.getElementById('article-title').textContent;
                const url = window.location.href;
                
                switch(platform) {
                    case 'facebook':
                        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`);
                        break;
                    case 'twitter':
                        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=${encodeURIComponent(url)}`);
                        break;
                    case 'line':
                        window.open(`https://social-plugins.line.me/lineit/share?url=${encodeURIComponent(url)}`);
                        break;
                    case 'copy':
                        navigator.clipboard.writeText(url).then(() => {
                            showNotification('連結已複製到剪貼板');
                        });
                        break;
                }
            });
        });

        // 評論提交
        document.querySelector('.comment-submit').addEventListener('click', function() {
            const textarea = document.querySelector('.comment-textarea');
            const content = textarea.value.trim();
            
            if (content) {
                const newComment = {
                    author: "訪客",
                    date: new Date().toISOString().split('T')[0],
                    content: content
                };
                
                comments.unshift(newComment);
                renderComments();
                textarea.value = '';
                showNotification('分享已發表，感謝您的參與！');
            } else {
                showNotification('請輸入您的心得分享');
            }
        });

        // 返回頂部
        document.getElementById('back-to-top').addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // 相關文章點擊
        document.addEventListener('click', function(e) {
            const relatedCard = e.target.closest('.related-card');
            if (relatedCard) {
                showNotification('正在載入相關文章...');
                // 可以跳轉到其他文章
            }
        });

        // 通知函數
        function showNotification(message) {
            if (window.parent !== window) {
                window.parent.postMessage({
                    type: 'notification',
                    message: message,
                    level: 'info'
                }, '*');
            }
        }

        // 滾動事件
        window.addEventListener('scroll', function() {
            updateReadingProgress();
            toggleBackToTop();
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderRelatedArticles();
            renderComments();
            
            // 從 URL 參數獲取文章信息
            const urlParams = new URLSearchParams(window.location.search);
            const articleId = urlParams.get('id');
            const category = urlParams.get('category');
            
            // 根據 articleId 載入對應內容（這裡使用模擬數據）
            if (articleId) {
                loadArticleContent(articleId, category);
            }
        });

        // 載入文章內容
        function loadArticleContent(articleId, category) {
            // 根據文章ID和分類載入不同內容
            // 這裡可以根據實際需要調整文章內容
        }

        // 鍵盤快捷鍵
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 's':
                        e.preventDefault();
                        document.getElementById('bookmark-btn').click();
                        break;
                    case 'p':
                        e.preventDefault();
                        document.getElementById('print-btn').click();
                        break;
                }
            }
        });

        // 文章閱讀時間估算
        function estimateReadingTime() {
            const content = document.getElementById('article-content').textContent;
            const wordsPerMinute = 250; // 平均閱讀速度
            const words = content.length / 2; // 中文字符估算
            const minutes = Math.ceil(words / wordsPerMinute);
            
            document.getElementById('read-time').textContent = `約 ${minutes} 分鐘`;
        }

        // 初始化閱讀時間
        estimateReadingTime();
    </script>
</body>
</html>