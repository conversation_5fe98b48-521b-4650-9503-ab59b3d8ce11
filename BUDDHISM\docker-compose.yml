version: '3.8'

services:
  # ================================
  # 🚀 主應用服務
  # ================================
  app:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: buddhist-healing-app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/buddhist_healing
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
      - SMTP_FROM=${SMTP_FROM}
    depends_on:
      - mongo
      - redis
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    networks:
      - buddhist-healing-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ================================
  # 🗄️ MongoDB 資料庫
  # ================================
  mongo:
    image: mongo:5.0
    container_name: buddhist-healing-mongo
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-password}
      MONGO_INITDB_DATABASE: buddhist_healing
    volumes:
      - mongo_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    ports:
      - "27017:27017"
    networks:
      - buddhist-healing-network
    command: mongod --auth

  # ================================
  # 🔄 Redis 緩存
  # ================================
  redis:
    image: redis:6.2-alpine
    container_name: buddhist-healing-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    ports:
      - "6379:6379"
    networks:
      - buddhist-healing-network
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ================================
  # 🌐 Nginx 反向代理
  # ================================
  nginx:
    image: nginx:alpine
    container_name: buddhist-healing-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./static:/var/www/static:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - buddhist-healing-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ================================
  # 📊 MongoDB 管理界面 (可選)
  # ================================
  mongo-express:
    image: mongo-express:latest
    container_name: buddhist-healing-mongo-express
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_SERVER: mongo
      ME_CONFIG_MONGODB_PORT: 27017
      ME_CONFIG_MONGODB_ADMINUSERNAME: ${MONGO_ROOT_USERNAME:-admin}
      ME_CONFIG_MONGODB_ADMINPASSWORD: ${MONGO_ROOT_PASSWORD:-password}
      ME_CONFIG_BASICAUTH_USERNAME: ${MONGO_EXPRESS_USER:-admin}
      ME_CONFIG_BASICAUTH_PASSWORD: ${MONGO_EXPRESS_PASS:-password}
    depends_on:
      - mongo
    restart: unless-stopped
    networks:
      - buddhist-healing-network
    profiles:
      - management

  # ================================
  # 📈 Redis 管理界面 (可選)
  # ================================
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: buddhist-healing-redis-commander
    ports:
      - "8082:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
      - HTTP_USER=${REDIS_COMMANDER_USER:-admin}
      - HTTP_PASSWORD=${REDIS_COMMANDER_PASS:-password}
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - buddhist-healing-network
    profiles:
      - management

  # ================================
  # 📊 監控系統 (Prometheus)
  # ================================
  prometheus:
    image: prom/prometheus:latest
    container_name: buddhist-healing-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - buddhist-healing-network
    profiles:
      - monitoring

  # ================================
  # 📊 Grafana 儀表板
  # ================================
  grafana:
    image: grafana/grafana:latest
    container_name: buddhist-healing-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/:/etc/grafana/provisioning/
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - buddhist-healing-network
    profiles:
      - monitoring

  # ================================
  # 📧 郵件服務 (Mailhog - 開發用)
  # ================================
  mailhog:
    image: mailhog/mailhog:latest
    container_name: buddhist-healing-mailhog
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    restart: unless-stopped
    networks:
      - buddhist-healing-network
    profiles:
      - development

  # ================================
  # 🧪 測試資料庫 (開發用)
  # ================================
  mongo-test:
    image: mongo:5.0
    container_name: buddhist-healing-mongo-test
    environment:
      MONGO_INITDB_DATABASE: buddhist_healing_test
    ports:
      - "27018:27017"
    networks:
      - buddhist-healing-network
    profiles:
      - testing
    command: mongod --noauth

# ================================
# 🗄️ 數據卷
# ================================
volumes:
  mongo_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  nginx_logs:
    driver: local

# ================================
  # 🌐 網路配置
# ================================
networks:
  buddhist-healing-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16