const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const rateLimit = require('express-rate-limit');
const Joi = require('joi');
const User = require('../models/User');
const auth = require('../middleware/auth');
const sendEmail = require('../utils/sendEmail');

const router = express.Router();

// 驗證 schemas
const registerSchema = Joi.object({
    email: Joi.string().email().required().messages({
        'string.email': '請提供有效的電子郵件格式',
        'any.required': '電子郵件為必填項目'
    }),
    password: Joi.string()
        .min(8)
        .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .required()
        .messages({
            'string.min': '密碼至少需要8個字符',
            'string.pattern.base': '密碼必須包含大小寫字母和數字',
            'any.required': '密碼為必填項目'
        }),
    firstName: Joi.string().min(2).max(50).required().messages({
        'string.min': '名字至少需要2個字符',
        'string.max': '名字不能超過50個字符',
        'any.required': '名字為必填項目'
    }),
    lastName: Joi.string().min(2).max(50).required().messages({
        'string.min': '姓氏至少需要2個字符',
        'string.max': '姓氏不能超過50個字符',
        'any.required': '姓氏為必填項目'
    }),
    phone: Joi.string().pattern(/^[\+]?[0-9\-\s\(\)]+$/).optional(),
    birthDate: Joi.date().optional(),
    gender: Joi.string().valid('male', 'female', 'other', 'prefer_not_to_say').optional(),
    membershipPlan: Joi.string().valid('basic', 'premium', 'vip').default('basic'),
    interests: Joi.array().items(Joi.string().valid(
        'meditation', 'tibetan_medicine', 'scriptures', 
        'sound_healing', 'mind_body_healing', 'overseas_study',
        'research', 'workshops', 'community'
    )).optional(),
    agreeToTerms: Joi.boolean().valid(true).required().messages({
        'any.only': '必須同意服務條款和隱私政策'
    }),
    agreeToNewsletter: Joi.boolean().optional()
});

const loginSchema = Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().required(),
    rememberMe: Joi.boolean().optional()
});

const forgotPasswordSchema = Joi.object({
    email: Joi.string().email().required()
});

const resetPasswordSchema = Joi.object({
    token: Joi.string().required(),
    password: Joi.string()
        .min(8)
        .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .required()
});

// 登入速率限制
const loginLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分鐘
    max: 5,
    message: {
        error: 'Too many login attempts',
        message: 'Please try again later'
    },
    standardHeaders: true,
    legacyHeaders: false
});

// 註冊用戶
router.post('/register', async (req, res) => {
    try {
        // 驗證輸入
        const { error, value } = registerSchema.validate(req.body);
        if (error) {
            return res.status(400).json({
                error: 'Validation Error',
                message: error.details[0].message,
                details: error.details
            });
        }

        const {
            email,
            password,
            firstName,
            lastName,
            phone,
            birthDate,
            gender,
            membershipPlan,
            interests,
            agreeToNewsletter
        } = value;

        // 檢查用戶是否已存在
        const existingUser = await User.findByEmail(email);
        if (existingUser) {
            return res.status(409).json({
                error: 'User Already Exists',
                message: '此電子郵件已被註冊'
            });
        }

        // 創建新用戶
        const user = new User({
            email,
            password,
            profile: {
                firstName,
                lastName,
                phone,
                birthDate,
                gender
            },
            membership: {
                plan: membershipPlan,
                status: 'active'
            },
            preferences: {
                interests: interests || [],
                notifications: {
                    email: {
                        newsletter: agreeToNewsletter || false,
                        courseUpdates: true,
                        workshopReminders: true,
                        promotions: agreeToNewsletter || false
                    }
                }
            },
            status: 'pending' // 需要郵件驗證
        });

        // 生成郵件驗證 token
        const verificationToken = user.createEmailVerificationToken();
        await user.save();

        // 發送驗證郵件
        try {
            const verificationUrl = `${req.protocol}://${req.get('host')}/api/auth/verify-email/${verificationToken}`;
            
            await sendEmail({
                to: user.email,
                subject: '歡迎加入佛教身心療癒社群 - 請驗證您的郵件',
                template: 'welcome',
                data: {
                    name: user.fullName,
                    verificationUrl,
                    membershipPlan: user.membership.plan
                }
            });
        } catch (emailError) {
            console.error('Email sending failed:', emailError);
            // 不因為郵件發送失敗而影響註冊
        }

        // 生成 JWT tokens
        const token = user.generateAuthToken();
        const refreshToken = user.generateRefreshToken();

        // 更新最後登入時間
        user.security.lastLogin = new Date();
        await user.save();

        res.status(201).json({
            message: '註冊成功！請檢查您的郵件進行帳戶驗證。',
            user: {
                id: user._id,
                email: user.email,
                fullName: user.fullName,
                membership: user.membership,
                emailVerified: user.security.emailVerified
            },
            tokens: {
                access: token,
                refresh: refreshToken
            }
        });

    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({
            error: 'Registration Failed',
            message: '註冊過程中發生錯誤，請稍後再試'
        });
    }
});

// 用戶登入
router.post('/login', loginLimiter, async (req, res) => {
    try {
        // 驗證輸入
        const { error, value } = loginSchema.validate(req.body);
        if (error) {
            return res.status(400).json({
                error: 'Validation Error',
                message: error.details[0].message
            });
        }

        const { email, password, rememberMe } = value;

        // 查找用戶（包含密碼）
        const user = await User.findOne({ email: email.toLowerCase() }).select('+password');
        
        if (!user) {
            return res.status(401).json({
                error: 'Invalid Credentials',
                message: '郵件或密碼錯誤'
            });
        }

        // 檢查帳戶是否被鎖定
        if (user.isLocked) {
            return res.status(423).json({
                error: 'Account Locked',
                message: '帳戶因多次登入失敗而被暫時鎖定，請稍後再試'
            });
        }

        // 驗證密碼
        const isPasswordValid = await user.comparePassword(password);
        
        if (!isPasswordValid) {
            // 增加登入嘗試次數
            await user.incrementLoginAttempts();
            
            return res.status(401).json({
                error: 'Invalid Credentials',
                message: '郵件或密碼錯誤'
            });
        }

        // 檢查帳戶狀態
        if (user.status === 'suspended') {
            return res.status(403).json({
                error: 'Account Suspended',
                message: '您的帳戶已被暫停，請聯繫客服'
            });
        }

        // 重置登入嘗試次數
        if (user.security.loginAttempts > 0) {
            await user.resetLoginAttempts();
        }

        // 更新最後登入時間
        user.security.lastLogin = new Date();
        user.progress.lastActiveDate = new Date();
        await user.save();

        // 生成 JWT tokens
        const tokenExpiry = rememberMe ? '30d' : '7d';
        const token = jwt.sign(
            {
                id: user._id,
                email: user.email,
                role: user.role,
                membership: user.membership.plan
            },
            process.env.JWT_SECRET,
            { expiresIn: tokenExpiry }
        );
        
        const refreshToken = user.generateRefreshToken();

        res.json({
            message: '登入成功',
            user: {
                id: user._id,
                email: user.email,
                fullName: user.fullName,
                role: user.role,
                membership: user.membership,
                emailVerified: user.security.emailVerified,
                lastLogin: user.security.lastLogin
            },
            tokens: {
                access: token,
                refresh: refreshToken
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            error: 'Login Failed',
            message: '登入過程中發生錯誤，請稍後再試'
        });
    }
});

// 用戶登出
router.post('/logout', auth, async (req, res) => {
    try {
        // 在實際應用中，這裡可以將 token 加入黑名單
        // 或者在 Redis 中標記為無效
        
        res.json({
            message: '登出成功'
        });
    } catch (error) {
        console.error('Logout error:', error);
        res.status(500).json({
            error: 'Logout Failed',
            message: '登出過程中發生錯誤'
        });
    }
});

// 刷新 token
router.post('/refresh', async (req, res) => {
    try {
        const { refreshToken } = req.body;
        
        if (!refreshToken) {
            return res.status(401).json({
                error: 'Refresh Token Required',
                message: '請提供刷新令牌'
            });
        }

        // 驗證刷新 token
        const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);
        
        const user = await User.findById(decoded.id);
        if (!user) {
            return res.status(401).json({
                error: 'Invalid Token',
                message: '無效的刷新令牌'
            });
        }

        // 生成新的 access token
        const newToken = user.generateAuthToken();
        const newRefreshToken = user.generateRefreshToken();

        res.json({
            tokens: {
                access: newToken,
                refresh: newRefreshToken
            }
        });

    } catch (error) {
        console.error('Token refresh error:', error);
        res.status(401).json({
            error: 'Token Refresh Failed',
            message: '令牌刷新失敗，請重新登入'
        });
    }
});

// 忘記密碼
router.post('/forgot-password', async (req, res) => {
    try {
        const { error, value } = forgotPasswordSchema.validate(req.body);
        if (error) {
            return res.status(400).json({
                error: 'Validation Error',
                message: error.details[0].message
            });
        }

        const { email } = value;
        const user = await User.findByEmail(email);

        // 無論用戶是否存在都返回相同訊息（安全考量）
        if (!user) {
            return res.json({
                message: '如果該郵件地址存在於我們的系統中，您將收到重置密碼的郵件'
            });
        }

        // 生成重置密碼 token
        const resetToken = user.createPasswordResetToken();
        await user.save();

        // 發送重置密碼郵件
        try {
            const resetUrl = `${req.protocol}://${req.get('host')}/api/auth/reset-password/${resetToken}`;
            
            await sendEmail({
                to: user.email,
                subject: '重置您的密碼 - 佛教身心療癒網站',
                template: 'passwordReset',
                data: {
                    name: user.fullName,
                    resetUrl,
                    expiresIn: '10分鐘'
                }
            });
        } catch (emailError) {
            console.error('Password reset email failed:', emailError);
            user.security.passwordResetToken = undefined;
            user.security.passwordResetExpires = undefined;
            await user.save();
            
            return res.status(500).json({
                error: 'Email Send Failed',
                message: '發送重置郵件失敗，請稍後再試'
            });
        }

        res.json({
            message: '如果該郵件地址存在於我們的系統中，您將收到重置密碼的郵件'
        });

    } catch (error) {
        console.error('Forgot password error:', error);
        res.status(500).json({
            error: 'Request Failed',
            message: '處理請求時發生錯誤，請稍後再試'
        });
    }
});

// 重置密碼
router.post('/reset-password', async (req, res) => {
    try {
        const { error, value } = resetPasswordSchema.validate(req.body);
        if (error) {
            return res.status(400).json({
                error: 'Validation Error',
                message: error.details[0].message
            });
        }

        const { token, password } = value;

        // 加密 token 進行比對
        const hashedToken = crypto
            .createHash('sha256')
            .update(token)
            .digest('hex');

        // 查找用戶並檢查 token 是否有效
        const user = await User.findOne({
            'security.passwordResetToken': hashedToken,
            'security.passwordResetExpires': { $gt: Date.now() }
        });

        if (!user) {
            return res.status(400).json({
                error: 'Invalid Token',
                message: '重置令牌無效或已過期'
            });
        }

        // 更新密碼
        user.password = password;
        user.security.passwordResetToken = undefined;
        user.security.passwordResetExpires = undefined;
        user.security.loginAttempts = 0;
        user.security.lockUntil = undefined;
        
        await user.save();

        // 發送確認郵件
        try {
            await sendEmail({
                to: user.email,
                subject: '密碼重置成功 - 佛教身心療癒網站',
                template: 'passwordResetConfirm',
                data: {
                    name: user.fullName,
                    resetTime: new Date().toLocaleString('zh-TW')
                }
            });
        } catch (emailError) {
            console.error('Confirmation email failed:', emailError);
        }

        res.json({
            message: '密碼重置成功，請使用新密碼登入'
        });

    } catch (error) {
        console.error('Reset password error:', error);
        res.status(500).json({
            error: 'Reset Failed',
            message: '密碼重置失敗，請稍後再試'
        });
    }
});

// 驗證郵件
router.get('/verify-email/:token', async (req, res) => {
    try {
        const { token } = req.params;

        // 加密 token 進行比對
        const hashedToken = crypto
            .createHash('sha256')
            .update(token)
            .digest('hex');

        const user = await User.findOne({
            'security.emailVerificationToken': hashedToken
        });

        if (!user) {
            return res.status(400).json({
                error: 'Invalid Token',
                message: '驗證令牌無效'
            });
        }

        // 驗證郵件
        user.security.emailVerified = true;
        user.security.emailVerificationToken = undefined;
        user.status = 'active';
        
        await user.save();

        // 發送歡迎郵件
        try {
            await sendEmail({
                to: user.email,
                subject: '歡迎加入佛教身心療癒社群！',
                template: 'emailVerified',
                data: {
                    name: user.fullName,
                    membershipPlan: user.membership.plan,
                    loginUrl: `${req.protocol}://${req.get('host')}/login`
                }
            });
        } catch (emailError) {
            console.error('Welcome email failed:', emailError);
        }

        res.json({
            message: '郵件驗證成功！您的帳戶已啟用。',
            user: {
                id: user._id,
                email: user.email,
                emailVerified: user.security.emailVerified,
                status: user.status
            }
        });

    } catch (error) {
        console.error('Email verification error:', error);
        res.status(500).json({
            error: 'Verification Failed',
            message: '郵件驗證失敗，請稍後再試'
        });
    }
});

// 重新發送驗證郵件
router.post('/resend-verification', auth, async (req, res) => {
    try {
        const user = await User.findById(req.user.id);
        
        if (user.security.emailVerified) {
            return res.status(400).json({
                error: 'Already Verified',
                message: '您的郵件已經過驗證'
            });
        }

        // 生成新的驗證 token
        const verificationToken = user.createEmailVerificationToken();
        await user.save();

        // 發送驗證郵件
        const verificationUrl = `${req.protocol}://${req.get('host')}/api/auth/verify-email/${verificationToken}`;
        
        await sendEmail({
            to: user.email,
            subject: '重新驗證您的郵件 - 佛教身心療癒網站',
            template: 'resendVerification',
            data: {
                name: user.fullName,
                verificationUrl
            }
        });

        res.json({
            message: '驗證郵件已重新發送，請檢查您的郵箱'
        });

    } catch (error) {
        console.error('Resend verification error:', error);
        res.status(500).json({
            error: 'Send Failed',
            message: '發送驗證郵件失敗，請稍後再試'
        });
    }
});

// 獲取當前用戶資訊
router.get('/me', auth, async (req, res) => {
    try {
        const user = await User.findById(req.user.id)
            .populate('progress.coursesEnrolled.courseId', 'title category')
            .populate('progress.coursesCompleted.courseId', 'title category')
            .populate('bookmarks.scriptures.scriptureId', 'title category')
            .populate('workshops.registered.workshopId', 'title date');

        res.json({
            user: user
        });
    } catch (error) {
        console.error('Get user error:', error);
        res.status(500).json({
            error: 'Fetch Failed',
            message: '獲取用戶資訊失敗'
        });
    }
});

module.exports = router;